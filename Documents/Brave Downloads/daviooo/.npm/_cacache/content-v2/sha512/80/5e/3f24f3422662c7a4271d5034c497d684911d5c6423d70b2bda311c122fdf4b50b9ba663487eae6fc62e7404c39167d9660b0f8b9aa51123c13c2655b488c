{"_id": "@sindresorhus/is", "_rev": "65-39cf03dbd9959c13dbeee2344c0b9dea", "name": "@sindresorhus/is", "dist-tags": {"latest": "7.0.1"}, "versions": {"0.1.0": {"name": "@sindresorhus/is", "version": "0.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "d0ff822b2b92e6476b7f52ca0f0289992f47061b", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.1.0.tgz", "integrity": "sha512-lMZXffjDR0kW2SSHJerK+e4r9g0sacKX2zswyDWy6Oi/Ejy8IayLyRyUl3ojPEXoDEwYnhaziRT5m6DkATWLAQ==", "signatures": [{"sig": "MEUCIDZAPkHhUwG0JYIciDN9RkBrMreUIFgMo/hqtResQ5wcAiEA0HBs78GqrJKJfKnfydz8hIrkz6UpBqf0G3k4xkxFWtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "d0ff822b2b92e6476b7f52ca0f0289992f47061b", "engines": {"node": ">=4"}, "gitHead": "103c5afe6f20e68f64f448bbe4047862e3d565e5", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "4.8.3", "publishConfig": {"access": "public"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.1.0.tgz_1506016469568_0.5930651263333857", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@sindresorhus/is", "version": "0.2.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "c7f5e01c8d3372c682e888f73fd72992514c13c9", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.2.0.tgz", "integrity": "sha512-3SegjLjiwJUA9kAhfbJmIpE7beMxjdD8gFoiovYYpjsqfvpGPViKwL9haJXG7FmWZxN0xWVd0bDB9quUogQBPQ==", "signatures": [{"sig": "MEYCIQCCcpnvnnm0eislq4UKNFp/hNjOeuzWEgToU59SDrcdcwIhAJ4VqqTh/IeSJXMrHFa+Pk/dNacwbWVPCV+lT7SakNCn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "c7f5e01c8d3372c682e888f73fd72992514c13c9", "engines": {"node": ">=4"}, "gitHead": "27d15f40bda9e390d1dcac5722e37a2675425483", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "6.11.0", "publishConfig": {"access": "public"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.2.0.tgz_1506655378448_0.7947612015996128", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@sindresorhus/is", "version": "0.2.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "537fe5655c9e6b22313776494abc712f0b327e79", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.2.1.tgz", "integrity": "sha512-uHUsXrbsLFa/qf9JC+961fOyxVLncCrvZ70uI5ZdDVidnkFatnZJoLcyLaAx4A2WrEgRFJckn6yiykZK4RNpdw==", "signatures": [{"sig": "MEYCIQCObkTu7v+A7BRkFZbZKAevG4nKZNgVXSyceiTnxhGF3AIhAIDyZhTF5NA//KdnUVorn8YgKTbQ7rPBT4Nbs/X57Btv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "537fe5655c9e6b22313776494abc712f0b327e79", "engines": {"node": ">=4"}, "gitHead": "11b98171c82991416c3ff82f076d963b0c775184", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "4.8.3", "publishConfig": {"access": "public"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.2.1.tgz_1507177308076_0.29263611719943583", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@sindresorhus/is", "version": "0.3.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "ae248b274cb9eec2d8c7acdf6a70689126ff7237", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.3.0.tgz", "integrity": "sha512-oITNea0S2WJA4WCkoiQxjZKPEKkdE/9xYcGxkMu4PqVxaU91541HdudB25Z8im58jJ3z7yOx1WQQG2jWIzZ5xQ==", "signatures": [{"sig": "MEUCIQD2p39SEg3/k24V5F3AAil8R2z5BQIOd75HwmmOnbhXaQIgYRDMSUooN75AR/y+0V1mjeFYjtd4HWlZ5A0x6bUNi6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "ae248b274cb9eec2d8c7acdf6a70689126ff7237", "engines": {"node": ">=4"}, "gitHead": "04cb80dfb170d9c2e004f9ba0825fd0bab9411fe", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "4.8.3", "publishConfig": {"access": "public"}, "devDependencies": {"xo": "*", "ava": "*", "jsdom": "^9.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.3.0.tgz_1507715474871_0.08435121271759272", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@sindresorhus/is", "version": "0.4.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "45bfece11eb55cac415e65b8de352eb75de1b968", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.4.0.tgz", "integrity": "sha512-5WG0MPK5JOCf16GDyP26gmTYI2pMU5eBbiN0bUTb5GzEgBYT7Dj7S7Jjl6dn/pgAXegmACqjD009v5tVFX200g==", "signatures": [{"sig": "MEYCIQDg+hNsR9E/NlyGeABrr5SxYqNFxE+VWviv2bi9oPb+6AIhAOhVzvTOFu03YcMSrQpIW+InD43tNDzop1uV9nsSJbQ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "a4eebe4b4a95eeb7c6a476d89e92d2af0daf2f2b", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.7.0", "publishConfig": {"access": "public"}, "devDependencies": {"xo": "*", "ava": "*", "jsdom": "^9.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.4.0.tgz_1508614109577_0.9955992680042982", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@sindresorhus/is", "version": "0.5.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "5398b9c4f0963c170d0c8d5873709999f82f508c", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.5.0.tgz", "integrity": "sha512-6cNJTqnikB7E8BV8kYgoYCAwMBh3QSCUte3fq56ldWASPxrGteIDJR5kJ+BJ+AaTRTzI073VUFMHlWrhACn+Jg==", "signatures": [{"sig": "MEUCIBaHI37lMwN3Fk5xGTiUUvhw6V7Wzv8QpWKGV9YsPfntAiEA6HVjJxjCd20zFZEfK4qZAJbP9MBcItWmhNDd012ojT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "files": ["dist"], "types": "dist/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "e8ac7dc4aaf8d1f9a9b07ae0b0e50e45fb24fbba", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.9.0", "publishConfig": {"access": "public"}, "devDependencies": {"ava": "*", "jsdom": "^9.12.0", "tslint": "^5.8.0", "del-cli": "^1.1.0", "tslint-xo": "^0.3.0", "typescript": "^2.6.1", "@types/node": "^8.0.47", "@types/jsdom": "^2.0.31"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.5.0.tgz_1510025031986_0.32947274576872587", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "@sindresorhus/is", "version": "0.6.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.6.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "383f456b26bc96c7889f0332079f4358b16c58dc", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.6.0.tgz", "integrity": "sha512-KLZFhY/4nJM5wDUSXvPfcwXTlFLkyF3vHQIetYpA19GI1lCpUMp6SuNMVCD4YDEOO/XEqIIwRfPHeoq20kWqNg==", "signatures": [{"sig": "MEUCIAbvGz5d6PNZEPM0ol3n+oraDx8TvtibxUPgxRb94V7tAiEA5MsikSDpAMvnhX9Nka19KJ/D5PNInH0XArFV0EXMXVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "types": "dist/index.d.ts", "_shasum": "383f456b26bc96c7889f0332079f4358b16c58dc", "engines": {"node": ">=4"}, "gitHead": "50e3fe88c722133953118053bb31c6a6f7a39c9a", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "4.8.4", "publishConfig": {"access": "public"}, "devDependencies": {"ava": "*", "jsdom": "^9.12.0", "tempy": "^0.2.1", "tslint": "^5.8.0", "del-cli": "^1.1.0", "tslint-xo": "^0.3.0", "typescript": "^2.6.1", "@types/node": "^8.0.47", "@types/jsdom": "^2.0.31", "@types/tempy": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.6.0.tgz_1511122697769_0.09692995506338775", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "@sindresorhus/is", "version": "0.7.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.7.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "9a06f4f137ee84d7df0460c1fdb1135ffa6c50fd", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.7.0.tgz", "integrity": "sha512-ONhaKPIufzzrlNbqtWFFd+jlnemX6lJAgq9ZeiZtS7I1PIf/la7CW4m83rTXRnVnsMbW2k56pGYu7AUFJD9Pow==", "signatures": [{"sig": "MEUCIFAFHyfvlGGXBenq+HbtEmSzV77prS6WoZq7ET3R9KiwAiEAqStIGhRxM13pt6gdpYXc1G/GQUvmxUPAG5WYvrvXxdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "files": ["dist"], "types": "dist/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "828a5b3088804f32fec824a2fbff0396d4cd13b3", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.9.0", "publishConfig": {"access": "public"}, "devDependencies": {"ava": "*", "jsdom": "^9.12.0", "tempy": "^0.2.1", "tslint": "^5.8.0", "del-cli": "^1.1.0", "tslint-xo": "^0.3.0", "typescript": "^2.6.1", "@types/node": "^8.0.47", "@types/jsdom": "^2.0.31", "@types/tempy": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-0.7.0.tgz_1513024821800_0.9983615970704705", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "@sindresorhus/is", "version": "0.8.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.8.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "073aee40b0aab2d4ace33c0a2a2672a37da6fa12", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.8.0.tgz", "fileCount": 16, "integrity": "sha512-cerdLImrIp8hbt+i2v2rCLCo9fqoRrciP/BzLY3fnmhbwGNDgf7b3Niiqu085+LjPHYxgu0f+3/ciPDPJFgGkA==", "signatures": [{"sig": "MEQCIGow4wbuVV0lGcpyDvYwQk44gZyrGLjv/9HqojwJhlULAiBh6WNypenYg4WITN0pJ7Hn4vNqXfkguiOj24dHaHxzfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97819}, "main": "dist/index.js", "files": ["dist"], "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "7ae4b44ca281da26703793c52be20c4982a352f9", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "*", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.7.0", "typescript": "^2.8.1", "@types/node": "^9.6.0", "@types/jsdom": "^11.0.4", "@types/tempy": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.8.0_1522775413420_0.5357373780876304", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "@sindresorhus/is", "version": "0.9.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.9.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "1a65a8fab2d4efa5a32a5f5f40cf9b0806aabd69", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.9.0.tgz", "fileCount": 16, "integrity": "sha512-gKDK3XG96qMB3ZEZu9/Qvv6T2x2ZFGn4dcftsItHRF74kA6AqC9DIcOfnzgZSIWsSIHA47soiSNgAEHFxBQ7Fw==", "signatures": [{"sig": "MEYCIQCUvxcbL4bVMiSyEieJaXHjxhZOOtTXCY/N0brYgJsGuQIhALG2uh+fkguq8GTb+q4mZdbowrVKhONDA1DHtNZ4o43l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6/AlCRA9TVsSAnZWagAArvwQAIvjuvOCI6vkggp1oHgf\nw7qYU1h8hrRAbb4Zv4h/yPzDxIAkUxu9+ZampEW/UcURUHSi455wolXFaAIj\nHiX1Bs/ke+RyuOZkF73xV5oxBnyZUot1vVhSRpeWp6wkmDH601ZClqwkg6+N\npfDvGI2a0djIhHNESUvdUIuaO8zN/1dV/2wiT8wfObb7XQ2TgTIU2lfSfp/A\nrecVaQGhK7QuXCNdOiFpTyg/E2zhrrVD4hHSJjAlghvM6Sbdtb0NxiEY47Up\nDQo1/QK1YwyugviYsmNvehC2NNth5y3HQCicz8zI8OrMlQYZtL+4r4XHGbiC\nixlOWYD89DCPqZyAZxCbHnogfHySh0ieizSFqxSf9yPTiASAQrDZasdE85U6\nYsAJ5XUYUVyASkq963wFZ02uOeRSjGoBHRVZT5CzLFEwfE5Ar3ymWoL1xbYV\n5oDPpBIJQIhpndo2g6QDJOAxux4On78plen3s3bD0EkFNv3afSGANjNB1B37\nz4/aRIpq27OLR9aTPVJBJRgo/+tZOzdQbyo9np1Vfu9ylYCw18yCAFSsr4fK\nwiWrgmMgi9xIfR908fl+Wi6MVjyAEyJ3+bnyxOdipsiFttgsbEnWBakhfkiS\nvoBHG0O83YMd+laozxQq9b874srrzuCTI2/npNbGn9aACa8R6viZliJXuY48\n51bS\r\n=zuUV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "37f6cc3fe3df89e3b77aa9bb2cb87957fa9759ce", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"symbol-observable": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "*", "rxjs": "^6.0.0", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.7.0", "typescript": "^2.8.1", "@types/node": "^9.6.0", "@types/jsdom": "^11.0.4", "@types/tempy": "^0.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.5.3"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.9.0_1525411875538_0.19847575916850846", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "@sindresorhus/is", "version": "0.10.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.10.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "f42dd6a9d12cd79fa6f53b27cf5bea3a30d2cafa", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.10.0.tgz", "fileCount": 9, "integrity": "sha512-bmfpY7xAyYhgE531LyMH40uVWgBymnUbL6scPM3pogT/v9CDDnS8N5LRlwLiBGrvjYhBSylIz9P5mIqAMcoKIg==", "signatures": [{"sig": "MEUCIGkqiItppQg+Un1z/I6G348uue9OLVh3nfXsHeodT2LiAiEAjKjD4WUSt3pGy9qjYYVAoRpgdgJMjA7ho5rgi7Envks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76978, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPkhkCRA9TVsSAnZWagAAPiAP/RnaNJXDw+ADVwEBTgaq\nutON1yYcR9AyvJU7x2pDRIQsbRsGu5jjmj+0Fw4++UJLRgV5CX386w4OW3uM\n9H5Dsk7dULAR/dge3fK3HsWWdYT1vI71/MlUteCvEfrxeTAIheDRt0lhbeD3\nSJrSCjT/DzBArG8Zvyfr3lyX0XOx4REjHf0wwpuOq8yNVawSj2qaz1uSV2nV\noDGXxUBxsI5cu3BU0KJFoDJvOP7JvTOrOfZcBK1PXGXVFacyVPx99jhZYhSP\nQ80eQYhLJxpyg28NhtUfmBaz9WsNTJVgsaEtNQiYPkMXt6kk93dR7sqLtt3i\nYVcNy/C3uhzi/aMZn1ql7w6AbI8h0fGfjy77ixvXe1fvgDVnmLiMYQlB0d0m\nqvBlQk2X8bVns+ZNR7Fryh8iUDhrp1NpNa9b1vdIEshKz2FVbT/DYelxP+O+\ngeoS1Uma6P4tPMLWxIgYWdINupLezAUhbQw42vhdEYfT8AF/ukNOZAUS3H8n\nNed5aMgVunSWVo8cC1rEIjNtoPiSTaTNmLfn/iGLRshWHILsR1Nl5/oNvKlQ\ncOSeylVZ3yeNZgj743nMCKp+7n5r4nUgGF+BFxs/tZkghMDCQPWrd2zsX+//\n3YSAe1WQA3XECk/NcbHz0hZtOlOJRunZ2DWjL7cbaPipvRN2DLZ8dN3qOnRg\n7J6X\r\n=x1u6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "d11b7eaaa1ca6a8565196a8bf07284b6435f4e50", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "del dist && tsc", "prepublish": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"symbol-observable": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "*", "rxjs": "^6.0.0", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.8.0", "typescript": "^2.9.0", "@types/node": "^10.1.2", "@types/jsdom": "^11.0.4", "@types/tempy": "^0.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.5.3"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.10.0_1530808420839_0.8008182477174501", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "@sindresorhus/is", "version": "0.11.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.11.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "a65970040a5b55c4713452666703b92a6c331fdb", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.11.0.tgz", "fileCount": 6, "integrity": "sha512-i5Zh5+3QARyXtzq3Bd2Lg3aBRFLcgYbNoap3Hyu1uRN8X+D+0JMqqc4uk4XjhNxfVdCnL8WHUA2wxa33QCC50w==", "signatures": [{"sig": "MEQCIANYDjG5LQsgfNFqE6n2pp6HB03OThIOH6DJxwDabW4hAiBKN/dLkLnEfizyvrtwuAMY3xbSF3Pb3oYhgjBaAoeljA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34554, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRILiCRA9TVsSAnZWagAAy7AP/0otmENeW91LkN7PVGSH\nWigfCccjedy71SunvmQGdoCgrdCcGPwRAsNWhPZmq8LP8A+VYsaRG3f7GiP7\nDGt+4boxanEfvFdVrfpHf0YvH7Efp6FG94zYM8CGoBnXKK5aH+IDJMUtblpH\nzd2qFpgQERF3aVaoNBFAORmQCjjOhBMwiNtex3JDdPCjwWsYl94hmjq25L3O\nFlqQaDnKhjK0OXyVxk8BMzePXa2GrNZJ1ahFgaCD5dfBCykisuWfU4rYlzOp\nTMcBSfb0pyOOjSxhO/InbU9oGNBMv6RtkCmXPJEUurtsfh97nyuHIrJbkc+F\niOzjN0u3GJ1Oa39dKqkvoFsSyyH8fUqTNiwptLogBGJcvzQI5quwg/fxhLHy\nXSiODlm2myXuHgOnVdik6lxEJjh571/s5xX0CAsSXPyIV11s0AhWvdTpVZww\nsKRWqUA850AD2fQepbeRpB5cSXT/sIty/QB9I2RmGbieRU72MjTTk4eewdzJ\nIQbwIchO6cdWN4nvyV347o9hvE38x+/zRcOdSwDe4iROHlmpLlPj1XPF5U/C\nU33Z/95ofY1b1c9rJUud7EQhLq8swWosOtmlvmt2v40RNBn2Nms4bUpfftBP\ni9mBVelKx5twcY+RAiC7YeeHhw8FMXXxbftaypOx+FHSWh1NO7bMF6aaobAa\nEhq4\r\n=CbhG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "b2bb3e7d3717de9734a3881156b1f8ab00236fe9", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "del dist && tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"symbol-observable": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "*", "rxjs": "^6.0.0", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.8.0", "typescript": "^2.9.0", "@types/node": "^10.1.2", "@types/jsdom": "^11.0.4", "@types/tempy": "^0.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.5.3"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.11.0_1531216610235_0.32137062124715365", "host": "s3://npm-registry-packages"}}, "0.12.0": {"name": "@sindresorhus/is", "version": "0.12.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.12.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "55c37409c809e802efea25911a579731adfc6e07", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.12.0.tgz", "fileCount": 6, "integrity": "sha512-9ve22cGrAKlSRvi8Vb2JIjzcaaQg79531yQHnF+hi/kOpsSj3Om8AyR1wcHrgl0u7U3vYQ7gmF5erZzOp4+51Q==", "signatures": [{"sig": "MEUCIBaamqLpvj8U6XpCbxzAUxbWNuCc3ydqRo2tdtmEQbULAiEAh0s+POmFYMic14Kt9gOiwgWkjxw09Z+yofe+/dxVMp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrcvfCRA9TVsSAnZWagAAXaIP/jYq6WPORGAA18RDopAy\nORFWepA0b6V2844VSeOysjp+Htd03yf0vePQEtYaaCQH77DEwM5/LaLmV2IG\n8Hse8iUC6JwdrGxih8SzhQXRCkpOiPOvY5iG8dPZIJxWW2fnKHcrpoGs7emP\n8kwTnOM/8S1jTApC+87m12jFGGUjrAEhQ6el+SnUa1X9HLMVPQ/E9JtNczLY\negr/mNJF8uuA0dPE1+VmyfL2ilOriEPRGeNoGm8BR1ZcoDLHz+TTkF/Q/3SP\nUSf/itI0BdZOQcKXMqrrSWLVlX6uUaon3hBIt6E+pFKlWqAHu7yauAwM0Qjz\nB2/M8RmViUddxZGkQp9myVeLdG0rTgMirSH/48n2XUOJ79pJF7qseefGe/64\nFdBnQRuFTj1LVy6ldz4rGGzLsYyLQXl76wmOsMu3QhVqKjo3rCju4dJ4K2vv\n6xBIklZy03Bl19CrlxTJJSJZZ6Lgmfhq+1wMKbecWZ5aF21T6lBWQyqIomtd\nDApZo8kNRwVQG5Ncvdr/TuDUM5aTKZWwg3QbqJfCM/oPPAB42H1mUOsulP+h\nkJ3GWMIp9kM1UuMWXp/8+EEoFnCYZx6LiqSddygOSMDTs87aACj8AzjISOIO\nQaqddA+KzkEHdw1E3TDm8kvoeKfwdGmCJFakbvx3WtCR/66Enci+m47DVsZ2\nVkfc\r\n=/50i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "c983ffa4cd83c114b18d021c180e6ba9265070cd", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "del dist && tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"symbol-observable": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "*", "rxjs": "^6.3.3", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.9.0", "typescript": "^3.1.1", "@types/node": "^10.11.2", "@types/jsdom": "^11.12.0", "@types/tempy": "^0.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.12.0_1538116574508_0.9398347559081299", "host": "s3://npm-registry-packages"}}, "0.13.0": {"name": "@sindresorhus/is", "version": "0.13.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.13.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "a85d06c13658d0cc9584537df4a99423461f114b", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.13.0.tgz", "fileCount": 6, "integrity": "sha512-xTPFzGRnYKVMQlcrVRhO7XIXX5t5hmBC+KpEnlhvjbN1cg5k4qDEVPwzkqhC7jGzmhAY0F9vouWHCt4S/kyeNg==", "signatures": [{"sig": "MEQCIBjBMU95GuKF1q0WpJvxPKBg7UqY2TrvI0Nlxq2Lsr7gAiBqQ/eAnKs0/X1Yf/SLa+ROFxbfHnAQMCuTBfNArnYY+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3D7BCRA9TVsSAnZWagAAIKwP/1G5pOMuVjiVxlcgxLHU\n1us9rqSAOjnJnn1Mb6psRNGo+KiUGh2Tv1y0V5n8MkiOxwqrb4SNVT1QEx2Z\nt1ZoHqk4wL/xX0t26DyVzJnGVP8oKQeGTHiWIhMVpCaGe1FX4oLeYUehCO2K\nsI5vrG1eNGmSOb1KhA+vjYnnM4B+GDvEtMlRvM6xRO3xXRRB5bychqlmjMvI\nrRVwq6O/wZ0PtOkVPm5dbxEeDNVOxVOJc2bKbFbn/dp3B0dCgggmuRTmvyO8\nzWL9J8RLIGbbjs/mXbf9l2N9RW41e41fWvWVdygF9QfThY7Q4XdOKjP5/ByX\nfwmIbGNDLPLm+Y4jZLMAJBji6/miGJCCuf6fczmbRFSlUraWN+929nTDXY55\nx+Ne+vv0iuB0pjxlwh9hTe0oGDR0ZUu9rounA0PgeIHI+TTrCH3ApA6BSc/2\nwd6vnzf6Qgl2n0BbgBsWM0t5DajPUfoDFOI7Pk8unLhmKSTN5CqLe8k9og7g\ntFvBqWzHJPUpdD5Qvpsdlmyapx6B1FjJ/l03pSCYAN82wGnvIde5ZJ4tQaGm\nLmHRsfmo5XO1eo4Yb5q4DwGSI0cIXTbg6n+Bf217psfkCq/ScCN1N6Bdb3v+\n+BIbkQgyM8KmJQb+n83uIHJcqp5XeXyNvL1ItJbXBHY++8pNGezA85LCFREk\ny1kZ\r\n=P4u4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "2232eeea79b3d2a26fb5b0af60e042ff2171cdc3", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "del dist && tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"symbol-observable": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"ava": "*", "rxjs": "^6.3.3", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.9.0", "typescript": "^3.1.4", "@types/node": "^10.11.2", "@types/jsdom": "^11.12.0", "@types/tempy": "^0.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.13.0_1541160640760_0.8677253184249949", "host": "s3://npm-registry-packages"}}, "0.14.0": {"name": "@sindresorhus/is", "version": "0.14.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.14.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "dist": {"shasum": "9fb3a3cf3132328151f353de4632e01e52102bea", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.14.0.tgz", "fileCount": 6, "integrity": "sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==", "signatures": [{"sig": "MEUCIHIiMIw8pgMG0mgiz0jxF0t9LzgQN7cGRyGbxMW3Sr8yAiEAvsR+f2mQYboqpXtanBUVPI1wd4D4hCmT50ubfPrWRqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEoIJCRA9TVsSAnZWagAAU08QAKRmmjyTcJv4iAVu+wry\n2CzzAwXJWrHc93Jw/6+u0TBj54yGGGblVjDyL9zh8oDte+ea3h7q9boPKgFb\nq5e2uTUcqreN3ACUae3RPfTMysurTujN6Bd+iet8CRcAmC8n8Isdano2UB5Q\nEvgP1BJgRIgIrHKCr+OwLNLCfN7K5XQPuZeymMaU1BhzIoz8e0vRRnyrQSsC\nIb5NnhpVi3GsLqwlr7yGbTvNZ28mTm3wzDSivpNbZ/ceEecNUdFc/ioRhzTn\nusAzAD2aIjp/WSVuokPgCQYz5fzFj19clZfuvoURD/uS+EOQQaWPQ0xESHx2\nN5qENeQo1u1m1Snq5l5AVxpz5GDiE8DQKxIV1iR6Nh+rg18hVEu8Y8Q9DPgS\n91D+4ue1cTql2mKFtRS6SilDmvmmKhAoCROJw3wvv1Pef4Q9Ke9aqkW53ay7\nE0xfbWXtdu491hdOfYglED4F79BQi5ExUWQRKrVWM4cXs6QE87cwsBd9+p5c\nV99QHu4ZgOJoV5EOJyB2sOf/U0emh4rTV1So0Dz8HLhRQ4JbBNwZmVD4CSAX\nqQo1WG3DntAYTnk44nHkiwmVM4tlL3U6kyN7hxMeWrpA5qSIhokc2uYZNOxE\n7c+gmo7WteSsKKYekDdoFYVTL+c9dsszU9OWGdr6f7vsdTKkbVp913BN7v0S\ncjHv\r\n=KAWq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=6"}, "gitHead": "6cb1d1e91073ada3d0e298e2fef361f0d418b668", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && npm run build && ava dist/tests", "build": "del dist && tsc", "prepublish": "npm run build && del dist/tests"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "10.13.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "^0.25.0", "rxjs": "^6.3.3", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "tslint-xo": "^0.10.0", "typescript": "^3.2.1", "@types/node": "^10.12.10", "@types/jsdom": "^11.12.0", "@types/tempy": "^0.2.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.14.0_1544716808201_0.7722326585650734", "host": "s3://npm-registry-packages"}}, "0.15.0": {"name": "@sindresorhus/is", "version": "0.15.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.15.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "96915baa05e6a6a1d137badf4984d3fc05820bb6", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.15.0.tgz", "fileCount": 6, "integrity": "sha512-lu8BpxjAtRCAo5ifytTpCPCj99LF7o/2Myn+NXyNCBqvPYn7Pjd76AMmUB5l7XF1U6t0hcWrlEM5ESufW7wAeA==", "signatures": [{"sig": "MEUCIEPqGWeUukiuJJmnJp9iTKYCbycMvCT3xscRlXPKGy25AiEA80tsVKf8UnnqdvhZysMHt9ZsBuwt0Ft9D7uZ5kqu61g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcV0dHCRA9TVsSAnZWagAA+pkP/3xauLO+o5kszilNubYp\nn6DDX+xALtVOGCQQDraOal4dHJc5nDDreN0Xm7JlYeFMlLNgH7fo6blNbPDR\nsGum0Z6170PCAm96+XaOt062iFG4MWYqYW+wJ1sd3gKWi5zPDvRaNRY5txFK\nNTeExmlzedROxo2fiqD67ly+hffjRGEAGTTxQgBZ1PBzR8Xi6a5siIvZ/uXR\nlnYLMbASotzDODzNPS9mNBozHUM4o5/IpJzHLVtYoqdwLnf77/w8Ysj+EZK5\n74g+8D/qVH1uC2wp6UUcb8L8Gf4fct++gmkWsQyTyggXZ9oURvl98ZRKWhQ3\nIF6Np2MpiToqYYwiK8MIpJy5aL6w6b29iCdz8kX8j5arYnA3rBnNKTM2o47+\nBp0zc6vrQDLtA0uBFfU/mGEPYn+3scn+68FLgD4uygjzB4FhT+yI5BY0tc5t\nnxHs2lZ6mEo16xk/4wfR3DKIA6p0PFS4GNVsvB0536emGKTDLFzKb0n4kLDW\nM6qqZCwEcDXCQV4A9K1J6BMy75qpiY1DVLp473brQXqGw2O45xiK30OpGzPR\nNh/j/Dsms1x9MNKwReaMtAnDypQ/QGS932DOjc51eyBkAkzonbNZ4aINK5+0\noctOTX6VjwijgFWrtqK2nqAe0CxEoN+XOgEnodTILOiQTx7E+TbCg2FPIvil\ns4AT\r\n=ThGX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=6"}, "gitHead": "3ec41686f710fe1e5291b5ca172b5518f488168c", "scripts": {"lint": "tslint --format stylish --project .", "test": "npm run lint && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Type check values: `is.string('🦄') //=> true`", "directories": {}, "_nodeVersion": "10.15.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "^1.2.0", "rxjs": "^6.4.0", "jsdom": "^11.6.2", "tempy": "^0.2.1", "tslint": "^5.9.1", "del-cli": "^1.1.0", "ts-node": "^8.0.2", "tslint-xo": "^0.10.0", "typescript": "^3.3.1", "@types/node": "^10.12.21", "@types/jsdom": "^11.12.0", "@types/tempy": "^0.2.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.15.0_1549223751257_0.5657524336620294", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@sindresorhus/is", "version": "0.16.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.16.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"]}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "355a7801472ad3810365c1e8aecabcaf45227c54", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.16.0.tgz", "fileCount": 6, "integrity": "sha512-QjJQRRsPoIBbvI0IFiY1NehCRgyw7BNobGVO9FSuMoX7OocwUMbsjXYFneoYT8XJbiXAEJvPvrj9eEb4ZMa84A==", "signatures": [{"sig": "MEQCIG9Py0R+aVot0cdiCxYjbGFpgxDPhVVGYEcxQbY66rIQAiAvfuuP/9m04SCydM5AtmlfLpbNkNbtv7HeDNJglII93Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczVgYCRA9TVsSAnZWagAAz3cP/izXCY4XZya75nw4ALCc\niYvyTVl8y8uKmS2ZtHcd3AwiiWyUXyZUoOYP9h8Ijg6hs5hGhHeDEsRyR3KD\niRO1sF29dBBM1oPF3yOwNvud8QTtdZm2+GvjjP5/AQj7xoyQbMmnvnD/1zek\n6RzKJ++Sh6TZ40UZHwZ+CwAFJcZUYfIt2HUGZIyx36uwQ2kz/9YFm47EIXaL\nmX707MmWjuWnkuWIGcHqgnONeggAzPjS1T+AuHZfzSesz6YB361BZAZ5KYxb\nuP/knc1iOnxxXDjHODrdw3DzZyOpI2zBevR1i6qmEW1VTqF7AcmE0rPiIiHX\ns85lmJJd4DqCWF4ItN9ro+SRctJR3ElxIOJUrZlkdtwT5TmY/vOsaH5rsHSG\nMhfCU0OGAn/iLUo252QpbobuWP6XDmM2g9FbbSccQRsL79/bb5Zb2he1GyE7\n6yscPtIqbjIiC4MNu5yQIu1ib82BCrtfSsFP6SwXjk0yIm/4O2kzRZwxWxLZ\nCSy9PjveKD3KaZS89AvQ/1brKZS6z7uUIHjZ790jZmCX6M2uZWoJIZ0w/SCK\n4BSqBp5ooTCEkvsb5nSiwdlJk4vp5TF8FqrQtjXKmd/8g8T+/BuHeBqrc5kC\nNw/XyG2HNWAaVScWQe5zTz34UkE2M7mTxRNi0SsFrKsY1Fz9tLkJZjIJtuld\nGvO8\r\n=56dh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=8"}, "gitHead": "9bc8307770453ef3d71dfcdf3ff2d2d28cc03b73", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "rxjs": "^6.4.0", "jsdom": "^15.0.0", "tempy": "^0.3.0", "del-cli": "^1.1.0", "ts-node": "^8.0.3", "typescript": "^3.4.1", "@types/node": "^12.0.0", "@types/jsdom": "^12.2.3", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.3.0", "eslint-config-xo-typescript": "^0.10.1", "@typescript-eslint/eslint-plugin": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.16.0_1556961303599_0.10001875159508633", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@sindresorhus/is", "version": "0.17.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.17.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"]}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "37d16c3df3a8b4dcc930210ff68ff3ace35f5ab4", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.17.0.tgz", "fileCount": 6, "integrity": "sha512-hGqdhem3E9L+BBPpkRZQ9RtSg+MpbGfoDXNxuPFgJtM7/CiZHU+Jit0DywctJOsWV0jAPp9RpO8QB7GSaVZq8A==", "signatures": [{"sig": "MEYCIQC8XPoxerkvfhb1sKK3F4G8cvIW/UNVyOOVtMm7BAuQZwIhAI69uF5A/2dJfTCGex4H+8uY0yeCWhP5fs4rg5VjlZDH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2O3QCRA9TVsSAnZWagAA9HwP/20xvQxkqOUlW/1eCTfJ\nm1w04ETjPzPgQmklFx2ndaAuSvHSkxwVwci0cjiK8zRrQSmKrzFLrhUOfp5v\nDLb17ded//kQ+ZeujdFh9uBHH54bAUkVx/NWiCZWr7cnSZMG9Z1/VPtkTtmM\n6JLimdULX3sFmNVyMN/BRXJXp9zZi/NGxxRzPluvME/fhHci5dcPaX5Pb3dM\npUsrKdnUfGyGJxbqsVqrtY2/BoTZdQoNeODqj16N4P1JLEBrnd5spjfWZ8qy\n/9WAbCMXCy5UfKvNplQA5Po9K2TTwffpDSo6y6UnSi/UESZEiT9qD5LZXFQZ\nTJRHhjpkskl9d+9MfmVprPxNu/H+A3l1bH1OYpM5/do7nPQKJ61KUeBM2Kei\nykyosP/V7nG9Suvbn73ByZBFC2IUum7blb5HXglqc1ukOkPKASffi6JIJ4q3\n5/xols+fCvDFiPe+/1ARJNVdQM59+K12wmfsiLZlWvivXAoTCYzxfpN+uYGY\n+IwZFMB5YJfFdZSL/fY9B0hrvZhKViSaHc4O3odII5Qt4liGfqa7O7BpuY3A\nXUW1yZshEk5967Hb41nfuuC4pbU7LWHpwszf9cuM2e90evXsY72LObrcGv8x\nMqXk79XdQDSWCR8Y2nmi1OJRUXQs5lRnOUq4GCs14tcbPQM/03qjTL1oNY/q\nlyOM\r\n=O2O3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=8"}, "gitHead": "b064473589b3586eafa04e49267c806e848b58d9", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "rxjs": "^6.4.0", "jsdom": "^15.0.0", "tempy": "^0.3.0", "del-cli": "^1.1.0", "ts-node": "^8.0.3", "typescript": "^3.4.1", "@types/node": "^12.0.0", "@types/jsdom": "^12.2.3", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.3.0", "eslint-config-xo-typescript": "^0.10.1", "@typescript-eslint/eslint-plugin": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.17.0_1557720527867_0.6771706230708885", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@sindresorhus/is", "version": "0.17.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@0.17.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"]}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "453b27750f358689c4aa3c9f32d9ace1f0929a79", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-0.17.1.tgz", "fileCount": 6, "integrity": "sha512-kg/maAZD2Z2AHDFp7cY/ACokjUL0e7MaupTtGXkSW2SV4DJQEHdslFUioP0SMccotjwqTdI0b4XH/qZh6CN+kQ==", "signatures": [{"sig": "MEUCIFOzfG4hx1mrlF3OaHg1SGC7Z3dk5yApQKc6C0IXxW9qAiEAnNHNZRIUVEhpn416XRtmvhoGkiUq8gB4C6hik7H5IPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc48mYCRA9TVsSAnZWagAABuQQAJp+fC8CJ8EaSQBI6WED\nKTyL3MYjprJG9CKpQmcRR4GTtL5yR/dp5/DKFj7dOjQCLY+8rhuL5FVKvcMi\nWw3Yk79QAKov51Hbzxmamb2Ldg6z7y0/axaeOhrnjc8o3UvOUmpP13ayeA7t\nUQV1Z3tej4AEExor55i8psO8pHsKXU2EZY3sVIM9Rjik6J1DywZWvVv7flvy\nbNExRgxgWwlxrkUr+iB7YgNg7QAHnXqKUwJOzVwLzXYkSL7MAN+Lsi0+/d4m\naNWknFt5lKf/XfQTtntEc4gNMV+O5RfPg2cbQK0nLjjtgU+ViJv9P6OolG13\nw2cz3Jr/M7vpZt9NC1HJ+tjcmntSXrR3r/iwoYlT5jjcTt6NJLqJIpxSalL2\npBFWyjZo70kdSSqU2UuCbGCnQI6EzFOnt8ylMmZOkuGuULVNL1YLi+ZkmSW+\nFH3x1m535aMAmLLYvgbjEmDPLh6iLnqvOaJErJEgAL0CYnO5PPNpJgs2PF3O\n85l4lmIl95gIVbBGie/P3Rw/xVMNvBIVUTK1FzohOiWbdiy9KXVgfymyuYcC\n8ygX5RMs3uiXQlyDYOJe5UffpEkzT6kVpYY63BB/X0YzmlWA2hDBxPpBMFKt\nWVleL3kM8t1jflGBUvcnwnruiu9LiT2mh3FJBTueRpNimIftoJ0yVwg8EMMM\nnreJ\r\n=GpQW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=8"}, "gitHead": "878d111ae734660d4c0238ef7ec6b779e1b52083", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "rxjs": "^6.4.0", "jsdom": "^15.0.0", "tempy": "^0.3.0", "del-cli": "^1.1.0", "ts-node": "^8.0.3", "typescript": "^3.4.1", "@types/node": "^12.0.0", "@types/jsdom": "^12.2.3", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.3.0", "eslint-config-xo-typescript": "^0.10.1", "@typescript-eslint/eslint-plugin": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_0.17.1_1558432151812_0.2419872417397264", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@sindresorhus/is", "version": "1.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"import/first": "off", "import/newline-after-import": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"]}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "4f475ff3f32b0a309b7faffd33328e93d7953330", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-3rlOB53XCVO7LfjXFx4bCGrZPPjkgYD7pP0E/yo4d57H32aYqD/QNmeXcVnx7CM5SxGScwl2P0b1kCDYZgNWqw==", "signatures": [{"sig": "MEUCIGYbNPKUt3KZA3ZSw9bz8AL9d2L9eeglByi8xTQWFO8NAiEAt/mSdSEq8cYd3bQpPQrQYB6Yexg6bNZ4xpb7+NShx1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGHIQCRA9TVsSAnZWagAAfLMP/jGp6AIw0FfIgG2L8wNi\nze8Q//ZSIRPdGF5VTfeoNRP5fvUL+Y8mA5zAZyRNu1RZwuXWODeyz2+YAB6b\nRJeA6bsrSTf/XNVnA5vGMaTNGFjgiS8okeIUupqw3057fSc8A2zqVG+SN9/O\nDpwa54HDPqcsPasPUANTjqKMkNPtjVIHfYO3qPGvKiK78R8ZXytdeasGHTug\nmRX8lepnc1rPHyiQzRSPXKgctaSfkhXCNADI0nVIMoUylIczHH6I4vudackZ\nD6wLJH1fwNOPnw07hoeN/+W3xpWtTsfryvPkWxMt4/dnAvqUGBs6GysgirOm\n2kS2rBKJefYYrRx471LOJc6e7BNRruWOS+vcX1ROLdfwrIrxw9aQKwAE98wX\nDmjM4z7Kv18HfQYtX6OamCAXLMQQ3zxnZdVrySiCjSbXvusWZpAXZMvnMbCo\niloQfAKSbwADj9XrFTQlGUVdmEuYHgEnux+M+flUt3cVdPHDy5D0TD8JU+PK\n4Ils7LcWAOMQJCUoQ3TkcamnwGYIhi7QaZu9R+T1WbJolfJ8Q1Sd+RJxtzkA\nHP89M7TBjlnBq9PiL6QRr5u0VaLnTrAJLu/+RvlXcyLb4DU24zlGtTQjqHtn\nnOWKB/BmgqMf4T5acT+UdcQ/Wo82gbv0/0eIjlbsxPfKllxMk+I6u1c13wCT\nJ4qb\r\n=acVK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=8"}, "gitHead": "e358e44dd5212b8083082727ce0cada959fb2248", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "rxjs": "^6.4.0", "jsdom": "^15.0.0", "tempy": "^0.3.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "^3.4.1", "@types/node": "^12.0.10", "@types/jsdom": "^12.2.4", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.4.0", "@typescript-eslint/parser": "^1.11.0", "eslint-config-xo-typescript": "^0.15.0", "@typescript-eslint/eslint-plugin": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_1.0.0_1561883151799_0.5558018601739321", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@sindresorhus/is", "version": "1.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"import/first": "off", "import/newline-after-import": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"]}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "2b4e94c79fa9864d0411a0b9a94e4b8d66b22e28", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-6tdrcyVaCv+wo1S2/grH6IBG18ry2lImjU+HUQKAU+GLX3LL959l7OaLUycIPSuQQWTQ9trA0GwrFK7E3PlYeQ==", "signatures": [{"sig": "MEUCIQCpRejuO19c8+1rZl1YmJzucM1Dj8bVsVDzJDDzm4zAGgIgcgqps5RBqHPaRfXQmffJeRvG33Y/F/ifR1c4ETYMM0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlasHCRA9TVsSAnZWagAAg+0P/2wlG5lG9TEplznHGHEu\n2avcm9s8vhTSMhFeaqu3PC52s5w1vlvwhjeWwV6cPCSApl+9OniqW+s6aNFI\nATGvgmzup8mbEF1sWX0mVl3ntgO3G9Ne/iIgTDyCGF3HKeV5d6J4DSGfS+/h\n+kAMzdIBwd93aO8hYoaNHEAv0R5pbOCC/ZL1C+5oevEv/riqhaAfZhLEQenK\n+zhnfLR0ybt8qqd4RwYDXck5Nx0fNE0ulL3sb1jSDKN7AHgu6CCr9s3lyUfj\niE0/u3ah0CVa3dUc8uVUhTcF9DQM3Ab9eiCOmED0EkTgBVeC6Mdo7pqGyonP\nYHgMBydpORrCJyvW9xZ6EmgJG6GHyc6u/52dFI9obOl6SRv01TI1g/4u//mT\nwZgsKl21oVUJi3Efgz4vOf9bLgidPpLyw6yJ6kwecYoU6e7ELdf4Ep6LfoZd\nG6w/ffhwg5M2UYkTcG3mzzPv57+shkdkaId7am7j2mo5E60zPhqJvA/0eioR\nITeQhB5cgmJuRVm2bMzPrURjKzp1DViEVJSpjLEtqjgKPPC5QRDXGTS+OUFq\nZmJ8Rw3kGC9+O6L8I5tDOQtvwHGO1uwQ3fKPDBU8qcpkEFOKLNZR+BOCiZXl\nRUpqWPV0C6/n9YGF03GSH2wiQLhYLUrHxrI/COBlHbNbZ7sqdAdOFhoG4P37\nZgwp\r\n=0N9r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=8"}, "gitHead": "4a63743feb9557cd36ce07c181b22624be8e5560", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "10.16.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "rxjs": "^6.4.0", "jsdom": "^15.0.0", "tempy": "^0.3.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "^3.4.1", "@types/node": "^12.0.10", "@types/jsdom": "^12.2.4", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.4.0", "@typescript-eslint/parser": "^1.11.0", "eslint-config-xo-typescript": "^0.15.0", "@typescript-eslint/eslint-plugin": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_1.1.0_1570089734459_0.16524878961394052", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@sindresorhus/is", "version": "1.2.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"import/first": "off", "import/newline-after-import": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"]}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "63ce3638cb85231f3704164c90a18ef816da3fb7", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-1.2.0.tgz", "fileCount": 6, "integrity": "sha512-mwhXGkRV5dlvQc4EgPDxDxO6WuMBVymGFd1CA+2Y+z5dG9MNspoQ+AWjl/Ld1MnpCL8AKbosZlDVohqcIwuWsw==", "signatures": [{"sig": "MEYCIQCvUGq7kLtjWPa+zj6XSLoth11vs+D0bPP0l8BOIifQRgIhAPx45hEqh6xlvdStxnugl1LTHLJvM6bDtf0BYUt1EjCG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdls04CRA9TVsSAnZWagAAaCcP/2r46+VScIfkViw0PTAh\nTD8JXEtRfBRZY3J7aZ88vpljcHo6BdvKSl0REi9t5lG3BiAyVBcEarBtJTXB\niwLhgWIVmqIziB4BvErlaehjt1l9unVIFbb+DJrvpIPlWMdBfJds0rFGnabo\nejuP/eI9Y52gJH81epUdOfo6FyUhl9ZD690VTaEuby2KenSyKG6VpunIwbBe\ngeCPn+ojWssm6PDuDbAaPN5epgfI1aR2nX/WvFWjTMgIYy91kYS+AfNsq69P\n3qBlaSbzx1TtNwEU6haq/ayRCOFb4tRkdGQrJZjlOh+tl937FdwQxmDgA31Z\nZmXHezROHg4KwenRXp3Ue6hgMdOjxcZjkNHvawTvtBtOLx2NmytkZCgKHWEF\n4ZdfUohhHR1vPfOm4YfSw+j8j4o71nCQye9+MYsdwfyMuM0EdAhpML2YR5Ez\nM2wwQ9Ao1AumxN6qR4L8T8HiaDFwVnOirNqjm9GPnGxb5C+9/MJ6Rt2/L/W0\ndTxmLPcxQ/VhA4UvDITUnXhq+ydbwTRXum/XreUdA7ObEcGzN19drRbl8owo\nPemzYgCEwjA8mpS0Xr5mZbt9313IRWVwnB5UDhlffgotAHW6+xdgtWbJYxhb\nNfkCmbawbTisJLIABZkF710EJWrA0ko8A7BJMzOmH+wH44x73SFFBLmF4zTk\narM5\r\n=Ptl+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=8"}, "gitHead": "e7277de849b27469cb243d1eaaadbeb47e97a886", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "10.16.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "rxjs": "^6.4.0", "jsdom": "^15.0.0", "tempy": "^0.3.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "^3.4.1", "@types/node": "^12.0.10", "@types/jsdom": "^12.2.4", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.4.0", "@typescript-eslint/parser": "^1.11.0", "eslint-config-xo-typescript": "^0.15.0", "@typescript-eslint/eslint-plugin": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_1.2.0_1570164023327_0.16613662715474087", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@sindresorhus/is", "version": "2.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "1a7d0e05905877a61440d4f15520a5f5b4fedfbf", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-bX03FiS5dr3cc01hhymlqDAjO5c5WcRPwY4BrghFFhUWI/3vsVlaq8GiSFsrcA3O07mPedlS6Mnr/whaJ44ubA==", "signatures": [{"sig": "MEYCIQDkyknyWcyNSVVbzch7u+GngJe+oVyhW7QphCnYKlPRoAIhAPUGnSLjX4fKVRzaRBFkGUcDAEU8lFl54SVRBIXITR8O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMcUvCRA9TVsSAnZWagAAPy8P/1h9uh/z6Zjiwby5XiGW\n6ismkmfGs6vMKR65H+1+CZv5SPzPu65hAFEVAvlItJqynf1MfbixALwC1IfN\nRebUEJq+3tMm+jinGCrORtL13Y3KNmEkidQm69/mRMAUFrNyaQLXi5yJkbiF\n/lXMEdLgKWyarVD9eCrTi+6BFVUv7H0qab4EsmAbNY+aF70FI///10D0B7ru\nH6GOnjlzDGyu9PifUipRfx+uXEAd9+59CB/vKSJqLUC532cWv8fBe3Runu81\n3J2zR5RjrAdx/MK0XKR3+4SIKBCP+npZ5sQ93RY56nenyXae8PVmLoIrwTsF\nSKEWU+MGaO6ZLZ3p4JMALpGqAfxHAp0GSIuXvmz5jQLIbu0JhhVH011rDSvS\nw24lutvQBbx0GGXYZgb7fDi3KVUBwrFXSQ1iSnUooDgeYSZh3fYgi7ZAl0u8\nzPR8wdN4B3v1qMaiKQzXXKHWab7Hcef2GSlefeD85E7Hw79EogFDoVwTjaP9\niTrWUgCxoatTkuDYOOJKWkhlsx3nL0f2jtlOWsJu4MbDfTYYoYI+yJ1gqgl3\nFokhviB7YWabIp6dpd9D+uEuBC2IFE70rjDcWYDXv9/3nL9iuqSpJB9GltBa\nUAdKI7VWkNn1zvLc6+oEKvOOqzzBItu2AwTTJrmSjuroadIM2+XoiLS1S3y1\nvqfs\r\n=W2PO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "863e26ad6af358900a00c65d04ecae8021cc8bce", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "13.1.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.3.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "^3.7.5", "@types/node": "^12.12.6", "@types/jsdom": "^12.2.4", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.18.0", "eslint-config-xo-typescript": "^0.24.1", "@typescript-eslint/eslint-plugin": "^2.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_2.0.0_1580320047098_0.9174335221114982", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@sindresorhus/is", "version": "2.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"babel": false, "require": ["ts-node/register"], "extensions": ["ts"], "compileEnhancements": false}, "dist": {"shasum": "6ad4ca610f696098e92954ab431ff83bea0ce13f", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-lXKXfypKo644k4Da4yXkPCrwcvn6SlUW2X2zFbuflKHNjf0w9htru01bo26uMhleMXsDmnZ12eJLdrAZa9MANg==", "signatures": [{"sig": "MEYCIQDdvyb8RkiDpaP0F+GxGfjkSUS2MfsSM4BCHqJ7bp26mAIhALuoFN0UX6ysmKgelhy04sJSwEbXOK+4S5lec1ihtTps", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSj9fCRA9TVsSAnZWagAA/6sP/jCDZR0nT3vpeOTEksDH\nnR3OFoTXEMRR2D/BEpWNvUD9zDDek53v3BzQuER+uVObF3LLKWgegKsJRWrV\nOE/HhTitn1y2FazQh7lYW0XAxPRyiqkC2Dz/GNiMTPwyfCSluI9Fc3z2Upd6\n0oKwhA/gAnN8NUIcsZ2i/48f3XSrFlf5XMgr6zCF1tHpGKqV8zBqGkF9tQls\nKiCR7ckAuQD9cuCjbcWYK6xM1E4fcgJOAiH/+8Z8PxChZAHnLnW4mIPtsvvt\nJaQKLIAJNERJLz1dZTYYi9aUVrHqmTWyK+ABz2JtvXwcBysR/HIzdY13BJKc\nM1SHwsuYnDu6Q0pg/ahUm5hKUvZHfmVts5lQlLg5DeHyFVS3rYf6aB/IMAGl\nIKQJ/EiAJwRCtw58JCSkQQKtIEGrCdkcwM9HIfz/CmvUKXN2pDBT1dIuqC+b\nWUDde+lYoM8V7aAv3AUoMf2lFe26OtuAnAV/us83WFvjhvmt0YF54q4Z5ifl\nmczvNnnV7NrjS9fxN+ZyZv1Uxn6ENNstYSeNz66xF/Cg0T8f1um9zEnL8z/T\n8Q1PFqW6IUXwjvTUbrxFRY2zMCxVQzKu1mgmaIVp1G7ycmx4PnlcI2D6Hc/w\np1uht+1s+owkBIMAvALOQ25kaN9mqnFbeKgRIljai8C+8+KsymxYiNmUJSU+\n1n5w\r\n=Gk4c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "1ff389cabb53a2b921ad9da00759823b57b6d0a3", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "10.18.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.3.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "^3.7.5", "@types/node": "^12.12.6", "@types/jsdom": "^12.2.4", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.18.0", "eslint-config-xo-typescript": "^0.24.1", "@typescript-eslint/eslint-plugin": "^2.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_2.1.0_1581924190696_0.35503752115929754", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@sindresorhus/is", "version": "2.1.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@2.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "ceff6a28a5b4867c2dd4a1ba513de278ccbe8bb1", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-/aPsuoj/1Dw/kzhkgz+ES6TxG0zfTMGLwuK2ZG00k/iJzYHTLCE8mVU8EPqEOp/lmxPoq1C1C9RYToRKb2KEfg==", "signatures": [{"sig": "MEUCIFW7Ebs1oqneDCxir7nPun9zKfT6qjTBBqIMDHJahXeAAiEA0igslVfepeAVsvTcBpSxn31ozr3EXt/M484ZzcrKtHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenCY0CRA9TVsSAnZWagAAmW0P/3VxiFVAUIb/mWGfyf1E\nZ20WItqnrlMDs/1zTRchPWMnW4ah1s7YMMpy6xA154Nq8RPj0XNNTuI1lfw7\n2smlznOiTzw06AoLqhEUdWC2FtxTo7efJOAXzom0Md4Y2GpKgtf2dUh1X/ky\nqdUmMl9ng8FThbBdUQS0F+bFazSXYaMB1lAGMfBvbyrxR9GA3ASeTPQWRxGz\nDOArxWFpmjMZZ9y1K71PgNv45vXBzC++/exBzE45XaNJRMYa+U+w5lNUb05H\ndKrzY3dvbHGhS2pvJXj1PuUvcNm2bTwqxGrF4FH7PTtoZR5IEHBU3slTeKZl\n36Do2IBFChnFh+TYCC8dELSIxfZ/ZZ/QR0jyE5bnE2Mqc982rpnVfnMrR1IG\nqsWpSCCu3aq2jCBB16+oA9VYBmdDJI1cRf346Vs5X/GY2FQDfX0/5ylxkeo/\n+T4/fLAl3XAydXbFa/k5EzvsjnKof7RTC4VzBYHAxWgkUT/Ze2OGYcmBt4GG\njNvTeCITY0bT5Mo9DmIGdHl2femPCto0XRQQvnh2dxQa03IUk3v0jjOKkUTA\n2bh3AoeQ3/houy0s+culgOGowH+Fo0LTKcGtRFU+kg+ZEcHhKZFSnzxLFBUz\nh5L8f4Q4ALmQiSNP1eyjqRybqHvez3lOfuZwy3VLXxSCuNNQ4BK5nSNshdc9\n6tp0\r\n=VZ5P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "fae0096eba33ebae4134ac9d3e23a2e337475cf5", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "10.19.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^13.7.4", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_2.1.1_1587291699974_0.7821388181103495", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@sindresorhus/is", "version": "3.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "78fabc5e295adb6e1ef57eaafe4cc5d7aa35b183", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-kqA5I6Yun7PBHk8WN9BBP1c7FfN2SrD05GuVSEYPqDb4nerv7HqYfgBfMIKmT/EuejURkJKLZuLyGKGs6WEG9w==", "signatures": [{"sig": "MEUCIBxlPv3K8EKrbUUKHexeP8yQms80WgkomueTQFztzmV9AiEAz4EUb+YZgXoT2qHMkOAFLe71fNtvBUrfCZ7SZP+6OGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe97JTCRA9TVsSAnZWagAAKkAP/jX0egXTkadAiSkYPlXg\nYm3ilVJiLBKFhgGl4KZDsGb7sTX7EqSdqMgRStVApmoqwms7+w/NfuAkE8qk\n4az3JhLIIeLR20E0Zxo/6PZRFVctjRYUGor2xTryJx0iVagoaH9DuKHNIv8l\nXIUAjJRmv7vp1D8lQ/Xav6G4vhVGf2ffHGMNKG8edRYbVZaviIGqvmoXzV0B\nU1yziDtlSsDHn1mnzpHmLmlu3WTnsWdRGVcL1QZ/dWcjrZc3DEzvjYqQC1+K\nP/vZcQEAErfYOHLFMKXyiFcAwblzF8b9ZpfXuYMDr5i91REaeeFQCTwLGXt7\nESuXODUnaB5CepPD+0shLPHklrK2BznqFksNbtxaFBgR4cBcRudvxbrbPUDf\nBhWBeozsetSZqL/HZXr1oj59RPEZ7q9iwMmin2t/dmn4EquX1h6uuyp+xyYS\nv+oMthDoffr6hgFJJbCCPTaBIyJuKuZyxI6xSmE7+VmIFbTwDqUJfUMWy2NX\nuXZDOo4x3ejt79mCTO4SBN6uscaKKb4MT2rmgw+iUN1mYqiyr8FHbM3dRYMl\nDfOUXqs8xgFW293t+997O5eInLW7D7yg4jWGh0mlcyqC83nbAPFqJ+C6Hz2m\n+SkGo3+4P3iT8Z48rJGB2pKE1ihfjsyyalSZWT3bsRZNqqhXkUFlU3asgGv3\nPOtZ\r\n=pA7D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "4b35ad5becbd464cb185ee34a4f5371381d70657", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.4.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_3.0.0_1593291346769_0.6130996663030968", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "@sindresorhus/is", "version": "3.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "d8735532635bea69ad39119df5f0f10099bd09dc", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-n4J+zu52VdY43kdi/XdI9DzuMr1Mur8zFL5ZRG2opCans9aiFwkPxHYFEb5Xgy7n1Z4K6WfI4FpqUqsh3E8BPQ==", "signatures": [{"sig": "MEYCIQC46uzLVH4b7T9XPqjefO5YN7PFAX82/m9aV9EU1xYdxAIhAL3DJC9zjB5B/SInWVjAhrwmkp6J0Ivhf7sW0xlatMgS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfG9wECRA9TVsSAnZWagAAfn4P/0H3/fYz33IEHp0COEkZ\nEewYuic5ATB8O1j8TFZv8Vi5jFdXLpQFYU6YX87QrD4x68CYZPuHJk9ZhDlS\nqOpLZYYDiSgJIP86YY916uMtaxc0arjf54ULO8FAU0TLUp2RlmIwWU+IvC0v\n7nioaFLP0OdpXN47KJF/2/OrR3HJor8PwTU/UeOX/9KnCpWcmq2LLggoG52x\n+/7p7unpwMuTy8avyUpf+5soK+sSzYduw4YDqy2tNYxTtsy3eEmCw3jLqBtj\n6TC7iIbiwIXh5IwzHXSyEWvkc8ydKbLXEjlpQZ7cl4qo2TT6PZgbVHvwIdOa\n83rmLmZTBuGp1koeG/UkdQBf/29w1uKv5IN1q5TWieD93RSff4lEPqcSdYl8\nJT0sAyx+AZ7UBSKenSGsu85VHgJXjycFCW1SPYe6oiPdtWjGzQgfoNdwrl9+\niP+zPEo9gAfgDGD/XauHvkjHhVtWEOe6GEbhe9ppAeEuQMAh5PLVDgwvZibv\nZuf6HOHvOfkYElcVNLVOGLkGex6P/i949qZtXKKhXD7y0Tx5ttlhriWt1J1v\npWofFyBNJZvA1MpzGcjJZ6VGXieN9ROeuwOWWVHgl63WVpFkIvqbD8r3cYBe\nJhv5BeGA2ch36xGaujaNyM1SQBvZhDHnrWHi3P+G/KOR0DqaBg5OfojCfDNd\nf9Q2\r\n=tdp5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist", "types": "dist", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "09d31733d3ee82788eb02a6f4ebedf8063bc120f", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_3.1.0_1595661316269_0.046659961713244824", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "@sindresorhus/is", "version": "3.1.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@3.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "6e39e4222add8b362da35720c9dd5d53345d5851", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-tLnujxFtfH7F+i5ghUfgGlJsvyCKvUnSMFMlWybFdX9/DdX8svb4Zwx1gV0gkkVCHXtmPSetoAR3QlKfOld6Tw==", "signatures": [{"sig": "MEUCIQCQULUlICEyebYPXjXSb84YwN6iqvefJrDl5tMxlfZqZwIgIgXX0aXGsfNRA6iT7AWbn3Hm3gykF7phDJm3LIYoHpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOQsICRA9TVsSAnZWagAAX1kP/2PVBFczLX4HylRCzNwb\nBtsyh7Nlrkbf5ZqXENSmUlXvEJ+ZZ9ZQWp3pcQoVbrDW4JgvyfKtB1o5hRVl\nacyBzmUSHQXGhLlxKxflksTPRWJ+FsA9XomOh1XZQQ3qIPJBwAHfxDMEoYzs\nfDhceaw3jWMVCHRqytTtEDFqRCoBeXYNx09TecAVzBp7PWVmHEQCR4RBI3XR\nKe3WL6Ls0NA9jbLyB4AHq4MsbiJ0WlqcqwitkgFKPrSiNWR5drEMtoa+<PERSON><PERSON><PERSON><PERSON>\nPb41cFWwhhHWtBXT0ikCfGuGvpyx68SMY6q8t0K7R11evR+E/pIo3DRHIJ1X\nOu89bCEkb/31+ZBQLGNpnoUfemkuChUoX1Kx3KN9kMB8AeQ7OcXTx/tjP/gq\nz2z/O5r6obabIND/knwf6EK3VLBUHDL874SeF6X7B0PcDe7NL1XY/hbzUQZX\n6zyhYIgahwvaj3WJf7E2wTYiDXQElzK5NStxnQm2e8aqqdfMoB9Iinh1LyKT\nWBctit9Wb0f8Xjr+76Izkb2LqqupRUJS8P02VzUh3hvo2QL2m9GShMcaVIHU\nrQSYWFutiYLzjAJNo3ksdeaUgm3t6pRGh2jVgTvZmnfOgj4ZroSp6u4E5yB7\nsdDAtsvMjtL8ei+E+++PfFub6eFd7YPfcaCZ3GvO2zje2o9ZUEgOPwDoXZC/\na8eE\r\n=3SKq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "5feadcb0b8ea7f0fa108c82f6a4c50695ff2664b", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_3.1.1_1597573896142_0.6993902993873762", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "@sindresorhus/is", "version": "3.1.2", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@3.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "548650de521b344e3781fbdb0ece4aa6f729afb8", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-3.1.2.tgz", "fileCount": 7, "integrity": "sha512-JiX9vxoKMmu8Y3Zr2RVathBL1Cdu4Nt4MuNWemt1Nc06A0RAin9c5FArkhGsyMBWfCu4zj+9b+GxtjAnE4qqLQ==", "signatures": [{"sig": "MEYCIQCYXoSngr9ZtJ8ctB+rrB2qHHzJEdeJ9U98QbkSVS1ShQIhANzxd8lX3AwIraT6HGDsIBmy63rh5Y+Wek8kI929/Gvn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQEnECRA9TVsSAnZWagAA374QAIQjnOYGlUIQITxroMSK\nOtb4IOniOEzTDC1G4C+sPN/2bJEjj3HMYygPHZGksyj6DCBNhAjCcJHTnfKL\nXWy11nS2g8G6Y4B5S1baNQPWbFVpJ+1Ll//zk+aO882awGukqPGXZTBnv19w\nTrf4BZG8XtYBSsX3QMfAQVE5SGh2KgYZRheHPecvGj5vLuvkZcAg0Oqnrvmu\nZ7xUQRN8eJpcZiZshT/DuLEWSTyXHh/HR6HZavnO9HbZb9NkrBuzAG3/9NQe\nkhX5CJNg6yANComdlDzEhnhA6wV7ZOy5vaqLuZXEU/riQvSdw2oGMWK/Tr9X\noMhjcaNI6Sb5iKe7lZgx5/4tw/5+Xo3ztQQbL/pqVcUixaqQssrdpVam/AmO\nNhN7Dc/peGRU1ZyiO/AX1LYuwqJ+yjp37KjarErJC3PDpbY31RBrf8AYBah/\nk5ANIcx5mqmfg6XiLnFHEYjBxkpNiXwo7GwwprfBTMKOeenu1kWEPtD7bZZt\nIm6+nFS1IYh8X11L0xd1OQrGdoBl3rN7G7mZJASQ4ZG+FyMeZs1cyZ3e7ZKQ\ncmh6IQ4svnR1e9j02focM3+THaDNbWB6x34iMEeGltxYFUcIupGU6IsiapN3\niR+vty1Agu5ELbqrLK9QMq5fBenvna9Vblw92dl3EarJ7BoKZwm1DXZMDzjP\nWvaO\r\n=Ghfb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "bf6bba7af8f3f16e1b0ab4eb390196a5ffa9ef7e", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "12.18.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_3.1.2_1598048707448_0.8399747033299334", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@sindresorhus/is", "version": "4.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "2ff674e9611b45b528896d820d3d7a812de2f0e4", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-FyD2meJpDPjyNQejSjvnhpgI/azsQkA4lGbuu5BQZfjvJ9cbRZXzeWL2HceCekW4lixO9JPesIIQkSoLjeJHNQ==", "signatures": [{"sig": "MEUCIG14Uvlvah3oJoOlyFW1RgWgsQFlViNwJZ6F+uN2xvMSAiEAo51NhvVrsLFqehJAThfvlvQrUFFBmP9awPpX25STZUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhNQECRA9TVsSAnZWagAAWz8QAKUgZfIe82zzy8yCqKXC\nN1MQqcEDLzNt4tJLcUOQVt/F8o8GQJ/roN6Xn1c7GVSnlcpO/zanlD5DPgvt\ndZoN7aEvdPs5Q/mJDS3D/jNDJ399HvVFNkuAKcNOk8kmlaq08OIXOejRHVB1\nRJUKakepp8jH82hlNqBU8TXu07EKJNfrjc1bPA7NobRzE2o5mAqPfIICDzoo\nArKFg99siE/7l1MlCNExytcNJtW0W/hn3kvOe9pb2/lOv9BFCT8HWKKpSAZ0\nuFKa4fVkHdzbJkcB1tLI+8yI3k4ReyORP0Dhi3lvferLg6IPOCMe8iuUISW5\nhU+pFJyG2yOxG8bEh1LEQNNzL7vkidK3/qq2cDzZwJji83mDBLBY4GQKtNnE\n81JQQJrLgECZhtJaV8r3RSErRU2zQE0/RBuWABnoiLnifVJSfNYSMFuLrWdn\nz7MLOMN0BvR32mVwQlBoG3dbrSnBVwIMOKngDqUwAuHe+FVm868/lFVGN/kP\njDDVjyfi+HrGyPc33ilvOyP5rx4ffeMLokgrDz65FudSc89PTHqykYp+i3wm\nxq7Y98nFTB1+/Ugh0pYn0XTGmiM0BMYWcX5y0HRIRcikaa7cBjTrWxG6DsNm\nekvK2SEKw3bgU94fac8+TZ3lld+WzNZa8NxG6jSjaElIMr9zaI0AQJeHpIaT\ntJh5\r\n=i9Fv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "d528545e02de3396ea900cd93af478292f0697ee", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.0.0_1602540547346_0.5308319005875917", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@sindresorhus/is", "version": "4.0.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "d26729db850fa327b7cacc5522252194404226f5", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.0.1.tgz", "fileCount": 7, "integrity": "sha512-Qm9hBEBu18wt1PO2flE7LPb30BHMQt1eQgbV76YntdNk73XZGpn3izvGTYxbGgzXKgbCjiia0uxTd3aTNQrY/g==", "signatures": [{"sig": "MEUCIQCnyhtPr/NYGwGYkw1uw0fv6FalLuktlwNMeAZHoCqEnAIgZUBWY/Z6906C7kucGaWjelHnrNNw16tcNytbUSXPsII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggTt/CRA9TVsSAnZWagAA7R8P/AwxpzqSgkv9NbcZs2gw\nw1yZecCKYm6pblqSL/wY11vngYr78puye2LAL5aaH4yglklGD0KAkAtm27XD\n7w8FwVzpfxNdm/WTxPt8BNwSibWAGb1FGPleJnpaAFxCEZt7RwsPTgosLyqt\nSdMYOQZcCPPiXV/VNswei9naA+N/yCt+uRCebwt0EyAFsa7eY5YWjPGrN4zY\nWw21aD5RuqSLKn9nJZZfVeXy7hm0Mh9TTeEuwJmvE6mgVI3rv9agX//SxcNn\nOOcEXKx4vg9S8R/TFS51v7kHPZgZpGRe0AWfeAoWwXXPkmsUZouWPAg/7lGs\nGxUWTPHGOKYBJUg7AxmKA+07PpVLv2JIy3E0WdqkJfcSxJ1p044fkKtxtwsB\n+Py1mvrKamimvr7ddTjD/R2AMX87O5AmPXc8u1NwomZ9b08TZe9qxYHSepW1\nGEKNKksSu7esRiSDxy9TrkJJEUJSUvaM3iLMD9sN/W4m9o6lvGMs6zbkX4U3\nvwbE49uK/tqJiIgSChfBs0n9vb1SUIczgLBzzh13MJRrERb5KVEyGU4dCkEO\nBYY3M8KUQEPV6X4Xq1czsFnppY6mzPhNlg0FEhRBM4gTGFlO94fNw1Cdk1XG\n5abcdb20y9YPu31xfs06iz5/bWJ9VdwZ9EciHpczex2jOWSgIs4BNNFFRoMT\nbkC+\r\n=no53\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "238e8c80c7de926f62d3d0e903a257bf32667d0d", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.0.1_1619082110336_0.6687762540613778", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@sindresorhus/is", "version": "4.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "3853c0c48b47f0ebcdd3cd9a66fdd0d277173e78", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-Cgva8HxclecUCmAImsWsbZGUh6p5DSzQ8l2Uzxuj9ANiD7LVhLM1UJ2hX/R2Y+ILpvqgW9QjmTCaBkXtj8+UOg==", "signatures": [{"sig": "MEUCIF4UzoX1KgU5IuNDLOaMhIzgt4WswsWQLyvnJ3yElCiSAiEAgKs4gaa5KHYbNlZ47GULxtPHif5T71DCBZ/PoaQSmQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOxgzCRA9TVsSAnZWagAAPx8P/ROGK7e80/syp1QkfcQx\n6gc4Weu4sNPhJdU/AdSwl9NYBjQ0SiIgdkZ6fQ1woe+6CB2qhUj+Y18PCVLC\nf6mm1/pk8Up5n5P3vu77QWixXCKbu3v/ZO8cFGHGc5EN3s2ifCxBTEVewzz8\nUS7js7DDJ9+nx5NW1gJMjjddw0hUIiP9Z1qNDT/bWhOYj6cjsBmeVmqYOObX\nH/+FL8OSNOcydOkwWGuSVMKHO/rwMFIv/eeth6tylCHlTcLMWEaixEfdd3qc\noRHKTNMqEEVNIWQ3MLOLFTXqRTDptvD/n3QgpTBgSAobudNTSz6VAZD5yp3i\nqj5HQUIkQDIub8EezhcoRRm0f6qvM+8+Qlrkmf9+jKJcSsgKh7Ah5dpTkkSH\nz+YYIjpWtRf58bAPAgBHQddKSL2yYE083eee0vBUPLh8104a8eCauKVrN0rZ\ncEGqyh+76izhYi9Ho348MH/X1bMmwv0qlWaljf/67ZLQebirDw8xYEIjEvf/\nRKgozGOcCwpDxko/wCPtr7KtKQy3I1uXHGchkUnbTO/Rp8MkeC7lduE+7mca\n4qSZI9Kua3wGA4l8OsPTM/pcUAJZGf+NOK2Bt9Vw4O6k6dXxzkO/uM/WzHUD\nSJBDHHKxHjl6ogRbhQGgnME6zrXvw0n1VbFwlzF8pqkEPxfdhEhxbAtM6i2j\n+911\r\n=TjWC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "b007935b4b436d93af7fbc5a603bf854534b83a8", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.1.0_1631262770886_0.9722094574469", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@sindresorhus/is", "version": "4.2.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "667bfc6186ae7c9e0b45a08960c551437176e1ca", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.2.0.tgz", "fileCount": 7, "integrity": "sha512-VkE3KLBmJwcCaVARtQpfuKcKv8gcBmUubrfHGF84dXuuW6jgsRYxPtzcIhPyK9WAPpRt2/xY6zkD9MnRaJzSyw==", "signatures": [{"sig": "MEQCIGUnrpqmuaEK9gKBm827fdBCRBFbF94NHMScqG+0jXqZAiBkR6VxzFmFWN5j1XOuBlYr1ISvFSWrEXKT/9P3+qIk/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP1/2CRA9TVsSAnZWagAAjWcP/1uHoztiRoIlFdu/7h3u\n2wcPv8PzMZB94XZ1QR2JJklt2GePfVRiq0xVI/mBfV3MOHrXc4ObPvZoV04+\njQfGS4VFJo4WEsihjCCc+dRKmQ1K2F3PlmzvtdRLJZl1jUyhJiJcscN/m/41\nl86LYB2+YMnHoMzAu02PCPZKx6TyVopD8ZCpT1uvCrm9sOPVyUbQYkKDShAG\ntpd5CefJw2cSEfYn7viXsQaXsnhROa2/Fn6/m83EAKXv75zC2s3u3iaegDDX\n3oM2yuvLTJX2vzGWaYTM6Sg4dJhDoUaCXL9b88oD8tS3u0VCmkSpIngHJFJz\nXRhUllmYCBsAgLKEIgohbY37xMX/kFGg7ucUsewDHhw9mGj9YHpFmOuQz0Ey\nEK6maYZrSLpvkFgig3V0+Ko5M7T5NnxXU6BjvbxTw/wMv331EckWP3WZOYyF\nxwf7XfBNxC3ssLgJFBKyiqaYzGAwbfj0V78/KNA4KOQYcw0mA8jwqjAzaSp8\n2sx9Z6x05lxJkbqwehXLJw/As09M8SjKs+F8hnrrGB+NikcjOcJ+ex+1zG/b\n+pVv9NfRTVp/QUJylxO5aet11qboImF7mL1gdNadNijd6u4V/sgKAyvsfWU8\nSioIBZGDD6of5G7Cnh18W7Rt9FJFwQB2vVwpYe7gTAWkEmt4i2jIMdKLGkSE\niqwJ\r\n=8UGk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "13b2343dfc9f43115aa7fafce1e21646cc6b1e67", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.2.0_1631543286724_0.6288165199830849", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "@sindresorhus/is", "version": "4.2.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "b88b5724283db80b507cd612caee9a1947412a20", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.2.1.tgz", "fileCount": 7, "integrity": "sha512-BrzrgtaqEre0qfvI8sMTaEvx+bayuhPmfe2rfeUGPPHYr/PLxCOqkOe4TQTDPb+qcqgNcsAtXV/Ew74mcDIE8w==", "signatures": [{"sig": "MEUCIQDYAs7xT4JPBA80XQVVDgZU7BSklc6AMx/vHSTUVDQQqAIgK0mV2kuzp0xwGTCIOOKNJd2XN5MRZLin5lVo59wpStk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2FLVCRA9TVsSAnZWagAAj80P/ipHjjKs/lWvxWAKkYL8\nxypTs6eOAtGGYMiybyFxAFH8Iqk/sTo0FbR4rdUeMVUxNKllwjkM1GYXC4CC\nljTxmmc5/XVx7XZ5QyGWf8zFTmIjzRXk5GdONGv5BUEvSCQdqo3jtIhbtYuC\nEO95y5gO8punQkNOaRWGX5qSMSm+8unLYIIruS9YyfvdZ04GuBHYlOdm8yi/\nWuk7Gn4CBMzQmnwcAJLoH6fM4O2KIoylgUKF8z8kNX86U+1+von+RVSWL0s2\nWazVMKNK1kan0pgnuMERRYnmTWgxzRnkQUxlT5SWXnfp2/+E6PLfqzf1e7aI\n/TQpTmY5iUDCXYXZnV4WPqNQyVs13pqyN8pac/vA4aGKiyvKtSU6e1JpGe2R\nhukIP7U513zTlRO1/PHWei9BJE5GJJlJ4lxcZtmzk0Ax+uN6+Q9najOnsNoy\n3DLjWWx5kClAuqKqCCekdMelR2xpUu9PY2ILkHtK4IZhvvzyZO29spSWZpJR\nimCXI+Yu+TMb8CULaZEhW62RT04/UlR3gaDXPUWY7WNnv6Xeg+Q9EWWiFGra\n89AF1xEKHfNKSyeOHIU0CmIN0vSn/6Uq/DgMsG4SeGk2AWl7JgBsFumxA5UV\nFzXPAc1XXmcarEaTkJrcljZeQKO934rZr/cXFpMMHC0vb4HZzPHm7YyWVbSP\nPqTY\r\n=WpFa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "f5cc764e22da69e2d6d820d3e44f00547bb35336", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.2.1_1641566932997_0.22167215611219993", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@sindresorhus/is", "version": "4.3.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "344fd9bf808a84567ba563d00cc54b2f428dbab1", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.3.0.tgz", "fileCount": 7, "integrity": "sha512-wwOvh0eO3PiTEivGJWiZ+b946SlMSb4pe+y+Ur/4S87cwo09pYi+FWHHnbrM3W9W7cBYKDqQXcrFYjYUCOJUEQ==", "signatures": [{"sig": "MEQCIE9IUqX5cCW5/5l+Zp3RBpg4vgg5LR4VHRhyQZbzXcT0AiAETUoY9Jrpa0wRMz3JXSsYN9bJUcpMpwshvZKUqfPIKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5QfkCRA9TVsSAnZWagAAws4P/306qyF0U0FCslNF4Jth\n96s7knbMm3QUr5oxDbNuET21j5REgkJbjNPL1JBmYONrTbxxW5niPXBDRmcp\nkaCIliYIXYLCh7QpbMVHP4GKQfPprnHRGo9V7hBUMFnRvunvfTjI1vA/RaXs\nK2Jy/RouK8n13UgCmPfRNuLXRUi5aIhOtcXIOtp8NsK66xTmWVnb39CN3SWv\nnUhWi+ismpA4IDOKMrE2IBBhj+ng04gcRmNNxCNtally/0cW5FjIuLV58Uss\n7E/h/n1hIfG/9Xz3seH1LMFVmeKmeC3uICtcCkATRzoTLPiK1Ckg9W4ZPFtm\nl6Lt4Q+psH6yfIfB658dlGrM8hjh27qAlIO//qJTNOASydlhBAlQFrMWRB0x\n9bTD/viRQAMac7G+aXyW3usGak1PKGMNFPaO2jZ6M4pnJXeWIcjWCbaGpac4\nSr02KJj2sS9hn0l/DFwBiWIEyutYQH1Ub1bOkEAEjXzhzqT4izhTCZV9dfd6\nAmys196n6IFwSazhS7SjrpQvU/BVnDyzGge/x7s+jGZPvLtO03fa0o8J7pR0\nxU/5VE4ZF8dQriIZqB09slJTeRphS27P8Zfs3faNc7B4BRcLqfnf1KqNXzyg\n8LoAu+0X9fVlI5eETdVT2SzNRfk0XzifR4O0X1Q3rA8oWF2TlO7CzFffXKOg\nIgBK\r\n=qh4B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "b1efe7f5cf615786a42690cdf38ecf4c51f0b798", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.3.0_1642399716682_0.9680271534972791", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@sindresorhus/is", "version": "4.4.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "e277e5bdbdf7cb1e20d320f02f5e2ed113cd3185", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.4.0.tgz", "fileCount": 7, "integrity": "sha512-QppPM/8l3Mawvh4rn9CNEYIU9bxpXUCRMaX9yUpvBk1nMKusLKpfXGDEKExKaPhLzcn3lzil7pR6rnJ11HgeRQ==", "signatures": [{"sig": "MEYCIQDDMQYQtDhwotf7iRgkk4w8Yd4Zco60AhKUkwHqCGDwoAIhANbII15JP09RMO2XvuO7Cn97qUF7XBdKpVzcBJz4xSTI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh79EzCRA9TVsSAnZWagAAWj8P/jFVDZkTHphRlcaLY6SN\n6lMOMZI7LGTE2bi2ZIcRQ0Sgkgu/885lorCWgW5O4uVSr+DlYMJDfskI/DwH\nr3hqmAgt1Q2HJYh54FCo2BiFBE7dHL/9AenSx1PRK/9HjSfrMldnBNhTk/EE\nu/N4ocppjIgnIFaNfxWHtktQYRdvV34fwMi7YistK3IKAEYrNy6U1wArPKDW\na8gjsMYZyVi2FgfXBr6WNhuU7U5V6bxbh1XB5gkyncgTxbYeFqcherKJJA53\nT5sZ5HJ5J2gcfGYZXEvPeQQd6bFAxRgXQb8gF9jd1aemzR/U4GLvLK2JjPHe\nE/qCP4aRCh7vYN+BHq4Te+huPn9p4RGfl/KiNHWA0TI/cdqlDQD4JRe2sQlC\nhZkVicAVObJbe/fokAMAn72UpM5GGD6rDd3FCF6bDU9QXZ8iR14iO4zC1zUe\nNsXp0Eu6e+XNFI564tcVEtgHQZOtsGAIE/McC1b+QRhCSFF+QNvqi1BZnJsW\nRpUs0zJx1Cp/7N4rT6M9zONWY88a/Mkt4aB5+m+xGGY631cwGGajQ+HAcFAe\nWXfnkIDg08/t5k2kaEA7oVi5drrmCuvK8iO7YsDglQZxA9qOpQ/Cz85dOwzo\n98mqH2kRUY3W9gy2j3WZAzN8GYfgt6TLMXn0eqp4R4d0uuCPO5q+c0P2VSkG\nI6UY\r\n=1eHn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "c3d12667fd60df4f27218aaf8a6b7ac3201dc311", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.4.0_1643106610835_0.2226566818765099", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@sindresorhus/is", "version": "4.5.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "7c8293e2268de42d7037249a9e4f905dc890539b", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.5.0.tgz", "fileCount": 7, "integrity": "sha512-ZzlL5VTnHZJl8wMWEaYk/13hwMNKLylTSPZRz8+0HIwfRTQMnFgUahDNRRV+rTmPADxQZYxna/nQcStNSCccKg==", "signatures": [{"sig": "MEUCIQCc6Jj4hLtk18dfLiaMRmrlWgsiE7VXf7oi7VGPsjBwLwIgQSQcy2L5DFA1OTDR3EHqUcqQs0oJd3hjRm6+FQ3gjFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGJ58ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHcBAAlKU2MyqSq4tFkkA8unLYOukumRJG3C3fmuQthWzpL3DPGBYh\r\ne0rzp/bZKb20cGKL5YU2J5BmdDlCwDua0AFVeWaauv5HBPDPOQBHNmaAeVGe\r\nI4Dpcdx3tY8d4EHUwH7t4DZ2p+ulGsN771ORh738/JFsSdJ9EvXJJCF4ohYk\r\n5sSPcu+RhrmzauNuPTCNFTbvWedWy5go4Gz5ZDcMILJNNHbqFgXAdfHheor4\r\nVHs88g9+CrUakboVgrU95+hDF9Kr55qpsS+yim8Irg+spH05YR6on/MBcZoL\r\nHTvuiVvACIlzBqtIl1B7SSDXlUT4Kw0uE4Z0dNakgBTj+dDXwFcCCjS23vzT\r\nyl1I69nfBwQLZ/oqOVQbag8pL0NyySKN57zOmM9lJT5hon4ssj4Kp9X5GcfO\r\n74pgh0UWERsZ6ydNRUNho/EnYYN2ccA+3ZrZECFdUe37oYgLa3jLzPq16nip\r\nhe5aw/v0zQTBt/KQSUeJVmaWR35I5X7hKA9WNkJwfTOe8KuiSKxbzDqUC1yq\r\n2Xw036++pRO4l+eMb5qq8MNnmN+XsJNVmhzww980Yu1ovpmTo2kAEGZmMm3V\r\nwPNBmeOiYA3+Z/eoFLCerhKOSIGeBDm1SX6DUVgnD5MjIHlOYtphUj6gcdIn\r\n2g4PokOJvRYWm2pl5A+Nm3TPMuf/yFZ6VzA=\r\n=4+03\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "dc2dc9a4380d888c7db37eeac809537205480978", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.5.0_1645780604091_0.9070353091709376", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@sindresorhus/is", "version": "4.6.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@4.6.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "off", "@typescript-eslint/explicit-function-return-type": "off"}, "extends": "xo-typescript", "globals": ["BigInt", "BigInt64Array", "BigUint64Array"], "extensions": ["ts"], "parserOptions": {"project": "./tsconfig.xo.json"}}, "ava": {"require": ["ts-node/register"], "extensions": ["ts"]}, "dist": {"shasum": "3c7c9c46e678feefe7a2e5bb609d3dbd665ffb3f", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.6.0.tgz", "fileCount": 7, "integrity": "sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==", "signatures": [{"sig": "MEQCIHY/PNDdkkDZRMlVf9RFlEMJX1STaWqzSB7ZoQtbAidQAiAHwgxTweYL9NwfyjONfcNHpIy+IjQmGWn2kw6I0T91AQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGz8LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSbg//VpB0d/KlcmV7Z5Y3YlUuKaHJS68MDk6CdKO9GheXjxtCVCXy\r\nnRh4qq/YGidI9UwQqDdU5AodXGr29j7ZSZdM/U3rVuKjpoHFjLCTaSHrpof2\r\n4/9XTU9I7V6TmLQ5yRsQYRqfLHzU86HWYDbfBGPzkm7Jt4ax9lpZ3Pdj0ZEv\r\nkgfV+BVrLQEMl6QCC+C2kZDntKrhZQNDk29frlDd5ZVzUTpm2H0tO7txAmMZ\r\nsKTBvtUA98PfJpEUEbbyYI1oaCApGerY1dEcjUNYNV89CHPNhNRAJOJkHgT0\r\nL0BrIcyrgeciKis9YLOOvvQyYJTUBn8Tjm/bUZBey2NKDrg2hNbTzGk9cKfW\r\n4ogLeWl53YBEQIG2GBRDVsaoIgjNBovTTJIIlNZsqKdVexqDgi/cD5Ilknle\r\nJkvPdnSDCw9u0UN3G+QJ9yeItiAK3cVV0rqWuf/1VREhuJmyHUENDcrX1O6m\r\nxq9MrXLePq18OsbUCsRXOe/pIDYu9NtX2qWF3PnQvDCWvtiAs5cDDIgk57KB\r\nM0w9qbL1m1hA6o7uoAULt1Ldp4rYqOviYNkK5e0o/vDFlZYo+ANk/iElehbt\r\ntv0eLUxy7DEl58EaDzp/tXBr7XHTiDHl0DbL+0YNUS09pgSkdzn+4cQ3OQJ6\r\njEcb4Vco7f4CFlV2PRAbQRs6XPpTvm/h9kY=\r\n=JJ/K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "65ea91297ede74046b19a65c434411a39c16dacd", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.1", "ava": "^3.3.0", "rxjs": "^6.4.0", "jsdom": "^16.0.1", "tempy": "^0.4.0", "del-cli": "^2.0.0", "ts-node": "^8.3.0", "typescript": "~3.8.2", "@types/node": "^14.0.13", "@types/jsdom": "^16.1.0", "zen-observable": "^0.8.8", "@types/zen-observable": "^0.8.0", "@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/parser": "^2.20.0", "eslint-config-xo-typescript": "^0.26.0", "@typescript-eslint/eslint-plugin": "^2.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_4.6.0_1645952779207_0.0444565663812333", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "@sindresorhus/is", "version": "5.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/explicit-function-return-type": "off"}}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "bbcc0973de6aef308dc2d4898bf28b03f38dbc7f", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-AFY4d1/eSXxTBtWg0CFzuV5ekeaeN1QZ7AmwttlulptP2Xw1pVcSxMt/F2HWWhg7TfjE0XhtXFm3Z2j61DbeXA==", "signatures": [{"sig": "MEQCIHD9G4W/DF8//LW6FR8poAeY5iZzXLGB3v+kvaoM3q7QAiAlJnuwd0Sl19zDUfmQ4E5r+HhCFEJ7jktm5yWwN6Ou6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipHqGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUTRAAnyGKGtZCc8ugCX2tqv6ValANyTEddhNZ2sHnGMosCeUZ9NFX\r\nbpVgLt988J5lg/WabnlDFg34AgYRx28xEn9Sp1Tj71mCbD95KfDJ0bxKVIpg\r\nXyjcUE6ghN24i6Ez71dPzIYwPiELED4QYeRoTcEQHCwsnuIMti1sobW49/fH\r\nL90kI3dsOwm5Vzc0glHtcF+VwdurtDwYuCiVs1+zhbfo1FpwfIxieiq3o4te\r\n/sIqDgt8Qel/U7j7ukCV3RUkCtttNfyAdy0MoITe/Sjh3YXAxU1BIpJOFiQo\r\nHJc9yqtyNdh8Hc0nxO/P5gfeS6UdcyJVbn/KEmiz/2f2QbVb6YTGMFNC4wS2\r\noVKRBbTPLFckLVl2RksMtbgpjS775NjHsBvVOgdOgBZ0LB7X+0nyQtJ0/75P\r\nSKiegPT50Uf5aNN77q+Pj0t1MOdlRctLUKaK0zReYRewmOgwCINfiCQvRiu7\r\nkygH+8cl7F0QuEffAJFuRCU3VRRPFbUUFJjxh+phM5DMjFlzfzF4ITZdrUbs\r\nFMSh8rPgoo3cPOQAlcN0OEhM4aTOQ1sFKzsKnem2bNK0Jd94OOY1wYlnVG2S\r\naYhsvdViyVPgEcrMvS7GgXDAcHugOnZ3osTv3asSujF0DWf6lA7FceYb04jZ\r\nzxgcg2ZS1LhlAYA0yhVOP74c9j1ebywviTE=\r\n=FFAK\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "778c5da5b35a9a1402478839badb38cc542bc8fe", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "rxjs": "^7.5.5", "jsdom": "^19.0.0", "tempy": "^3.0.0", "del-cli": "^4.0.1", "ts-node": "^10.8.1", "typescript": "~4.7.3", "@types/node": "^17.0.42", "@types/jsdom": "^16.2.14", "zen-observable": "^0.8.15", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.0.0_1654946437890_0.9418315912984669", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "@sindresorhus/is", "version": "5.0.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/explicit-function-return-type": "off"}}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "0e54d0a471a675453f7ac3f618acb81622155c21", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-Jk18MsUvuX+PUw3/ia0vA3ZJeHa1h5nsHSHPztLNA/RN9LkwfPhXe3wa9/aFJg6MvrPADqetXyQVE5BPkQx7Xw==", "signatures": [{"sig": "MEQCIApN6xApeYTl72eVFCrF6yFcHFrCrDSqLHq7Q2OkEtzIAiA5+aSND7mrHFCJVMsTFgHZvq9X9YxrnP75dTfPLiwhjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipHxXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol5hAAjXfSg53brCdYtZz+tYvTD6c33j3OKXvRwxPOfwKLsediVe+0\r\nMvFeg7KseIU5zwG4ipcIOYIRFz1q73gisDWXlR9hMXep/EUz0TYzgywjThEp\r\no0ADs5CSh7ukTf7pn9BbAOYcNdZdiZ7MlHdqrf+a+RiLYni7xN14ZSlE0+D6\r\n0DCfTFMZoYDVhGTSSiQCMseFnMBCQqS0ZgCzCjVgRKGJyqx/BqgQQWxSSVAO\r\n5hkvplYaqdZt+jOd3B6Skx0KpUg+CcNSu8EJKV7CsqmxzMKkEsbxUZycomRo\r\ntb4zpugK7Dm9Y9CUT0twlUVFXxMMz33N2pXzMoRo8IRmSb4WiQzngpnilODn\r\nC/Vp/KhEurdk7iSGZIoaOUt7tBz7WNBgWY+AkNGb8tLS5E74WGiaJwxnrda/\r\n8FiXxnXuruQy8o7Y+rCHj/yR4poD4LW2Zc07YLzBI+HjLpAt6ygfli3xd6sR\r\nMvqQxHJMSDyI4/D92159uPx18X8/5WwgBxThdtvhw9N0zuZEeYERRZHcRVYy\r\nYXiLuHTBEc7pKMIRW1bZFQGJ7e2Y86jusGbYdReNKREdsBKYqpf2TjsRW1dv\r\nCEIh+0VjjGnuw68Qtm/QDtSdrOV1lQ37Y/UPYdaJbfRnTg97bg/qsL/hATm8\r\nsZl25MCuN0zjGklk5o62D4n98DeQ5/M8fSU=\r\n=jCEq\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "06d217f70ce64add95533940be4f3791841d2607", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "rxjs": "^7.5.5", "jsdom": "^19.0.0", "tempy": "^3.0.0", "del-cli": "^4.0.1", "ts-node": "^10.8.1", "typescript": "~4.7.3", "@types/node": "^17.0.42", "@types/jsdom": "^16.2.14", "zen-observable": "^0.8.15", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.0.1_1654946903187_0.06806002713564285", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "@sindresorhus/is", "version": "5.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/explicit-function-return-type": "off"}}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "d2b56c5eb1619fca9be94ced04ffafde8107a9e1", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.1.0.tgz", "fileCount": 7, "integrity": "sha512-TsEUyp3jzFpAMVO/+iFt+8nRoGJcU9ZE4mfyi3SpFry8j6CaxzLDuC7HefHCQ9/+IykpypZanfPPfeHpUAziyg==", "signatures": [{"sig": "MEUCIQDn4257oSIkjVMrNzkVUQLPQRseJxzjCQ9Um0QMke1tlAIgdO8l8bFRAGVXoB6e1a0Lx3OHV/BDK9zgXo1X44Be8+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipuKwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4fQ/9H9VPVX3fIiKV8WWCzEt5hyg2TCUxNxkug7aH//s0DQeI9W4Y\r\n5f+OIylcL1m0CZDzpfldAARypbtpJf6svBvCKvt0ZXP2/Zsns9kNkfLg94+4\r\nHJPZhvX7/pErtBt1W/llNe4hOzUsm/roPmftDOmxFPvSALIcMNb9jI0stXK+\r\nCmgLtcdz8qwcNtKiYyQwWKFGkAq0t3kVtYoAsC2P74a3xWHxCR1jZ+TBW6X7\r\nCIPXiagiliBSB/djwowaecHzdIzceQhul363dDqoRVnnJiK3YHAU+EUP0BVL\r\nIrPggNUlo4QqRDGNoHk9TZcwqlY1sky65hNrnk3jd18J7r004MDQnpjCo3Pv\r\niX+7kdhPunc2DabHtaglL2uNttyQmsvaoQhdcz4I8GMn8GWGe6yDBmegGM8+\r\nhOuM2CNFwXB9N5Udl0YSe+E2XZcQId4Xbz5+hYrRQNmU+9kt3M53aqaOyWg6\r\n+21NsbqOlMNwHxz1eiqh15fwt85l+s9UDEQ09iFieNTRVsxexhE0khedwMEs\r\nJ0w7BvSs74+ah3DzHCgf6/5rI4SSdVroqR0sYhddWEW7ziLIFbJFml/JrLrr\r\nOAu8ShFyGtKSR6Zlc4makfj5Ep2Yf9Jjv6kec8K6c5yGD4UPHIZssgeWQHbs\r\nJkZBvvBzS+1uQazYKfUezPoQkGNoUnqD7j8=\r\n=goaE\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "dc99f7cd4a8ad309d4c945b21816132dbc09ef92", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "rxjs": "^7.5.5", "jsdom": "^19.0.0", "tempy": "^3.0.0", "del-cli": "^4.0.1", "ts-node": "^10.8.1", "typescript": "~4.7.3", "@types/node": "^17.0.42", "@types/jsdom": "^16.2.14", "zen-observable": "^0.8.15", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.1.0_1655104176348_0.6862178935167182", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "@sindresorhus/is", "version": "5.2.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/explicit-function-return-type": "off"}}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "2d1acdf153e59bc6e865adf5c893dbf388a1789b", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.2.0.tgz", "fileCount": 7, "integrity": "sha512-tDrSvtear91yf6HFlHHwpV5x4ZyzFj3xkZxsfmsuLlQwR4e0+n8Da1Dr+TIY6nla0ATGxqLK9MWz7Dx/jlu9RQ==", "signatures": [{"sig": "MEQCIF0cNYe3Zw+aDoIY0C8BN70RRgcFI8uoJIKC67q3Vt3mAiAeJ4brq7fMTuREuU0xM3TsJ0NlQnwu0gtdY/zHX6F7bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisccsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrnpg//cZbM53ehuCvu1a+iRhZMI7QYf2NXJj2WiS5p6RWYhKN4kc/6\r\noSh80G6pI+QmVT0tHKHrbkIEIEQsqb8cDfyzHd55NOH7g8PiNJ2h7QWQ1GxL\r\nGgeEvBH0bRc0Wf5kuiSrJ+D4v8KZqMrcx/QPvJhLA80WOp+t2ITzxFC57+mB\r\nELpzsGFv0B/BLYHLTgwFzxWNgV1Ahk5xQFx8ZgB1b6a3Gr/WRgfDTufiTCM4\r\nCD+ESAJN2/p2lgJJztr80g+he6ZcJAwrSZfLwLtVmoYDYOYvfHSbY5QTn78/\r\nlMi/GC98tS/9gslkMfL3pGFpSS1LHXbElTRcgO/gW5/N8EkIqA1Ps4bvgiCI\r\nvC9NxEkM9TfSJvJoLA7JSXiQG3zcvE0YZw0JzXKbgGtCEWXYM0HeuVdMaYzj\r\nGYEp3/8LcM0mvBrcm9iavWJQS6h/Ayox6OYg8W4D7M756wf1b6WP2Q5njRzW\r\nG3OOkcyPJyP5VmT0WmWM9z3ylEzmZka3LSJEPe1o6JnG2Aa6Fml4PW89Y7QI\r\nZ/X5IzdMwZxyzvy4yElOvYxh102NKFqZoGML3bt/vQ5l1N9RazacXcvvXsgW\r\ndBk+6F1Tz4vI9727b7PdsNqD0UT17iTmOdogvKDGt5JETKoTNAKoCIe61szy\r\nRPiQToqy4d9cCoqe4EhpFp1UmOj78wnSPiM=\r\n=BIPO\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "d3ff1fdfce3f40aa06aabc2a62b66ff8ccf138ca", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "rxjs": "^7.5.5", "jsdom": "^19.0.0", "tempy": "^3.0.0", "del-cli": "^4.0.1", "ts-node": "^10.8.1", "typescript": "~4.7.3", "@types/node": "^17.0.42", "@types/jsdom": "^16.2.14", "zen-observable": "^0.8.15", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.2.0_1655818028768_0.30772687771045026", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "@sindresorhus/is", "version": "5.3.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "xo": {"rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/explicit-function-return-type": "off"}}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "0ec9264cf54a527671d990eb874e030b55b70dcc", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.3.0.tgz", "fileCount": 7, "integrity": "sha512-CX6t4SYQ37lzxicAqsBtxA3OseeoVrh9cSJ5PFYam0GksYlupRfy1A+Q4aYD3zvcfECLc0zO2u+ZnR2UYKvCrw==", "signatures": [{"sig": "MEQCIGwiJ/570K4Vr7WHNuW/LOO1bZRh2n5xufXIdYRUiIXHAiBImglkaL2EtVlVrrwQhLDr5zeuguzjW0JVrxSgF26Ftw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2WMyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrR4Q/9Fc6GWG8cE+c61DcngnK/ADfjlhCIf4Q1WNqPHwI+iSkKrP3W\r\nfOC70kABWtyj6NerU/T1zpeWcBRdDqcVELs91BgKgfW9ZjopeBGXaHytDuWL\r\nCXdejTAn9s5LCw5vRlFeNWZ1bx02VTUiy2VQYumvyBY5dy0V8FgSiC+7lQlm\r\nFem4Crg0+UHsUzkDdklJp2nUI4nZRfpsu4ddp3ukfkX1nzOb1+iTAgru7yJm\r\nW094wV0qo2J00fKEK0B/eguE4ysn+gM3oE3r9v5gm7C09semLXuWXW7zlWI8\r\nGJ+FdUNadEgzydCT9x9ijEm2tK7mvFUPAn8BU7u77JTIjd56tB52vr4z6eoL\r\nBhT6jHZPUG5S0/Wsl6dVBkHNEhmnj1xS0T7tJAd362Ow/ybSrkBlcNcXIv0+\r\nmkgZt7jO6PVE5TnbHWXiR3ASvP8m2VwRB5MvdN6bEeS3lEUcHaiWSTb6oc4F\r\nVXymQF+3hPBE3jb/M6zjBn7hT6LgpX0TUvnmbbp/NTM+zbpAmM6zb3722RVc\r\nAkCc6N8QrHZIeVxSGUTuO0kSp2RD1sYNhcM5XAEKWWRqedBTWBbD58/bfl6h\r\nIxhOuEDB2ghsW7olBP38XcKlsDaCZRuHEUv2oWH5k3J7cKdJa3zNyLEzyN4g\r\ni21CNWpiok2v7bX7yeHlWeSHzWoTeXGJJjk=\r\n=fqTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "911f44dc36b1fecae135644fc356b82ff834cef6", "scripts": {"test": "xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.1", "rxjs": "^7.5.6", "jsdom": "^20.0.0", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "~4.7.4", "@types/node": "^18.0.6", "@types/jsdom": "^16.2.15", "zen-observable": "^0.8.15", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.3.0_1658413874482_0.665706142590685", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "@sindresorhus/is", "version": "5.4.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "b1bfbd6024311ade045093b424536cefcc8e3a42", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.4.0.tgz", "fileCount": 7, "integrity": "sha512-Ggh6E9AnMpiNXlbXfFUcWE9qm408rL8jDi7+PMBBx7TMbwEmiqAiSmZ+zydYwxcJLqPGNDoLc9mXDuMDBZg0sA==", "signatures": [{"sig": "MEUCICIv03JCUZE+vcJw0henUdfNlFxDfUKRWgQg4upnXw9HAiEAqYk+iNUsulLXd6IuncMU+NIvaLaOCziil6A/S3Rm87w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58984}, "type": "module", "engines": {"node": ">=14.16"}, "exports": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "61a437eba39b13b5fb546638565b0694c6362233", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.0", "rxjs": "^7.8.1", "jsdom": "^20.0.1", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^20.2.5", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.4.0_1685456297611_0.13662705981485845", "host": "s3://npm-registry-packages"}}, "5.4.1": {"name": "@sindresorhus/is", "version": "5.4.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "c4383ce702fb90531c3d310506bab89e70427c53", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.4.1.tgz", "fileCount": 7, "integrity": "sha512-axlrvsHlHlFmKKMEg4VyvMzFr93JWJj4eIfXY1STVuO2fsImCa7ncaiG5gC8HKOX590AW5RtRsC41/B+OfrSqw==", "signatures": [{"sig": "MEUCIDnDK7Wwuf0THlrEn9K3mQIUnMD0jC0C8tyN/iGuX61cAiEAzQj3v2UwKA7W9F8N6dY5hRyLO1JU9zOLImGmzcFt09A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58965}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "7d468191f427cebe41bd3284c95338c13942b553", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.0", "rxjs": "^7.8.1", "jsdom": "^20.0.1", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^20.2.5", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.4.1_1685865857698_0.37857929065866025", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "@sindresorhus/is", "version": "5.5.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "70af51f38ef3b624eb08eb02a2ed70f117fa19d7", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.5.0.tgz", "fileCount": 7, "integrity": "sha512-3rO1QIz6mL0MvFVTOxqhDJRVsLfG/vK2VSlKKPghALA6FhJqU7L+RUHnFvH5BP5HhkWiMQqq514i9ZFTcqoGCQ==", "signatures": [{"sig": "MEUCIAM0exKGDTF2BLCLtuYM4JesyMOZcHt9pi9b3imGHJwVAiEA5i9LzZoUk4/7nkw9w1LVNlJjqcfJRyG+8NRLL1N5g0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59961}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "278e0e9696b9862381a6e3cae34f756c67ef735b", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.0", "rxjs": "^7.8.1", "jsdom": "^20.0.1", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^20.2.5", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.5.0_1689374077073_0.2779350207240856", "host": "s3://npm-registry-packages"}}, "5.5.1": {"name": "@sindresorhus/is", "version": "5.5.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.5.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "a18b694ca8e9f45d64f578a4779568dd74d232b8", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.5.1.tgz", "fileCount": 7, "integrity": "sha512-wTsEUhqTXg1NDW+o9aWANj4LxELwWjqN0F3ltsWwpYoh0NSlMWo+u7FluRrSF2E2uaPYx7dJ3FnTf69git/0ug==", "signatures": [{"sig": "MEQCIGLJk353U7trIQqJGkHsXswKIFYigUplZnN1JKUgqdXiAiAGPU4nMoLgMJOTO1xa0HhH04Cq/+kxhqoLnQVU7ps3Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59952}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "ad0c3b1429c3015d8992785bf5db0c988ae3bd70", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.0", "rxjs": "^7.8.1", "jsdom": "^20.0.1", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^20.2.5", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.5.1_1689518885309_0.8336172763068661", "host": "s3://npm-registry-packages"}}, "5.5.2": {"name": "@sindresorhus/is", "version": "5.5.2", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.5.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "cbfd816d556b941f65d32cfc6d708359b826e7ea", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.5.2.tgz", "fileCount": 7, "integrity": "sha512-8ZMK+V6YpeZFfW6hU9uAeWVuq8v3t7BaG276gIO+kVqnAcLrHCXdFUOf7kgouyfAarkZtuavIqY3RsXTsTWviw==", "signatures": [{"sig": "MEQCIERm3JAt4ij3QmutP8HOcP8uRG2otUgck6dtkz48BBIxAiAWeHzejeVXw5+hcB2SVLERZ+DoVIPQiIXok++RuyuRbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59904}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "94dc71557750a96b1ffc0c27ec9a065bc18a05de", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.0", "rxjs": "^7.8.1", "jsdom": "^20.0.1", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^20.2.5", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.5.2_1689634062375_0.1845973298465211", "host": "s3://npm-registry-packages"}}, "5.6.0": {"name": "@sindresorhus/is", "version": "5.6.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@5.6.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "41dd6093d34652cddb5d5bdeee04eafc33826668", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-5.6.0.tgz", "fileCount": 7, "integrity": "sha512-TV7t8GKYaJWsn00tFDqBw8+Uqmr8A0fRU1tvTQhyZzGv0sJCGRQL3JGMI3ucuKo3XIZdUP+Lx7/gh2t3lewy7g==", "signatures": [{"sig": "MEUCIQDs8St4avP/nLmcCwXXAx+f7NxfOSRRutGkDcOW42381wIgZpHi7t7ZTRmMSxWNZaJildW/2x7klVSqm7QaSB7pdiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61258}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "44beb083a3d7aee5dc4136128580bacdbaca9974", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.0", "rxjs": "^7.8.1", "jsdom": "^20.0.1", "tempy": "^3.0.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/node": "^20.2.5", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is_5.6.0_1690116262599_0.7826075226025746", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "@sindresorhus/is", "version": "6.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "dist": {"shasum": "06fa296f23affba79fc6a4b6e67c7742e85acd71", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-iVkg+3V+GJfof7yAHOxHyoiEfgpRJhnddOY3EsTM/uqwcm6M/jDQwK1n1nnraMpTRT36CxS0RCtJohggaQ0jgQ==", "signatures": [{"sig": "MEYCIQCrH9zpm1+P7xrEsdWV9r1vihZwaUOXPO71hczFmb+71QIhAIq0oFXcXFgTdL0oYzqw5KXycc12uwaMQ21Op+EiEmY+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92908}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "877ed1cc6aeac5be31186f0359128a5042840c36", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "rxjs": "^7.8.1", "jsdom": "^22.1.0", "tempy": "^3.1.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/node": "^20.5.0", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_6.0.0_1692125528329_0.3348358993860836", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "@sindresorhus/is", "version": "6.0.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@6.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}}, "dist": {"shasum": "1abd5a02e962ed67057e1fd946f6c3e59b872ba1", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-6.0.1.tgz", "fileCount": 7, "integrity": "sha512-yLtFOyhTzjtHXgccpHlfz/HKtfXb43HQLTUfAZjSq9OZJZmbAVVLbYwG7dRSMw4C38249lkiZZ0flkdBRZl+lg==", "signatures": [{"sig": "MEYCIQDHQ8Jmx1MDcVIcvzR/xgbcRgb/z0DJpfghLIIQU6IVCAIhAPIalsDsMWMbrH3B4QCaGmvA9TG6JZZ1v+pigkP2YTb/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93009}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "f10e2caf3d52b342f57f74933d9be54b241119ab", "scripts": {"test": "tsc --noEmit && xo && NODE_OPTIONS='--loader=ts-node/esm --no-warnings=ExperimentalWarning' ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "rxjs": "^7.8.1", "jsdom": "^22.1.0", "tempy": "^3.1.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/node": "^20.5.0", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_6.0.1_1697389570775_0.7384911241466692", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "@sindresorhus/is", "version": "6.1.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@6.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}}, "dist": {"shasum": "71a4ca5171888fb7fc36c6d1ff3604b0a5e43555", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-6.1.0.tgz", "fileCount": 7, "integrity": "sha512-BuvU07zq3tQ/2SIgBsEuxKYDyDjC0n7Zir52bpHy2xnBbW81+po43aLFPLbeV3HRAheFbGud1qgcqSYfhtHMAg==", "signatures": [{"sig": "MEYCIQDw8exHQLV5zxEZOiQrhmXhXf9P7WF+kUhK7vnfxqgnXwIhAPxIEbqHTdMdE8dR9mV9yKOWG3Xo+Urm713P3KkC6laH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93099}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "1acbd9e2023f6a8f12deef0788167eaec0fb258f", "scripts": {"test": "tsc --noEmit && xo && NODE_OPTIONS='--loader=ts-node/esm --no-warnings=ExperimentalWarning' ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "rxjs": "^7.8.1", "jsdom": "^22.1.0", "tempy": "^3.1.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/node": "^20.5.0", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_6.1.0_1698332594805_0.30368847872985927", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "@sindresorhus/is", "version": "6.2.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@6.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}}, "dist": {"shasum": "979e220a34e7403790f65cc7e381f6d50aeb8eea", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-6.2.0.tgz", "fileCount": 7, "integrity": "sha512-yM/IGPkVnYGblhDosFBwq0ZGdnVSBkNV4onUtipGMOjZd4kB6GAu3ys91aftSbyMHh6A2GPdt+KDI5NoWP63MQ==", "signatures": [{"sig": "MEUCIDgCArXhY8ik7r+3u9ElxoRmfwU0vc9DS4KEQFNLhixRAiEAy/PeGOxc+X1nt/cdhZOtmph13wMdVmy5jeR1QQl5OeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94207}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "664b9077e1666531e359118aa5ae1cea5983a2dd", "scripts": {"test": "tsc --noEmit && xo && NODE_OPTIONS='--loader=ts-node/esm --no-warnings=ExperimentalWarning' ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "rxjs": "^7.8.1", "jsdom": "^22.1.0", "tempy": "^3.1.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/node": "^20.5.0", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_6.2.0_1709191867693_0.26890190921822765", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "@sindresorhus/is", "version": "6.3.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@6.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}}, "dist": {"shasum": "c67768a6d85668afef39af6425950a495f29a8ba", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-6.3.0.tgz", "fileCount": 7, "integrity": "sha512-b<PERSON>Pck7aIJjASXIg1qvXSIjXhVBpIEKdl2Wxg4pVqoTRPL8wWExKBrnGIh6CEnhkFQHfc36k7APhO3uXV4g5xg==", "signatures": [{"sig": "MEUCIQDTzRbdQ5ybCQxm8Ll82o9fIfDCNjzSwE+8+CL32yiC+gIgFbQA8W3o1SwAcusXKzXpMzroLdoCn39Fl+ARGRFVBt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99507}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "a1987f8bada5f0279345f17c1b16b8f98291b54c", "scripts": {"test": "tsc --noEmit && xo && NODE_OPTIONS='--loader=ts-node/esm --no-warnings=ExperimentalWarning' ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "rxjs": "^7.8.1", "jsdom": "^22.1.0", "tempy": "^3.1.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/node": "^20.5.0", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_6.3.0_1713852067036_0.25658764167126646", "host": "s3://npm-registry-packages"}}, "6.3.1": {"name": "@sindresorhus/is", "version": "6.3.1", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@6.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}}, "dist": {"shasum": "43bbe2a94de0d7a11b95b7fc8100fa0e4694bbe0", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-6.3.1.tgz", "fileCount": 7, "integrity": "sha512-FX4MfcifwJyFOI2lPoX7PQxCqx8BG1HCho7WdiXwpEQx1Ycij0JxkfYtGK7yqNScrZGSlt6RE6sw8QYoH7eKnQ==", "signatures": [{"sig": "MEQCIDvzT3vTFJCgjax4LabqkFSMIGGa69Pn/32F9F+nfTDoAiBSvtJ0I9x3PLjaXvqdYMTAryp5LDhXb2ufBnP8Gsy8Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99567}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "47f49741eacf0a3678684738159a87c2011bb026", "scripts": {"test": "tsc --noEmit && xo && NODE_OPTIONS='--loader=ts-node/esm --no-warnings=ExperimentalWarning' ava", "build": "del dist && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "rxjs": "^7.8.1", "jsdom": "^22.1.0", "tempy": "^3.1.0", "del-cli": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/node": "^20.5.0", "expect-type": "^0.16.0", "@types/jsdom": "^21.1.1", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.3", "@sindresorhus/tsconfig": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_6.3.1_1715847337507_0.6320826896786624", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@sindresorhus/is", "version": "7.0.0", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@sindresorhus/is@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/is#readme", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "ava": {"extensions": {"ts": "module"}, "nodeArguments": ["--import=tsimp/import"], "environmentVariables": {"TSIMP_DIAG": "error"}}, "dist": {"shasum": "af4d8061cd325782355e7716633e03f7758d4f17", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-WDTlVTyvFivSOuyvMeedzg2hdoBLZ3f1uNVuEida2Rl9BrfjrIRjWA/VZIrMRLvSwJYCAlCRA3usDt1THytxWQ==", "signatures": [{"sig": "MEUCIFN7xHo8AkAqhuS3cYdfblujW741CnSlub5qD4s0eZaeAiEA+rgkNyFeE8RV3Gs9l+WoNvFgaQjGsh4wob4fSxAT2iM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99319}, "type": "module", "engines": {"node": ">=18"}, "exports": {"types": "./distribution/index.d.ts", "default": "./distribution/index.js"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "gitHead": "ab85d9bca9564eb0f867ab45e3cbf45659d9ac4c", "scripts": {"test": "tsc --noEmit && xo && ava", "build": "del distribution && tsc", "prepare": "npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/is.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Type check values", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.58.0", "ava": "^6.1.3", "rxjs": "^7.8.1", "jsdom": "^24.1.0", "tempy": "^3.1.0", "tsimp": "^2.0.11", "del-cli": "^5.1.0", "typescript": "^5.5.3", "@types/node": "^20.14.10", "expect-type": "^0.19.0", "@types/jsdom": "^21.1.7", "zen-observable": "^0.10.0", "@types/zen-observable": "^0.8.7", "@sindresorhus/tsconfig": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is_7.0.0_1720618987954_0.32843057792060604", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "@sindresorhus/is", "version": "7.0.1", "description": "Type check values", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is.git"}, "funding": "https://github.com/sindresorhus/is?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./distribution/index.d.ts", "default": "./distribution/index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"build": "del distribution && tsc", "test": "tsc --noEmit && xo && ava", "prepare": "npm run build"}, "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "devDependencies": {"@sindresorhus/tsconfig": "^6.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.10", "@types/zen-observable": "^0.8.7", "ava": "^6.1.3", "del-cli": "^5.1.0", "expect-type": "^0.19.0", "jsdom": "^24.1.0", "rxjs": "^7.8.1", "tempy": "^3.1.0", "tsimp": "^2.0.11", "typescript": "^5.5.3", "xo": "^0.58.0", "zen-observable": "^0.10.0"}, "ava": {"environmentVariables": {"TSIMP_DIAG": "error"}, "extensions": {"ts": "module"}, "nodeArguments": ["--import=tsimp/import"]}, "_id": "@sindresorhus/is@7.0.1", "gitHead": "e0976457e04ba5df210ca0c976844b580b62f741", "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "homepage": "https://github.com/sindresorhus/is#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-QWLl2P+rsCJeofkDNIT3WFmb6NrRud1SUYW8dIhXK/46XFV8Q/g7Bsvib0Askb0reRLe+WYPeeE+l5cH7SlkuQ==", "shasum": "693cd0bfa7fdc71a3386b72088b660fb70851927", "tarball": "https://registry.npmjs.org/@sindresorhus/is/-/is-7.0.1.tgz", "fileCount": 7, "unpackedSize": 99427, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbdRS9zTtLzdEn34Jscd+4KqPOzICsKbTKEtRBUyIYngIgIPK+IdFDSVZjNk8+DztOC8zz+JcE8HhXTwpQoSDAsy4="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is_7.0.1_1725638128549_0.9979976583229837"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-09-21T17:54:29.643Z", "modified": "2024-09-06T15:55:28.980Z", "0.1.0": "2017-09-21T17:54:29.643Z", "0.2.0": "2017-09-29T03:22:58.510Z", "0.2.1": "2017-10-05T04:21:48.204Z", "0.3.0": "2017-10-11T09:51:14.974Z", "0.4.0": "2017-10-21T19:28:29.768Z", "0.5.0": "2017-11-07T03:23:52.189Z", "0.6.0": "2017-11-19T20:18:18.050Z", "0.7.0": "2017-12-11T20:40:22.859Z", "0.8.0": "2018-04-03T17:10:13.482Z", "0.9.0": "2018-05-04T05:31:15.672Z", "0.10.0": "2018-07-05T16:33:40.901Z", "0.11.0": "2018-07-10T09:56:50.311Z", "0.12.0": "2018-09-28T06:36:14.674Z", "0.13.0": "2018-11-02T12:10:40.889Z", "0.14.0": "2018-12-13T16:00:08.390Z", "0.15.0": "2019-02-03T19:55:51.371Z", "0.16.0": "2019-05-04T09:15:03.794Z", "0.17.0": "2019-05-13T04:08:48.018Z", "0.17.1": "2019-05-21T09:49:11.963Z", "1.0.0": "2019-06-30T08:25:52.005Z", "1.1.0": "2019-10-03T08:02:14.587Z", "1.2.0": "2019-10-04T04:40:23.445Z", "2.0.0": "2020-01-29T17:47:27.268Z", "2.1.0": "2020-02-17T07:23:10.797Z", "2.1.1": "2020-04-19T10:21:40.095Z", "3.0.0": "2020-06-27T20:55:46.906Z", "3.1.0": "2020-07-25T07:15:16.478Z", "3.1.1": "2020-08-16T10:31:36.319Z", "3.1.2": "2020-08-21T22:25:07.578Z", "4.0.0": "2020-10-12T22:09:07.590Z", "4.0.1": "2021-04-22T09:01:50.545Z", "4.1.0": "2021-09-10T08:32:51.058Z", "4.2.0": "2021-09-13T14:28:06.877Z", "4.2.1": "2022-01-07T14:48:53.159Z", "4.3.0": "2022-01-17T06:08:36.826Z", "4.4.0": "2022-01-25T10:30:11.001Z", "4.5.0": "2022-02-25T09:16:44.257Z", "4.6.0": "2022-02-27T09:06:19.413Z", "5.0.0": "2022-06-11T11:20:38.069Z", "5.0.1": "2022-06-11T11:28:23.379Z", "5.1.0": "2022-06-13T07:09:36.524Z", "5.2.0": "2022-06-21T13:27:08.933Z", "5.3.0": "2022-07-21T14:31:14.668Z", "5.4.0": "2023-05-30T14:18:17.790Z", "5.4.1": "2023-06-04T08:04:17.894Z", "5.5.0": "2023-07-14T22:34:37.294Z", "5.5.1": "2023-07-16T14:48:05.490Z", "5.5.2": "2023-07-17T22:47:42.603Z", "5.6.0": "2023-07-23T12:44:22.790Z", "6.0.0": "2023-08-15T18:52:08.599Z", "6.0.1": "2023-10-15T17:06:11.078Z", "6.1.0": "2023-10-26T15:03:14.959Z", "6.2.0": "2024-02-29T07:31:07.858Z", "6.3.0": "2024-04-23T06:01:07.179Z", "6.3.1": "2024-05-16T08:15:37.691Z", "7.0.0": "2024-07-10T13:43:08.107Z", "7.0.1": "2024-09-06T15:55:28.783Z"}, "bugs": {"url": "https://github.com/sindresorhus/is/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/is#readme", "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare", "typescript", "typeguards", "types"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is.git"}, "description": "Type check values", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# is\n\n> Type check values\n\nFor example, `is.string('🦄') //=> true`\n\n<img src=\"header.gif\" width=\"182\" align=\"right\">\n\n## Highlights\n\n- Written in TypeScript\n- [Extensive use of type guards](#type-guards)\n- [Supports type assertions](#type-assertions)\n- [Aware of generic type parameters](#generic-type-parameters) (use with caution)\n- Actively maintained\n- ![Millions of downloads per week](https://img.shields.io/npm/dw/@sindresorhus/is)\n\n## Install\n\n```sh\nnpm install @sindresorhus/is\n```\n\n## Usage\n\n```js\nimport is from '@sindresorhus/is';\n\nis('🦄');\n//=> 'string'\n\nis(new Map());\n//=> 'Map'\n\nis.number(6);\n//=> true\n```\n\n[Assertions](#type-assertions) perform the same type checks, but throw an error if the type does not match.\n\n```js\nimport {assert} from '@sindresorhus/is';\n\nassert.string(2);\n//=> Error: Expected value which is `string`, received value of type `number`.\n```\n\nAssertions (except `assertAll` and `assertAny`) also support an optional custom error message.\n\n```js\nimport {assert} from '@sindresorhus/is';\n\nassert.nonEmptyString(process.env.API_URL, 'The API_URL environment variable is required.');\n//=> Error: The API_URL environment variable is required.\n```\n\nAnd with TypeScript:\n\n```ts\nimport {assert} from '@sindresorhus/is';\n\nassert.string(foo);\n// `foo` is now typed as a `string`.\n```\n\n### Named exports\n\nNamed exports allow tooling to perform tree-shaking, potentially reducing bundle size by including only code from the methods that are used.\n\nEvery method listed below is available as a named export. Each method is prefixed by either `is` or `assert` depending on usage.\n\nFor example:\n\n```js\nimport {assertNull, isUndefined} from '@sindresorhus/is';\n```\n\n## API\n\n### is(value)\n\nReturns the type of `value`.\n\nPrimitives are lowercase and object types are camelcase.\n\nExample:\n\n- `'undefined'`\n- `'null'`\n- `'string'`\n- `'symbol'`\n- `'Array'`\n- `'Function'`\n- `'Object'`\n\nThis method is also exported as `detect`. You can import it like this:\n\n```js\nimport {detect} from '@sindresorhus/is';\n```\n\nNote: It will throw an error if you try to feed it object-wrapped primitives, as that's a bad practice. For example `new String('foo')`.\n\n### is.{method}\n\nAll the below methods accept a value and return a boolean for whether the value is of the desired type.\n\n#### Primitives\n\n##### .undefined(value)\n##### .null(value)\n\n##### .string(value)\n##### .number(value)\n\nNote: `is.number(NaN)` returns `false`. This intentionally deviates from `typeof` behavior to increase user-friendliness of `is` type checks.\n\n##### .boolean(value)\n##### .symbol(value)\n##### .bigint(value)\n\n#### Built-in types\n\n##### .array(value, assertion?)\n\nReturns true if `value` is an array and all of its items match the assertion (if provided).\n\n```js\nis.array(value); // Validate `value` is an array.\nis.array(value, is.number); // Validate `value` is an array and all of its items are numbers.\n```\n\n##### .function(value)\n\n##### .buffer(value)\n\n> [!NOTE]\n> [Prefer using `Uint8Array` instead of `Buffer`.](https://sindresorhus.com/blog/goodbye-nodejs-buffer)\n\n##### .blob(value)\n##### .object(value)\n\nKeep in mind that [functions are objects too](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Functions).\n\n##### .numericString(value)\n\nReturns `true` for a string that represents a number satisfying `is.number`, for example, `'42'` and `'-8.3'`.\n\nNote: `'NaN'` returns `false`, but `'Infinity'` and `'-Infinity'` return `true`.\n\n##### .regExp(value)\n##### .date(value)\n##### .error(value)\n##### .nativePromise(value)\n##### .promise(value)\n\nReturns `true` for any object with a `.then()` and `.catch()` method. Prefer this one over `.nativePromise()` as you usually want to allow userland promise implementations too.\n\n##### .generator(value)\n\nReturns `true` for any object that implements its own `.next()` and `.throw()` methods and has a function definition for `Symbol.iterator`.\n\n##### .generatorFunction(value)\n\n##### .asyncFunction(value)\n\nReturns `true` for any `async` function that can be called with the `await` operator.\n\n```js\nis.asyncFunction(async () => {});\n//=> true\n\nis.asyncFunction(() => {});\n//=> false\n```\n\n##### .asyncGenerator(value)\n\n```js\nis.asyncGenerator(\n\t(async function * () {\n\t\tyield 4;\n\t})()\n);\n//=> true\n\nis.asyncGenerator(\n\t(function * () {\n\t\tyield 4;\n\t})()\n);\n//=> false\n```\n\n##### .asyncGeneratorFunction(value)\n\n```js\nis.asyncGeneratorFunction(async function * () {\n\tyield 4;\n});\n//=> true\n\nis.asyncGeneratorFunction(function * () {\n\tyield 4;\n});\n//=> false\n```\n\n##### .boundFunction(value)\n\nReturns `true` for any `bound` function.\n\n```js\nis.boundFunction(() => {});\n//=> true\n\nis.boundFunction(function () {}.bind(null));\n//=> true\n\nis.boundFunction(function () {});\n//=> false\n```\n\n##### .map(value)\n##### .set(value)\n##### .weakMap(value)\n##### .weakSet(value)\n##### .weakRef(value)\n\n#### Typed arrays\n\n##### .int8Array(value)\n##### .uint8Array(value)\n##### .uint8ClampedArray(value)\n##### .int16Array(value)\n##### .uint16Array(value)\n##### .int32Array(value)\n##### .uint32Array(value)\n##### .float32Array(value)\n##### .float64Array(value)\n##### .bigInt64Array(value)\n##### .bigUint64Array(value)\n\n#### Structured data\n\n##### .arrayBuffer(value)\n##### .sharedArrayBuffer(value)\n##### .dataView(value)\n\n##### .enumCase(value, enum)\n\nTypeScript-only. Returns `true` if `value` is a member of `enum`.\n\n```ts\nenum Direction {\n\tAscending = 'ascending',\n\tDescending = 'descending'\n}\n\nis.enumCase('ascending', Direction);\n//=> true\n\nis.enumCase('other', Direction);\n//=> false\n```\n\n#### Emptiness\n\n##### .emptyString(value)\n\nReturns `true` if the value is a `string` and the `.length` is 0.\n\n##### .emptyStringOrWhitespace(value)\n\nReturns `true` if `is.emptyString(value)` or if it's a `string` that is all whitespace.\n\n##### .nonEmptyString(value)\n\nReturns `true` if the value is a `string` and the `.length` is more than 0.\n\n##### .nonEmptyStringAndNotWhitespace(value)\n\nReturns `true` if the value is a `string` that is not empty and not whitespace.\n\n```js\nconst values = ['property1', '', null, 'property2', '    ', undefined];\n\nvalues.filter(is.nonEmptyStringAndNotWhitespace);\n//=> ['property1', 'property2']\n```\n\n##### .emptyArray(value)\n\nReturns `true` if the value is an `Array` and the `.length` is 0.\n\n##### .nonEmptyArray(value)\n\nReturns `true` if the value is an `Array` and the `.length` is more than 0.\n\n##### .emptyObject(value)\n\nReturns `true` if the value is an `Object` and `Object.keys(value).length` is 0.\n\nPlease note that `Object.keys` returns only own enumerable properties. Hence something like this can happen:\n\n```js\nconst object1 = {};\n\nObject.defineProperty(object1, 'property1', {\n\tvalue: 42,\n\twritable: true,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nis.emptyObject(object1);\n//=> true\n```\n\n##### .nonEmptyObject(value)\n\nReturns `true` if the value is an `Object` and `Object.keys(value).length` is more than 0.\n\n##### .emptySet(value)\n\nReturns `true` if the value is a `Set` and the `.size` is 0.\n\n##### .nonEmptySet(Value)\n\nReturns `true` if the value is a `Set` and the `.size` is more than 0.\n\n##### .emptyMap(value)\n\nReturns `true` if the value is a `Map` and the `.size` is 0.\n\n##### .nonEmptyMap(value)\n\nReturns `true` if the value is a `Map` and the `.size` is more than 0.\n\n#### Miscellaneous\n\n##### .directInstanceOf(value, class)\n\nReturns `true` if `value` is a direct instance of `class`.\n\n```js\nis.directInstanceOf(new Error(), Error);\n//=> true\n\nclass UnicornError extends Error {}\n\nis.directInstanceOf(new UnicornError(), Error);\n//=> false\n```\n\n##### .urlInstance(value)\n\nReturns `true` if `value` is an instance of the [`URL` class](https://developer.mozilla.org/en-US/docs/Web/API/URL).\n\n```js\nconst url = new URL('https://example.com');\n\nis.urlInstance(url);\n//=> true\n```\n\n##### .urlString(value)\n\nReturns `true` if `value` is a URL string.\n\nNote: this only does basic checking using the [`URL` class](https://developer.mozilla.org/en-US/docs/Web/API/URL) constructor.\n\n```js\nconst url = 'https://example.com';\n\nis.urlString(url);\n//=> true\n\nis.urlString(new URL(url));\n//=> false\n```\n\n##### .truthy(value)\n\nReturns `true` for all values that evaluate to true in a boolean context:\n\n```js\nis.truthy('🦄');\n//=> true\n\nis.truthy(undefined);\n//=> false\n```\n\n##### .falsy(value)\n\nReturns `true` if `value` is one of: `false`, `0`, `''`, `null`, `undefined`, `NaN`.\n\n##### .nan(value)\n##### .nullOrUndefined(value)\n##### .primitive(value)\n\nJavaScript primitives are as follows:\n\n- `null`\n- `undefined`\n- `string`\n- `number`\n- `boolean`\n- `symbol`\n- `bigint`\n\n##### .integer(value)\n\n##### .safeInteger(value)\n\nReturns `true` if `value` is a [safe integer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isSafeInteger).\n\n##### .plainObject(value)\n\nAn object is plain if it's created by either `{}`, `new Object()`, or `Object.create(null)`.\n\n##### .iterable(value)\n##### .asyncIterable(value)\n##### .class(value)\n\nReturns `true` if the value is a class constructor.\n\n##### .typedArray(value)\n\n##### .arrayLike(value)\n\nA `value` is array-like if it is not a function and has a `value.length` that is a safe integer greater than or equal to 0.\n\n```js\nis.arrayLike(document.forms);\n//=> true\n\nfunction foo() {\n\tis.arrayLike(arguments);\n\t//=> true\n}\nfoo();\n```\n\n##### .tupleLike(value, guards)\n\nA `value` is tuple-like if it matches the provided `guards` array both in `.length` and in types.\n\n```js\nis.tupleLike([1], [is.number]);\n//=> true\n```\n\n```js\nfunction foo() {\n\tconst tuple = [1, '2', true];\n\tif (is.tupleLike(tuple, [is.number, is.string, is.boolean])) {\n\t\ttuple // [number, string, boolean]\n\t}\n}\n\nfoo();\n```\n\n##### .positiveNumber(value)\n\nCheck if `value` is a number and is more than 0.\n\n##### .negativeNumber(value)\n\nCheck if `value` is a number and is less than 0.\n\n##### .inRange(value, range)\n\nCheck if `value` (number) is in the given `range`. The range is an array of two values, lower bound and upper bound, in no specific order.\n\n```js\nis.inRange(3, [0, 5]);\nis.inRange(3, [5, 0]);\nis.inRange(0, [-2, 2]);\n```\n\n##### .inRange(value, upperBound)\n\nCheck if `value` (number) is in the range of `0` to `upperBound`.\n\n```js\nis.inRange(3, 10);\n```\n\n##### .htmlElement(value)\n\nReturns `true` if `value` is an [HTMLElement](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement).\n\n##### .nodeStream(value)\n\nReturns `true` if `value` is a Node.js [stream](https://nodejs.org/api/stream.html).\n\n```js\nimport fs from 'node:fs';\n\nis.nodeStream(fs.createReadStream('unicorn.png'));\n//=> true\n```\n\n##### .observable(value)\n\nReturns `true` if `value` is an `Observable`.\n\n```js\nimport {Observable} from 'rxjs';\n\nis.observable(new Observable());\n//=> true\n```\n\n##### .infinite(value)\n\nCheck if `value` is `Infinity` or `-Infinity`.\n\n##### .evenInteger(value)\n\nReturns `true` if `value` is an even integer.\n\n##### .oddInteger(value)\n\nReturns `true` if `value` is an odd integer.\n\n##### .propertyKey(value)\n\nReturns `true` if `value` can be used as an object property key (either `string`, `number`, or `symbol`).\n\n##### .formData(value)\n\nReturns `true` if `value` is an instance of the [`FormData` class](https://developer.mozilla.org/en-US/docs/Web/API/FormData).\n\n```js\nconst data = new FormData();\n\nis.formData(data);\n//=> true\n```\n\n##### .urlSearchParams(value)\n\nReturns `true` if `value` is an instance of the [`URLSearchParams` class](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams).\n\n```js\nconst searchParams = new URLSearchParams();\n\nis.urlSearchParams(searchParams);\n//=> true\n```\n\n##### .any(predicate | predicate[], ...values)\n\nUsing a single `predicate` argument, returns `true` if **any** of the input `values` returns true in the `predicate`:\n\n```js\nis.any(is.string, {}, true, '🦄');\n//=> true\n\nis.any(is.boolean, 'unicorns', [], new Map());\n//=> false\n```\n\nUsing an array of `predicate[]`, returns `true` if **any** of the input `values` returns true for **any** of the `predicates` provided in an array:\n\n```js\nis.any([is.string, is.number], {}, true, '🦄');\n//=> true\n\nis.any([is.boolean, is.number], 'unicorns', [], new Map());\n//=> false\n```\n\n##### .all(predicate, ...values)\n\nReturns `true` if **all** of the input `values` returns true in the `predicate`:\n\n```js\nis.all(is.object, {}, new Map(), new Set());\n//=> true\n\nis.all(is.string, '🦄', [], 'unicorns');\n//=> false\n```\n\n##### .validDate(value)\n\nReturns `true` if the value is a valid date.\n\nAll [`Date`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/Date) objects have an internal timestamp value which is the number of milliseconds since the [Unix epoch](https://developer.mozilla.org/en-US/docs/Glossary/Unix_time). When a new `Date` is constructed with bad inputs, no error is thrown. Instead, a new `Date` object is returned. But the internal timestamp value is set to `NaN`, which is an `'Invalid Date'`. Bad inputs can be an non-parsable date string, a non-numeric value or a number that is outside of the expected range for a date value.\n\n```js\nconst valid = new Date('2000-01-01');\n\nis.date(valid);\n//=> true\nvalid.getTime();\n//=> 946684800000\nvalid.toUTCString();\n//=> 'Sat, 01 Jan 2000 00:00:00 GMT'\nis.validDate(valid);\n//=> true\n\nconst invalid = new Date('Not a parsable date string');\n\nis.date(invalid);\n//=> true\ninvalid.getTime();\n//=> NaN\ninvalid.toUTCString();\n//=> 'Invalid Date'\nis.validDate(invalid);\n//=> false\n```\n\n##### .validLength(value)\n\nReturns `true` if the value is a safe integer that is greater than or equal to zero.\n\nThis can be useful to confirm that a value is a valid count of something, ie. 0 or more.\n\n##### .whitespaceString(value)\n\nReturns `true` if the value is a string with only whitespace characters.\n\n## Type guards\n\nWhen using `is` together with TypeScript, [type guards](http://www.typescriptlang.org/docs/handbook/advanced-types.html#type-guards-and-differentiating-types) are being used extensively to infer the correct type inside if-else statements.\n\n```ts\nimport is from '@sindresorhus/is';\n\nconst padLeft = (value: string, padding: string | number) => {\n\tif (is.number(padding)) {\n\t\t// `padding` is typed as `number`\n\t\treturn Array(padding + 1).join(' ') + value;\n\t}\n\n\tif (is.string(padding)) {\n\t\t// `padding` is typed as `string`\n\t\treturn padding + value;\n\t}\n\n\tthrow new TypeError(`Expected 'padding' to be of type 'string' or 'number', got '${is(padding)}'.`);\n}\n\npadLeft('🦄', 3);\n//=> '   🦄'\n\npadLeft('🦄', '🌈');\n//=> '🌈🦄'\n```\n\n## Type assertions\n\nThe type guards are also available as [type assertions](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-7.html#assertion-functions), which throw an error for unexpected types. It is a convenient one-line version of the often repetitive \"if-not-expected-type-throw\" pattern.\n\n```ts\nimport {assert} from '@sindresorhus/is';\n\nconst handleMovieRatingApiResponse = (response: unknown) => {\n\tassert.plainObject(response);\n\t// `response` is now typed as a plain `object` with `unknown` properties.\n\n\tassert.number(response.rating);\n\t// `response.rating` is now typed as a `number`.\n\n\tassert.string(response.title);\n\t// `response.title` is now typed as a `string`.\n\n\treturn `${response.title} (${response.rating * 10})`;\n};\n\nhandleMovieRatingApiResponse({rating: 0.87, title: 'The Matrix'});\n//=> 'The Matrix (8.7)'\n\n// This throws an error.\nhandleMovieRatingApiResponse({rating: '🦄'});\n```\n\n## Generic type parameters\n\nThe type guards and type assertions are aware of [generic type parameters](https://www.typescriptlang.org/docs/handbook/generics.html), such as `Promise<T>` and `Map<Key, Value>`. The default is `unknown` for most cases, since `is` cannot check them at runtime. If the generic type is known at compile-time, either implicitly (inferred) or explicitly (provided), `is` propagates the type so it can be used later.\n\nUse generic type parameters with caution. They are only checked by the TypeScript compiler, and not checked by `is` at runtime. This can lead to unexpected behavior, where the generic type is _assumed_ at compile-time, but actually is something completely different at runtime. It is best to use `unknown` (default) and type-check the value of the generic type parameter at runtime with `is` or `assert`.\n\n```ts\nimport {assert} from '@sindresorhus/is';\n\nasync function badNumberAssumption(input: unknown) {\n\t// Bad assumption about the generic type parameter fools the compile-time type system.\n\tassert.promise<number>(input);\n\t// `input` is a `Promise` but only assumed to be `Promise<number>`.\n\n\tconst resolved = await input;\n\t// `resolved` is typed as `number` but was not actually checked at runtime.\n\n\t// Multiplication will return NaN if the input promise did not actually contain a number.\n\treturn 2 * resolved;\n}\n\nasync function goodNumberAssertion(input: unknown) {\n\tassert.promise(input);\n\t// `input` is typed as `Promise<unknown>`\n\n\tconst resolved = await input;\n\t// `resolved` is typed as `unknown`\n\n\tassert.number(resolved);\n\t// `resolved` is typed as `number`\n\n\t// Uses runtime checks so only numbers will reach the multiplication.\n\treturn 2 * resolved;\n}\n\nbadNumberAssumption(Promise.resolve('An unexpected string'));\n//=> NaN\n\n// This correctly throws an error because of the unexpected string value.\ngoodNumberAssertion(Promise.resolve('An unexpected string'));\n```\n\n## FAQ\n\n### Why yet another type checking module?\n\nThere are hundreds of type checking modules on npm, unfortunately, I couldn't find any that fit my needs:\n\n- Includes both type methods and ability to get the type\n- Types of primitives returned as lowercase and object types as camelcase\n- Covers all built-ins\n- Unsurprising behavior\n- Well-maintained\n- Comprehensive test suite\n\nFor the ones I found, pick 3 of these.\n\nThe most common mistakes I noticed in these modules was using `instanceof` for type checking, forgetting that functions are objects, and omitting `symbol` as a primitive.\n\n### Why not just use `instanceof` instead of this package?\n\n`instanceof` does not work correctly for all types and it does not work across [realms](https://stackoverflow.com/a/49832343/64949). Examples of realms are iframes, windows, web workers, and the `vm` module in Node.js.\n\n## Related\n\n- [environment](https://github.com/sindresorhus/environment) - Check which JavaScript environment your code is running in at runtime\n- [is-stream](https://github.com/sindresorhus/is-stream) - Check if something is a Node.js stream\n- [is-observable](https://github.com/sindresorhus/is-observable) - Check if a value is an Observable\n- [file-type](https://github.com/sindresorhus/file-type) - Detect the file type of a Buffer/Uint8Array\n- [is-ip](https://github.com/sindresorhus/is-ip) - Check if a string is an IP address\n- [is-array-sorted](https://github.com/sindresorhus/is-array-sorted) - Check if an Array is sorted\n- [is-error-constructor](https://github.com/sindresorhus/is-error-constructor) - Check if a value is an error constructor\n- [is-empty-iterable](https://github.com/sindresorhus/is-empty-iterable) - Check if an Iterable is empty\n- [is-blob](https://github.com/sindresorhus/is-blob) - Check if a value is a Blob - File-like object of immutable, raw data\n- [has-emoji](https://github.com/sindresorhus/has-emoji) - Check whether a string has any emoji\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Giora Guttsait](https://github.com/gioragutt)\n- [Brandon Smith](https://github.com/brandon93s)\n", "readmeFilename": "readme.md", "users": {"pl0x": true, "styfle": true, "joelwallis": true, "flumpus-dev": true}}