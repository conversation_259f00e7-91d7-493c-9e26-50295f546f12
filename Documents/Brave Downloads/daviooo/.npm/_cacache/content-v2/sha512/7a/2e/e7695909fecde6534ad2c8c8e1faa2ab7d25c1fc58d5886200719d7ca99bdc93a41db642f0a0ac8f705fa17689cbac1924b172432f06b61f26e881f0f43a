{"_id": "object.assign", "_rev": "87-90d229922a68702a4dd7505692afc6c5", "name": "object.assign", "dist-tags": {"latest": "4.1.7"}, "versions": {"0.1.0": {"name": "object.assign", "version": "0.1.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "ac427fc32f45af9f712d06390a63d442361f5eca", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.1.0.tgz", "integrity": "sha512-v0+87tnF4Ge5rnOOscDTKmrgqZ2V0YWW3WVXKEEsXCPnVVQp1kECry1ZYuNjgQJOoQ+hVgV217CAmHxy5Ty6kw==", "signatures": [{"sig": "MEYCIQC+xYIi27VeAk3D/k78r52WFKRrHVQ1g92v7L9zdF296AIhAOlfDsWlBs0Am/Z6R3ytWvbcBMmj5S1tMqxpa3mmudAt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.4"}, "scripts": {"test": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "Please upgrade to the latest spec-compliant version", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "tape": "~2.10.2", "covert": "~0.3.1"}}, "0.1.1": {"name": "object.assign", "version": "0.1.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "b56304d390e2beaf174693f78e8e4c998650d694", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.1.1.tgz", "integrity": "sha512-WYpgIxTaY3dzyYsWcZOTrrVkldOqh91ImU94WQzUfMwXZfD4/HutggHLvwobmXQ9vrxtiqd4y2GAaQgw/p7bNA==", "signatures": [{"sig": "MEQCIDOBJ/3VvDEn64Hy2YmJfAr34HdMf6Eji2gqwmrHQ5zXAiB+ahogPiLT0xQ1yuM2UIVUH1uYLFZuzOPkkZVSgSQtfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.4"}, "scripts": {"test": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "Please upgrade to the latest spec-compliant version", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "tape": "~2.10.2", "covert": "~0.3.1"}}, "0.2.0": {"name": "object.assign", "version": "0.2.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.2.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "31515deadded86e484b269dd9e4048c1f2430a40", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.2.0.tgz", "integrity": "sha512-vaovWZbiaTW7P10n3jSvj+12+3Qu0sbry9UNM+t6ltJK8hsQzEpbGfHh248jcxuEdKlPRuYa2Ot4q2+fDRb8Ug==", "signatures": [{"sig": "MEUCICyShE9nbik4udwxlgqh3mWUI5DZq79LyOFuuC76IV6RAiEAqA9VYIwXBA/6sgY0dK+30oW6KT2kYTSSeETKEsoAMTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.4"}, "scripts": {"test": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "Please upgrade to the latest spec-compliant version", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "tape": "~2.10.2", "covert": "~0.3.1"}}, "0.2.1": {"name": "object.assign", "version": "0.2.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.2.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "9eb66fc9a0deb0077212f6c731ff5b927841c3f7", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.2.1.tgz", "integrity": "sha512-HmNJgUH3r3ocqtSBD0HEX1MKYhCOb8B3W1OFfI0iKprOkgEMRQPYUitMGWYvYeoe6YjlrNb/tqctVrj8yu3BaQ==", "signatures": [{"sig": "MEUCIGSCOtUb6T/SLHFN7CN8CcUA29IpVHnU3X6/+KJo+i2PAiEAoWtFAsDqDguqmWLzpLIFYX5Sv9N6X92mIsKi0wJB5eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.4"}, "scripts": {"test": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "Please upgrade to the latest spec-compliant version", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "tape": "~2.10.2", "covert": "~0.3.1"}}, "0.3.1": {"name": "object.assign", "version": "0.3.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.3.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "9962e510b3c364b91c14e32a277106e05030e7ec", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.3.1.tgz", "integrity": "sha512-4+wCsh6sGqQSHdCWO9Vu47tvO9+eG7rovunVcSl7bkTxYwy5dAGqBF717GpABGscnFe+ooA091TYRapMSf7GSQ==", "signatures": [{"sig": "MEUCIQCZ7Y0F6CIzs2SdO1mZcGaaxYxXAw+XL+TqNGdh9abX2QIgQM+1RUO+xsCGIwNgERbwLCg/CwL7j7zEt5OJ4GPmmXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.4"}, "scripts": {"test": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "Object.assign should accept any value except null and undefined, and should coerce to an object", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "tape": "~2.12.3", "covert": "~0.3.1"}}, "0.4.0": {"name": "object.assign", "version": "0.4.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.4.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "27f64ad83293fa4af76d3ab492f5fd86552156dd", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.4.0.tgz", "integrity": "sha512-HeTCNRVtTW/LFlFoUsZPvS2hx71Zikb3FkDhAr0zwKCH1JFCHzXEl8x0kyNCCw7sQRcyTKQtmoJrqkmGcVCe3w==", "signatures": [{"sig": "MEYCIQCQXbwgVpo5I7RD6UQbQIzfY8QOOJ5F0/wJhL9q5UPyZgIhAO+X53ZberfGy+w4dfq+csIylUcDlH5ZcqeskYp9neb7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "27f64ad83293fa4af76d3ab492f5fd86552156dd", "engines": {"node": ">= 0.4"}, "gitHead": "d0aae09f3eecd698bbce1c14186e057549816c6b", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "Please update to the latest version", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "jscs": "~1.5.8", "tape": "~2.13.1", "covert": "~0.4.0"}}, "0.4.2": {"name": "object.assign", "version": "0.4.2", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.4.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "602ab33fe04eae8d5fba4d814af670b8bf9c783f", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.4.2.tgz", "integrity": "sha512-9EdgBdwEdP8FRDYeuy9eJh7vK6eCdHh3fSzPVFlniPvtraS/z/pjJK12QN/zvCpUxzxKzYB1XVUrrJGku8an+A==", "signatures": [{"sig": "MEUCIQCKjwpXXOr2Q1szcwYPZMKBxXUAaN+r+96sbPMmWAUHngIgALOd8vM0uoX/3QxTuJMIOA4nJwMUXyrldLyAcWMJ2/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "602ab33fe04eae8d5fba4d814af670b8bf9c783f", "engines": {"node": ">= 0.4"}, "gitHead": "f545ce2e7f33b6b7990a649353492ddc7c1f3faf", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "deprecated": "v0.4.3 doesn't modify function arguments, since it deoptimizes v8", "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "jscs": "~1.5.8", "tape": "~2.13.1", "covert": "~0.4.0"}}, "0.4.3": {"name": "object.assign", "version": "0.4.3", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.4.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "189610dc1fb976f731c18627737b9c63e34167f4", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.4.3.tgz", "integrity": "sha512-BHQy/sNBjeslIwAepQ/CNLcgE/V3wHRF/FG9hFbZy14UPEAckyXGp6n9VB6VSR7RCkHAPg0DjZ5l1A4R8ZziGQ==", "signatures": [{"sig": "MEUCIHysmNZzBwMRAsmP1qhGiMfTCQdIYfIO04L0RqjUllejAiEAmVWraAzdMfihnPY2Q+xPDnWMmtI0BQqtQc2rrPv+yy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "189610dc1fb976f731c18627737b9c63e34167f4", "engines": {"node": ">= 0.4"}, "gitHead": "8fae53124854f7031365a7042ff5f6391bcd4185", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "jscs": "~1.5.8", "tape": "~2.13.1", "covert": "~0.4.0"}}, "0.5.0": {"name": "object.assign", "version": "0.5.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@0.5.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "9492a5a2c091c25f4cbd01f5a779e04967f7a967", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-0.5.0.tgz", "integrity": "sha512-JJg465HnANpwULuZQ8+GFzUhthpRt+eNh+mqM29v7TrBzrkPZ6zcAYDLsHhxosLfz+6h7E/9hDHXYLprUzZDwg==", "signatures": [{"sig": "MEUCIAExAwaf8/JnlMIg6eR6hgjT3yjvPll018bgsOnCYl85AiEA1X3vS6QGWSRD4MoHXhtUiyxYTavq9ks4x4ZZZVJOOkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9492a5a2c091c25f4cbd01f5a779e04967f7a967", "engines": {"node": ">= 0.4"}, "gitHead": "25aafdb056405f780a34674461aac58941afc257", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.5.1"}, "devDependencies": {"is": "~0.3.0", "jscs": "~1.5.8", "tape": "~2.13.1", "covert": "~0.4.0"}}, "1.0.0": {"name": "object.assign", "version": "1.0.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "77335cdbc9bb6eec4ebc9ee0af7b719c29f5cde8", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-1.0.0.tgz", "integrity": "sha512-D/jA30Tuqp0hTVUUpIac15XXoszRUMN1eCOnZRfzFOYwSqiUspxt+WiX6bTBW7Gub5fq2qAJjv/mXijzj2xs8w==", "signatures": [{"sig": "MEQCIHWZApk8dqhRcHMHbsOcJGMd8PxMVjOTM88PWJKJ5dOcAiA6Uyo6BrHagFV6uKdj2+ji10y8O8ORyvlfd9F9NQDeEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "77335cdbc9bb6eec4ebc9ee0af7b719c29f5cde8", "engines": {"node": ">= 0.4"}, "gitHead": "9a05c806f4e277be290d85e27565ca40e17710b1", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~0.6.0"}, "devDependencies": {"is": "~0.3.0", "jscs": "~1.5.8", "tape": "~2.14.0", "covert": "~0.4.0"}}, "1.0.1": {"name": "object.assign", "version": "1.0.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "48dcc87410b98ddeede2115b52eea5f5d6e1c871", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-1.0.1.tgz", "integrity": "sha512-zzRloDGR4M0bKQ/nLPP5X4RA/ApZX/d/nF6V4/eFUXsBEi5n2e7jZj0jROJNOraOuB3ietWxweTs9iV67mqKEw==", "signatures": [{"sig": "MEYCIQCF5prQ4qxWFGkrkJIW/F1KoetJdwpSYrVUrsTwQIffPgIhAIfzZBRcnJdJtv+iRULlt5D4sV4OHEtxzL19OEO1fUgO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "48dcc87410b98ddeede2115b52eea5f5d6e1c871", "engines": {"node": ">= 0.4"}, "gitHead": "91a143139457edaf4d93a8b5a3f88624a5e50302", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~1.0.0"}, "devDependencies": {"is": "~2.0.0", "jscs": "~1.5.8", "tape": "~2.14.0", "covert": "~1.0.0"}}, "1.0.2": {"name": "object.assign", "version": "1.0.2", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "05f188119048084a17f249e6e54cc29bd4faf711", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-1.0.2.tgz", "integrity": "sha512-1SLTBKS+yUpvoAoRDUPXxp5qNQQVdAtrmsGDyOmYYTefLqFn9sCyD6CEK1sjHA6R+NUEzcmmHyudNzy4AziGjw==", "signatures": [{"sig": "MEYCIQCns5ARfGJyxiqoM3/heRsf/aO3HlZxq64gv6QKKP5ykgIhAIX3ctdJhbLcRi1E2m41UlL1K7LurRRU+LfiDvzDengq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "05f188119048084a17f249e6e54cc29bd4faf711", "engines": {"node": ">= 0.4"}, "gitHead": "2cd39f8335d3d80a4bb726bc207eaed3dcc99f8d", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~1.0.1"}, "devDependencies": {"is": "~2.1.0", "jscs": "~1.8.0", "tape": "~3.0.3", "covert": "1.0.0"}}, "1.0.3": {"name": "object.assign", "version": "1.0.3", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "3398a38ef7fbbe2923c20d0875397b5d2dfe7f93", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-1.0.3.tgz", "integrity": "sha512-zRQofVrhMV46VMiAmY0DeWeXSGR68lim1x+BZj0a4URMxhS3nFrH29ZTJfNXEOhfRODn9+Uq41ftg6bf14UXFg==", "signatures": [{"sig": "MEYCIQDNZyWUWgWOOSljH1N/RHr2njrfg65SLWSHBkx6PD4pTgIhAOzZUkEdPzeqXX6ZUqhI/VH4IdoxIOjqKBCi8hwzhFKL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3398a38ef7fbbe2923c20d0875397b5d2dfe7f93", "engines": {"node": ">= 0.4"}, "gitHead": "381370fc095f69ed3882f30c489e7bcce598c59f", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~1.0.1"}, "devDependencies": {"is": "~2.1.0", "jscs": "~1.8.0", "tape": "~3.0.3", "covert": "1.0.0"}}, "1.1.1": {"name": "object.assign", "version": "1.1.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@1.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "f229674273f94fcb230d02c1958a8b94ec9ef95c", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-1.1.1.tgz", "integrity": "sha512-F69Cy1YWq1KjNDhAhT4vpC5/8MVw5r2IsZC1PrlOVt/d8VtxJC/m9vFrhZwizNKlLfjA61bIpWhHpjHXbBdF0Q==", "signatures": [{"sig": "MEYCIQDBeZ9fFQ1atfoq2/9+FDmguqcrX3uZwiZyKZWXW7CfSgIhALRVMnmG/52Vf+MTZiZBZqsEdcglgj1AXiR25E/OvrCB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f229674273f94fcb230d02c1958a8b94ec9ef95c", "engines": {"node": ">= 0.4"}, "gitHead": "921b7a4917e58db22b18f1a8a8349f7506a645ea", "scripts": {"lint": "GLOBIGNORE=dist/* ; jscs *.js */*.js", "test": "npm run lint && node test/index.js && npm run coverage-quiet", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "coverage": "covert test/*.js", "prepublish": "npm run build", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "dependencies": {"object-keys": "~1.0.1"}, "devDependencies": {"is": "~2.2.0", "jscs": "~1.8.1", "tape": "~3.0.3", "covert": "1.0.0", "browserify": "~7.0.0"}}, "2.0.0": {"name": "object.assign", "version": "2.0.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@2.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "765ce3322a6e41fafe98cbd60c48f2304c61a457", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-2.0.0.tgz", "integrity": "sha512-yAVpqfa1LFKherIquWaAOc8BPNwQtZIhuvLS6n70q/wPt61Y41Mi3zSsY+O/Tf2XB7wPvnDpqBrVqXvv6eo1UQ==", "signatures": [{"sig": "MEUCIQD/y+XMjRajlC+48DsMvoR3UgNpewSubvG0Qq+XvAU3/gIgO/NOPI+3fd8UKHvDRdZQtQxq2YE8xTCuqAURGm2Bug4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "765ce3322a6e41fafe98cbd60c48f2304c61a457", "engines": {"node": ">= 0.4"}, "gitHead": "a7ab5dd10d36828129fb93206ef205b959950479", "scripts": {"lint": "jscs *.js test/*.js", "test": "npm run lint && node test/index.js && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "coverage:quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "1.6.3", "dependencies": {"object-keys": "^1.0.3", "define-properties": "^1.0.1"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.1", "jscs": "^1.11.3", "tape": "^4.0.0", "covert": "^1.0.1", "browserify": "^9.0.8"}}, "2.0.1": {"name": "object.assign", "version": "2.0.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@2.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "277b4212e33f624d9dfe3a14dfb177f74d672547", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-2.0.1.tgz", "integrity": "sha512-nNRkjvUdRG3x6Oz9wzeqpPNAiWW4bLXOge3ZDL6Q+3URb1TJ8hKOBxRSfyXnCoECZwocGyHHvvYO+70rmIs9VA==", "signatures": [{"sig": "MEUCICK01mI5EcW1C/W+vK7yKRjh4u8R/E70+WCi6GtjTY2gAiEAmE1rfK4gqk57FUK/cEVqXSaGoUDwHvlxd9a1dLhcxIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "277b4212e33f624d9dfe3a14dfb177f74d672547", "engines": {"node": ">= 0.4"}, "gitHead": "b0ebafc27005038047bbe262a313709541d012ae", "scripts": {"lint": "jscs *.js test/*.js", "test": "npm run lint && node test/index.js && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "coverage:quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "1.6.4", "dependencies": {"object-keys": "^1.0.3", "define-properties": "^1.0.1"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.1", "jscs": "^1.11.3", "tape": "^4.0.0", "covert": "^1.0.1", "browserify": "^9.0.8"}}, "2.0.2": {"name": "object.assign", "version": "2.0.2", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@2.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "0a8bc12d257a92b7e85eb626d2c8556b5cc7d56a", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-2.0.2.tgz", "integrity": "sha512-YaJO7mRRQaxS4a0NX9AoMq8Jnvemw5kdOItBM0flU3lcPO2htuFUVKHGyAUHq9gU3y2e/1Z3YusEhhwHAyMBsA==", "signatures": [{"sig": "MEYCIQD9WX9aZvV58RjP5k1JUwh0TcNmXYQ++YvLNqRM6BLG7QIhAMH5S7C+BR+h3IU2UPw3VwY92MewF1pKu6NJr57Sdqpt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0a8bc12d257a92b7e85eb626d2c8556b5cc7d56a", "engines": {"node": ">= 0.4"}, "gitHead": "18546124c4d2a7b6cf1f2c5797ed761b872846eb", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node test/index.js && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "coverage:quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "2.0.2", "dependencies": {"object-keys": "^1.0.3", "define-properties": "^1.0.1"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.1", "jscs": "^1.13.1", "tape": "^4.0.0", "covert": "^1.1.0", "eslint": "^0.21.0", "browserify": "^10.1.3"}}, "3.0.0": {"name": "object.assign", "version": "3.0.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@3.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "618a1f4b250e41e2ceae2d1a9943ace812e4ccc6", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-3.0.0.tgz", "integrity": "sha512-91fgGoGKE5tGdPFzeq6VsKMJFhjJ+XQKr2583bUbpMWejNRg9vGVCpG0RLjFFy14iiCPWtOIqdaCFSyQh3f7mg==", "signatures": [{"sig": "MEUCIF7dlhSCShvPPlWIIRF11jmnSfzMG8dHfkFMrXwEyO0qAiEAyTOj92PJbNg1TKAZTsQuKXTb9LWpOAZ73kHMbldj3DE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "618a1f4b250e41e2ceae2d1a9943ace812e4ccc6", "engines": {"node": ">= 0.4"}, "gitHead": "980d23d05b5516d7d0830831b40e2c1a123d8df6", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node test/index.js && npm run test:symbol-shim && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "coverage:quiet": "covert test/*.js --quiet", "test:symbol-shim": "node test/symbolShim.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "2.0.2", "dependencies": {"object-keys": "^1.0.3", "define-properties": "^1.0.1"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.1", "jscs": "^1.13.1", "tape": "^4.0.0", "covert": "^1.1.0", "eslint": "^0.21.2", "browserify": "^10.2.1", "get-own-property-symbols": "^0.5.0"}}, "2.0.3": {"name": "object.assign", "version": "2.0.3", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@2.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "91d3e2c6f70c85830b2701eb452d3f56d99bc6ea", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-2.0.3.tgz", "integrity": "sha512-mlHFZhmEWt5gzp8k9I0x7X8VKnGNaVNDGqIxn5KBE9ll0hUCh/5etvhBKLN17xLkuEfV0QncC7gyywFt3nDgzg==", "signatures": [{"sig": "MEUCIB42Z73fVbJIqnaYm8NFxINi9PTGYch8xFFGhvU/brEtAiEA7+1M5MCxqSwcCyF3+or2ZXZoDiDdU5vT13Fc7RhFKvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "91d3e2c6f70c85830b2701eb452d3f56d99bc6ea", "engines": {"node": ">= 0.4"}, "gitHead": "c65e8ed4fd5fc45c24d8973db67457bfac3b3a6d", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node test/index.js && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "coverage:quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "2.3.1", "dependencies": {"object-keys": "^1.0.4", "define-properties": "^1.0.2"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.3", "jscs": "^1.13.1", "tape": "^4.0.0", "covert": "^1.1.0", "eslint": "^0.24.0", "browserify": "^10.2.4"}}, "3.0.1": {"name": "object.assign", "version": "3.0.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@3.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "665c02e68d8f840d8d955e6c8ea4d4e1923146c2", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-3.0.1.tgz", "integrity": "sha512-XueBYCVf3jIzfzA3FJKy5CifrjxxTXNiOhYDBasqmJForbzq4YHGyJCC3yGjLi5UECpoDuYIv2VHFYmsmwvApQ==", "signatures": [{"sig": "MEYCIQC0ozP3rdX25DD3zzCJy+Nvu4NoX9T0w3c3VcXUxZIa5gIhANH+/2+VH8PVKIvhoHg1c2+/z6qcjxvCopLmlHfme85E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "665c02e68d8f840d8d955e6c8ea4d4e1923146c2", "engines": {"node": ">= 0.4"}, "gitHead": "ec6ccb0446b7d2435f73b4dabe0ee47e477a4b1b", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node test/index.js && npm run test:symbol-shim && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "coverage:quiet": "covert test/*.js --quiet", "test:symbol-shim": "node test/symbolShim.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "2.3.1", "dependencies": {"object-keys": "^1.0.4", "define-properties": "^1.0.2"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.3", "jscs": "^1.13.1", "tape": "^4.0.0", "covert": "^1.1.0", "eslint": "^0.24.0", "browserify": "^10.2.4", "get-own-property-symbols": "^0.5.1"}}, "4.0.0": {"name": "object.assign", "version": "4.0.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "8a121c4585debe5e2b71225edbb039650efa81db", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.0.0.tgz", "integrity": "sha512-NmkR+VQywZSMUeUaFkEXK49sN3k63M5Yq6q7vceuO+f+DsUiMlwIeofxdNbIxL3KCT+5i3wp5kSsk22U2IqETQ==", "signatures": [{"sig": "MEUCIH6gHJaHA4bDtceCmzIgmjtZ8hFkap3kXyxOzJRgmxMFAiEAmzb5UIwkiw8bHlaESNlu6Okfc0eV0Wj25632/SrOsL0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8a121c4585debe5e2b71225edbb039650efa81db", "engines": {"node": ">= 0.4"}, "gitHead": "ef940a14529950d22ca142c8abdd9d571618eba3", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && es-shim-api && npm run tests-only && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "tests-only": "node test/index.js && npm run test:symbol-shim", "coverage:quiet": "covert test/*.js --quiet", "test:symbol-shim": "node test/symbolShim.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {"object-keys": "^1.0.7", "function-bind": "^1.0.2", "define-properties": "^1.1.1"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.3", "jscs": "^2.1.0", "tape": "^4.2.0", "covert": "^1.1.0", "eslint": "^1.1.0", "browserify": "^11.0.1", "@es-shims/api": "^1.0.0", "@ljharb/eslint-config": "^1.0.4", "get-own-property-symbols": "^0.5.1"}}, "4.0.1": {"name": "object.assign", "version": "4.0.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "6e3fd88fb40e8e5cc1395688596cb183b4a91ce8", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.0.1.tgz", "integrity": "sha512-kWL2Wb3O1BaKzb5z+EWY6vW5S9MgDb+mk91mnMLOCTJunfeV5YCiGxLJLILCQU3Vdro0nZBQ6LB9wmvhNmRHfA==", "signatures": [{"sig": "MEYCIQDjVKPFG8C1wS+Su85scSn0zfRqUa4Kgt1oYtwAUO+X4wIhALKQgBnSFiE193Ch/kTNPMS1J0BediZ6YYWyiXo6L2pp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6e3fd88fb40e8e5cc1395688596cb183b4a91ce8", "engines": {"node": ">= 0.4"}, "gitHead": "ed5e034a9a4126efded35544e0a154874702f432", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && es-shim-api && npm run tests-only && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "tests-only": "node test/index.js && npm run test:symbol-shim", "coverage:quiet": "covert test/*.js --quiet", "test:symbol-shim": "node test/symbolShim.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {"object-keys": "^1.0.7", "function-bind": "^1.0.2", "define-properties": "^1.1.1"}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.3", "jscs": "^2.1.0", "tape": "^4.2.0", "covert": "^1.1.0", "eslint": "^1.1.0", "browserify": "^11.0.1", "@es-shims/api": "^1.0.0", "@ljharb/eslint-config": "^1.0.4", "get-own-property-symbols": "^0.5.1"}}, "4.0.2": {"name": "object.assign", "version": "4.0.2", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "5e414f5588b2ebee4f5f37f9b7ecb7dc321f2f80", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.0.2.tgz", "integrity": "sha512-l5EqhrV/GK/8VABNFFAX2EQ8UQg8979D9M8lJ4TFnQSDRW3+S1jq0MZZZV/ujXGmrvJnjYmtnh0leM2LJ6EkUQ==", "signatures": [{"sig": "MEUCIQCimd2RHrS8Hdh4qt2k+wXFhJqvnfpHEUB975ZEt/7cWAIgCDULtzPNpRN7QsYGtO7WsHJsJOPOGV87VP8AndrtZ6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5e414f5588b2ebee4f5f37f9b7ecb7dc321f2f80", "engines": {"node": ">= 0.4"}, "gitHead": "efd12f27196a83b66cd8a88f11827f079d90e147", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && es-shim-api && npm run tests-only && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "nsp package", "prepublish": "npm run build", "tests-only": "node test/index.js && npm run test:symbol-shim", "coverage:quiet": "covert test/*.js --quiet", "test:symbol-shim": "node test/symbolShim.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"object-keys": "^1.0.8", "function-bind": "^1.0.2", "define-properties": "^1.1.2"}, "devDependencies": {"is": "^3.1.0", "nsp": "^1.1.0", "jscs": "^2.3.4", "tape": "^4.2.1", "covert": "^1.1.0", "eslint": "^1.7.1", "for-each": "^0.3.2", "browserify": "^11.2.0", "@es-shims/api": "^1.0.0", "@ljharb/eslint-config": "^1.4.1", "get-own-property-symbols": "^0.5.1"}}, "4.0.3": {"name": "object.assign", "version": "4.0.3", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "91aa8203b982135f12269d91f7d0751676f22cb3", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.0.3.tgz", "integrity": "sha512-e1VSN6O4v1MzPaNUXZMq51rZ5/o+tMAE/7Y0ldppU7mxYCGDPUYD2EEsY8OaMk8UFcK0MD6TCzEaQMPxGeDF9Q==", "signatures": [{"sig": "MEUCIQDTBilCUVekswBxS2sYjo/nPpIodnt9HE/pmUnM6dG6bQIgXbHXChtjSOglaMfaQP4z1kts+FVV08Tqx9iHTvGuVbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "91aa8203b982135f12269d91f7d0751676f22cb3", "engines": {"node": ">= 0.4"}, "gitHead": "b7f770dfc0954c710dd99a91b597482fb64560e3", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && es-shim-api && npm run tests-only && npm run security", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "coverage": "covert test/*.js", "security": "requiresafe check", "test:shim": "node test/shimmed.js", "prepublish": "npm run build", "test:shams": "npm run test:shams:getownpropertysymbols && npm run test:shams:corejs", "tests-only": "npm run test:implementation && npm run test:shim && npm run test:shams", "coverage:quiet": "covert test/*.js --quiet", "test:shams:corejs": "node test/shams/core-js.js", "test:implementation": "node test/index.js", "test:shams:getownpropertysymbols": "node test/shams/get-own-property-symbols.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"object-keys": "^1.0.9", "function-bind": "^1.0.2", "define-properties": "^1.1.2"}, "devDependencies": {"is": "^3.1.0", "jscs": "^2.3.5", "tape": "^4.2.2", "covert": "^1.1.0", "eslint": "^1.7.2", "core-js": "^1.2.2", "for-each": "^0.3.2", "browserify": "^11.2.0", "requiresafe": "^2.3.0", "@es-shims/api": "^1.0.0", "@ljharb/eslint-config": "^1.4.1", "get-own-property-symbols": "^0.5.1"}}, "4.0.4": {"name": "object.assign", "version": "4.0.4", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "b1c9cc044ef1b9fe63606fc141abbb32e14730cc", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.0.4.tgz", "integrity": "sha512-R<PERSON><PERSON>4JcrbyFSPF2x7BQWr3nbHLqmy4732SWc2brBe89YLHZoZW/AFWKndkt0LFumLJPbsX3xb0PukBFBwCcmSw==", "signatures": [{"sig": "MEQCIA1hjZh8G3JnzTVwo9xooo3F7IIjSPLlugdro64YSQRFAiBOWPyre0evo0X+2EJrVp9TQ78Tso69pRPrbifBb1FfGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b1c9cc044ef1b9fe63606fc141abbb32e14730cc", "engines": {"node": ">= 0.4"}, "gitHead": "2a76fc5cd47dc47c3a01e5d681b8204213d77c84", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run --silent jscs && npm run --silent eslint", "test": "npm run --silent tests-only", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "pretest": "npm run --silent lint && es-shim-api", "coverage": "covert test/*.js", "posttest": "npm run --silent security", "security": "nsp check", "test:shim": "node test/shimmed.js", "prepublish": "npm run --silent build", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "tests-only": "npm run --silent test:implementation && npm run --silent test:shim && npm run --silent test:shams", "test:native": "node test/native.js", "coverage:quiet": "covert test/*.js --quiet", "test:shams:corejs": "node test/shams/core-js.js", "test:implementation": "node test/index.js", "test:shams:getownpropertysymbols": "node test/shams/get-own-property-symbols.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"object-keys": "^1.0.10", "function-bind": "^1.1.0", "define-properties": "^1.1.2"}, "devDependencies": {"is": "^3.1.0", "nsp": "^2.5.0", "jscs": "^3.0.6", "tape": "^4.6.0", "covert": "^1.1.0", "eslint": "^3.0.0", "core-js": "^2.4.0", "for-each": "^0.3.2", "browserify": "^13.0.1", "@es-shims/api": "^1.2.0", "@ljharb/eslint-config": "^6.0.0", "get-own-property-symbols": "^0.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign-4.0.4.tgz_1467661525697_0.9803472796920687", "host": "packages-16-east.internal.npmjs.com"}}, "4.1.0": {"name": "object.assign", "version": "4.1.0", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "968bf1100d7956bb3ca086f006f846b3bc4008da", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.0.tgz", "integrity": "sha512-exHJeq6kBKj58mqGyTQ9DFvrZC/eR6OwxzoM9YRoGBqrXYonaFyGiFMuc9VZrXf7DarreEwMpurG3dd+CNyW5w==", "signatures": [{"sig": "MEQCIEWSXWHnohu234BUjWkP2vdyu2fpjWP967rPJzPx70BGAiAyP7HF+WrREBcUOPvLS8JE3BXO8fq0wR7hyvqt9uVeDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "5fd74aee40aba36e9a8eb0c329abd33a07b93164", "scripts": {"jscs": "jscs *.js test/*.js", "lint": "npm run --silent jscs && npm run --silent eslint", "test": "npm run --silent tests-only", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "eslint": "eslint *.js test/*.js", "pretest": "npm run --silent lint && es-shim-api", "coverage": "covert test/*.js", "posttest": "npm run --silent security", "security": "nsp check", "test:shim": "node test/shimmed.js", "prepublish": "npm run --silent build", "tests-only": "npm run --silent test:implementation && npm run --silent test:shim", "test:native": "node test/native.js", "coverage:quiet": "covert test/*.js --quiet", "test:implementation": "node test/index.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"has-symbols": "^1.0.0", "object-keys": "^1.0.11", "function-bind": "^1.1.1", "define-properties": "^1.1.2"}, "devDependencies": {"is": "^3.2.1", "nsp": "^3.1.0", "jscs": "^3.0.7", "tape": "^4.8.0", "covert": "^1.1.0", "eslint": "^4.13.1", "for-each": "^0.3.2", "browserify": "^14.5.0", "@es-shims/api": "^2.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign-4.1.0.tgz_1513887492170_0.7630214935634285", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "object.assign", "version": "4.1.1", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "303867a666cdd41936ecdedfb1f8f3e32a478cdd", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.1.tgz", "fileCount": 22, "integrity": "sha512-VT/cxmx5yaoHSOTSyrCygIDFco+RsibY2NM0a4RdEeY/4KgqezwFtK1yr3U67xYhqJSlASm2pKhLVzPj2lr4bA==", "signatures": [{"sig": "MEUCIQC+HW5vxUFIAN+dvQatNEleCK64eHJRK8KD6sg/vuWlJgIgQowUgUTRMFDfP/W619U6bq0zKEWANZyrizUqjvdQcRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfW7irCRA9TVsSAnZWagAA6NIP/A4cWij/8XzFOFPklgu+\nD6ksAHLdhSgfqUtfWuIA4EpAz/jh7rXLn9Fx/4M0wmqXA2v3pikT5rxEyuWw\nOfJr+b2dtHc4XQE8RQnsotkajKxdLQIckX00meUmsj+9XjEFErGoVSBo4Xwz\nv8WQ5nVsOGJsNZFTlCtUoXGnQcvE42ihPR+gEJabLz9PIUqKU/Vige7fff/U\nFQWX/uiG6ewrqs9hH++/zHV5RGb+OpOht20HJ9UoNKF+2hs/EjmFx0VaG4i+\nbIHW+h/nMk8UHikPVeNVVl0isXE4qeHSyMuWNJaRMrxnOmkPFqo1BHjr/Uii\npdvr5URDWEFPZBSdXBP4XyodaQ03cVO7KBuUvU9Bc8MMUPcCUlcDog0420P/\ntUccXktuke1lAIP7fmjBsAT9bPXGUEgxY4lpd19Sp82Irr3d2rPQRE9Vs/X9\n4g/Tp1fkiSsWgCheWg5Efv101gi4LR4Tvx/RvK75QxrwdgMnJRntJ2da5yWG\nmrNAEMGw1OurqWGes8Pdw8nz+rbKlVYIkcBBear0bfT3e078obElwbR8Gyqy\nWTuxWVkGXRi5NsK/cAM4OouWWy5AkAuQH57mx4KqO/w+045cX/vm0iw+CbUt\nEGfJQuzO+5qQ4ZkSnUs8KQ1u185W73ZKJKNTItdF4ndknHoBYVDrthldR3Sk\npDUV\r\n=0ndv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "af0f4e870637b1883b3c99d3f66b1f0bd3a35ca8", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only && npm run test:ses", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "pretest": "npm run --silent lint && es-shim-api --bound", "coverage": "covert test/*.js", "posttest": "aud --production", "test:ses": "node test/ses-compat", "test:shim": "node test/shimmed", "prepublish": "safe-publish-latest && npm run build", "tests-only": "npm run --silent test:implementation && npm run --silent test:shim", "test:native": "node test/native", "test:implementation": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "14.10.0", "dependencies": {"es-abstract": "^1.18.0-next.0", "has-symbols": "^1.0.1", "object-keys": "^1.1.1", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.2", "has": "^1.0.3", "ses": "^0.10.3", "tape": "^5.0.1", "covert": "^1.1.1", "eslint": "^7.8.1", "for-each": "^0.3.3", "browserify": "^16.5.0", "@es-shims/api": "^2.1.2", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.1", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign_4.1.1_1599846571411_0.40628124545567035", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "object.assign", "version": "4.1.2", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "0ed54a342eceb37b38ff76eb831a0e788cb63940", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.2.tgz", "fileCount": 23, "integrity": "sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ==", "signatures": [{"sig": "MEUCIQCMglocF9D8yZO4ffgM0bHsomtIYqpdlB49n8xKdz7k+wIgSEIZ066EKlhqv57TktyzIZmmXxwgBPcqfSiP8OGNmQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnOGwCRA9TVsSAnZWagAA51oP/1BhIwesbvpcl/Hte474\no+OuUR8VC7GquQijhCugnqsMG9/KSKCuPeo47T10fK2v3K5xL+Q6yeZEmgI4\nqf3EavPDPs6QYN14uwTwUm/ps+QXJtxCfbspYY41lcIelM5Qk/6o5kLnsh8+\nq4/euzL5oX/OK6ekSxnP4Hq7I1bFFGS6f0tdBgZT0GR0r3DZ3ctApe/zOYAt\nRUnhKadkXqFMEBl3KCKlFCBeYJTomroo2mRSVABCJycT3G8nrkYCylOja0mp\nKsLKqn52OlfjPvSWspds5fji51Z3gCBeCmdmGAwr2h6vTo00tki8FrV0Hf4R\n+z3Pehx75d11slat0XPORVpzhAOfzdOcMrNJ4CPObhX61XV7FcYa5msJ6RW3\nt3uHbOj5atITSL7n9TJ5JIurK50FOXmxlw56p/j+N8VgxbKg56dMZHOnUe0R\nirjFprb2n9wJvWknGhcmGTrKBelgHtcURRb6GAg3MDmfCjdMu78pJiI4lgYJ\n99X2qDLxQs3mj9wBSjuOiXVnMhRa73PEdYjxKXugqb5f8sNQtRCioL211LqZ\nH5yypbuNi2xd5HbPWEY++Ezm2kpKzLuF28C/cfBEqXvywRTFpBkxrzjVWixz\nNfdW2i82v6Xz1WuiZa49yXR+NnZgQpkuW9mAVJG/0ejGgW5S9l/a+9zi/f0b\nJp6g\r\n=ocH8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "d6b68c1b89094542f22568083b7aa77b87a06cc8", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:ses", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "pretest": "npm run lint && es-shim-api --bound", "posttest": "aud --production", "test:ses": "node test/ses-compat", "test:shim": "nyc node test/shimmed", "prepublish": "safe-publish-latest && npm run build", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "test:implementation": "nyc node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"call-bind": "^1.0.0", "has-symbols": "^1.0.1", "object-keys": "^1.1.1", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.2", "has": "^1.0.3", "nyc": "^10.3.2", "ses": "^0.10.4", "tape": "^5.0.1", "eslint": "^7.12.1", "for-each": "^0.3.3", "browserify": "^16.5.2", "@es-shims/api": "^2.1.2", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.1", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign_4.1.2_1604116911439_0.7041054592325653", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "object.assign", "version": "4.1.3", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "d36b7700ddf0019abb6b1df1bb13f6445f79051f", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.3.tgz", "fileCount": 21, "integrity": "sha512-ZFJnX3zltyjcYJL0RoCJuzb+11zWGyaDbjgxZbdV7rFEcHQuYxrZqhow67aA7xpes6LhojyFDaBKAFfogQrikA==", "signatures": [{"sig": "MEQCIG3pydJgfH/3DgbUdNiNRj90aNKl9XZL7WIQOULlTYW8AiAbBJbQPlKjlepnbZeIf85xHVNE6NFX9NWbzklTg2AIcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1135842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7gYhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrfsg//YHu0Wpk02WGEPUH7sVAwDG9+bm/iVIaTLepRIvKfO6q3zv91\r\njiRqr7Fy/vGqY6DfWQqPbIYlnuSdPBiXVCa5kxdbTFrlnD77m1IxNSxKTFNd\r\nRBMf1Eyh5Av+dSHCHSCfx7PVfQ2fl5mtbB9lEg7PCxZnTuFXZ3zdKRE55KiU\r\nX/LN/KbF2s2Og8/wxovQ2OhHL15HBOo6ScMwm3ZzJ8fM1OxpYeU3Rd4BC+1v\r\n5ueu6hrth5eu7XTumT56MmWMytk/aPxYiLNgV2CL9lNymovDCB1xXf5C5mHu\r\nfP6+Arg0W0gNayVLkzYZQcOVbTRD7wvzqIuk2BA0TIubljxuBDtkHgk8y3KN\r\nizikNWh6kM6vpAYpi+NNdbmpK4EAik9CPFxS4QQ4QmSCL9m0DsyoV6mlfenR\r\nx+e0toYvtYLksAJ2InCDGcwUy2YC98znSJQWyigTd2wz4/QWqleAJPvbKNNA\r\njdUdFG86CyAh5GVVZI66xq3c9e1e5CnHQA/FKdZYKWPcH7ILPiw6ioWLStNs\r\n86mpQrk2pFTW1ja2ckzczAuDaORuyebDUp7OMYncQRI0KP6FXG5URROhDEnl\r\nYrNE5BBA6HK8ZDeDny1PB31mpFJNmiO1KDuiLm/oq8F4YmHaDTCoxnckM6AI\r\nIYmNIRVvP42akrIpwJyExCJLad7JeMuRrN0=\r\n=IUBj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "fe0b0e0124ac1d0cd98ca492d6fc747590018f77", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:ses", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint && es-shim-api --bound", "posttest": "aud --production", "test:ses": "node test/ses-compat", "test:shim": "nyc node test/shimmed", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "prepublishOnly": "safe-publish-latest && npm run build", "test:implementation": "nyc node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"call-bind": "^1.0.2", "has-symbols": "^1.0.3", "object-keys": "^1.1.1", "define-properties": "^1.1.4"}, "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js"]}, "_hasShrinkwrap": false, "devDependencies": {"is": "^3.3.0", "aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "ses": "^0.11.1", "tape": "^5.5.3", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "browserify": "^16.5.2", "@es-shims/api": "^2.2.3", "mock-property": "^1.0.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign_4.1.3_1659766305222_0.17211530677993614", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "object.assign", "version": "4.1.4", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.4.tgz", "fileCount": 22, "integrity": "sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==", "signatures": [{"sig": "MEUCIQDb8d1gRXFbIhJlGex26OJqN4THVoGBsNukyRnkPCB5IgIgES4304nFxJq/L84QkAahKHIXSAyfdWuqMXpsVzlJ9VQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+8P8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog8w//QEY5auUGIpSwfCMeFUFbruKh/GNEaIp9OJcWtySgQ/zfDZg2\r\nVu8yq6dPs2G3dM9sCC4GOYooEPUUv+cgJRxcg9+LTB0H1toAk35MCwFv0Q3o\r\nXvzC1PoI7w3vkx0P6cnqmuZIMhKcw68i79eIgFyNfmURwrFIqP0UUiSNbFyk\r\nDVWXfbhz3JCYHmyS+VHg/F/M6LJZZZPp04zK8d+ZoYziqTfYklZklUNnikYY\r\nD9FNsVIi+K86mtK1ucGkNyLykVJT8/Mn7LvYwVp86mFgQM5vZtT1fEOhZzxD\r\nVskiGHcVLvRnLfuHpadoBQtDakcrbUaNT2xBtTcYlk6ZJNK7/33kN2b+pmuv\r\nUzmmXlNPZgWXfcDsWOxYXhFxsH7jmjGyY+gSSLsK/onvmwtTrlkZJcBFPS40\r\nrk7pKdtywcyUwL4wZ3XuqY3kZRIa917xSi8ROaUmLduno/OtbQ0DjaOZTZn5\r\nWdy0jqKh4IJflG96HwXIAXabfdk3LviydlEE4FbGFkXk9XvzMrNCbmKRIvqg\r\ngx27THXkEQMFqxJn2iM6mgj7cqLlTnmtPwNO2YyXf39Z4NfzWzkR5tPwfMJe\r\nllujP5BOsMdMZdO95mH1MHzjI7ji7kXBQ+/8o0nxm3ovfaMeiyEosZUvBMxs\r\npibYwOJ9d3WPPhhoENoO6nLWQCGqaH1o08o=\r\n=IldA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "025e374fb6436c378918e91b47049a7a043f4b5b", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:ses", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint && es-shim-api --bound", "posttest": "aud --production", "test:ses": "node test/ses-compat", "test:shim": "nyc node test/shimmed", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "prepublishOnly": "safe-publish-latest && npm run build", "test:implementation": "nyc node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"call-bind": "^1.0.2", "has-symbols": "^1.0.3", "object-keys": "^1.1.1", "define-properties": "^1.1.4"}, "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js", "!dist/"]}, "_hasShrinkwrap": false, "devDependencies": {"is": "^3.3.0", "aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "ses": "^0.11.1", "tape": "^5.5.3", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "browserify": "^16.5.2", "@es-shims/api": "^2.2.3", "mock-property": "^1.0.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign_4.1.4_1660666876164_0.8088876915796015", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "object.assign", "version": "4.1.5", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "3a833f9ab7fdb80fc9e8d2300c803d216d8fdbb0", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.5.tgz", "fileCount": 21, "integrity": "sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==", "signatures": [{"sig": "MEUCIQDZr62kYfasL4vonxLQa6q3VxWCse+TcnJpm56hVS/WzAIgTq2Mm08vSNIVY4Nsih+qd9fMrp8bmRe/gAchagT053Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72658}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8688e533b3667c3c3b8dc48bf2329f08f96e67d2", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:ses", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint && es-shim-api --bound", "posttest": "aud --production", "test:ses": "node test/ses-compat", "test:shim": "nyc node test/shimmed", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "prepublishOnly": "safe-publish-latest && npm run build", "test:implementation": "nyc node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "21.3.0", "dependencies": {"call-bind": "^1.0.5", "has-symbols": "^1.0.3", "object-keys": "^1.1.1", "define-properties": "^1.2.1"}, "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js", "!dist/"]}, "_hasShrinkwrap": false, "devDependencies": {"is": "^3.3.0", "aud": "^2.0.3", "nyc": "^10.3.2", "ses": "^0.11.1", "tape": "^5.7.2", "eslint": "=8.8.0", "hasown": "^2.0.0", "for-each": "^0.3.3", "npmignore": "^0.3.1", "browserify": "^16.5.2", "@es-shims/api": "^2.4.2", "mock-property": "^1.0.3", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign_4.1.5_1701371660635_0.6046518328756039", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "object.assign", "version": "4.1.6", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "object.assign@4.1.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/object.assign#readme", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "dist": {"shasum": "b628b1b03f7e76f045c05f8e436976ac0a19f69e", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.6.tgz", "fileCount": 21, "integrity": "sha512-yVh6qdqwYsX9rUYzK6VGV4QT16okE8zNvcX0uRPg9MmBNcvPdTJi7cErXnUw/IeQ7Cg4tYMM6BTL3JEcEA0YTQ==", "signatures": [{"sig": "MEQCIFxkWMNinpGd1n/aPKWHGRZrpxrdAHCG0FwepE6ZbeV3AiAcqMOR+pTsVCNjppl6VlStJwvoCY2dwXitzpFIoZr7PQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78032}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f328e4579d2357ac82397f25041c7f37030a556c", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:ses", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint && es-shim-api --bound", "posttest": "npx npm@'>=10.2' audit --production", "test:ses": "node test/ses-compat", "test:shim": "nyc node test/shimmed", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "prepublishOnly": "safe-publish-latest && npm run build", "test:implementation": "nyc node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/object.assign.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "has-symbols": "^1.1.0", "object-keys": "^1.1.1", "define-properties": "^1.2.1"}, "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js", "!dist/"]}, "_hasShrinkwrap": false, "devDependencies": {"is": "^3.3.0", "nyc": "^10.3.2", "ses": "^1.10.0", "tape": "^5.9.0", "eslint": "=8.8.0", "hasown": "^2.0.2", "for-each": "^0.3.3", "npmignore": "^0.3.1", "browserify": "^16.5.2", "@es-shims/api": "^2.5.1", "mock-property": "^1.1.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/object.assign_4.1.6_1734547067302_0.35867668240123063", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.7": {"name": "object.assign", "version": "4.1.7", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint && es-shim-api --bound", "test": "npm run tests-only && npm run test:ses", "posttest": "npx npm@'>=10.2' audit --production", "tests-only": "npm run test:implementation && npm run test:shim", "test:native": "nyc node test/native", "test:shim": "nyc node test/shimmed", "test:implementation": "nyc node test", "test:ses": "node test/ses-compat", "lint": "eslint .", "build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "prepublishOnly": "safe-publish-latest && npm run build", "prepublish": "not-in-publish || npm run prepublishOnly"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "browserify": "^16.5.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "hasown": "^2.0.2", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "ses": "^1.10.0", "tape": "^5.9.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js", "!dist/"]}, "_id": "object.assign@4.1.7", "gitHead": "0aa92bc8f1d87c9cae87881cf2e9245093f39bda", "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "homepage": "https://github.com/ljharb/object.assign#readme", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==", "shasum": "8c14ca1a424c6a561b0bb2a22f66f5049a945d3d", "tarball": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "fileCount": 21, "unpackedSize": 78300, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZfYh1pCzkaj1UcX6n3E1EarRK7RZcEwfRiurukg8iEQIgDF4QsIs9VXjAxHDZdk4n3kt9VyPDHgMCzLQ49r6itRA="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/object.assign_4.1.7_1734555262666_0.9385776881401351"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-03-14T20:48:28.570Z", "modified": "2024-12-18T20:54:23.075Z", "0.1.0": "2014-03-14T20:48:28.570Z", "0.1.1": "2014-03-14T21:51:07.728Z", "0.2.0": "2014-03-16T06:43:46.641Z", "0.2.1": "2014-03-16T22:48:19.691Z", "0.3.0": "2014-04-10T17:54:08.243Z", "0.3.1": "2014-04-10T18:17:09.841Z", "0.4.0": "2014-07-19T06:40:40.606Z", "0.4.1": "2014-07-19T06:54:56.733Z", "0.4.2": "2014-07-31T03:50:23.625Z", "0.4.3": "2014-07-31T03:52:18.172Z", "0.5.0": "2014-07-31T19:59:21.470Z", "1.0.0": "2014-08-07T16:16:44.017Z", "1.0.1": "2014-08-26T19:23:17.666Z", "1.0.2": "2014-11-28T05:24:09.015Z", "1.0.3": "2014-11-29T20:30:52.220Z", "1.1.0": "2014-12-14T10:32:31.892Z", "1.1.1": "2014-12-14T10:34:22.810Z", "2.0.0": "2015-04-12T17:21:56.816Z", "2.0.1": "2015-04-12T21:20:45.753Z", "2.0.2": "2015-05-20T19:15:38.764Z", "3.0.0": "2015-05-21T06:37:03.313Z", "2.0.3": "2015-06-28T17:21:07.634Z", "3.0.1": "2015-06-28T17:30:40.871Z", "4.0.0": "2015-08-16T07:16:50.361Z", "4.0.1": "2015-08-16T19:40:42.209Z", "4.0.2": "2015-10-21T01:08:59.259Z", "4.0.3": "2015-10-21T08:10:43.032Z", "3.0.2": "2016-06-28T08:13:11.298Z", "4.0.4": "2016-07-04T19:45:29.439Z", "4.1.0": "2017-12-21T20:18:12.266Z", "4.1.1": "2020-09-11T17:49:31.550Z", "4.1.2": "2020-10-31T04:01:51.614Z", "4.1.3": "2022-08-06T06:11:45.447Z", "4.1.4": "2022-08-16T16:21:16.413Z", "4.1.5": "2023-11-30T19:14:20.821Z", "4.1.6": "2024-12-18T18:37:47.491Z", "4.1.7": "2024-12-18T20:54:22.882Z"}, "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/ljharb/object.assign#readme", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# object.assign <sup>[![Version Badge][npm-version-svg]][npm-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][npm-url]\n\nAn Object.assign shim. Invoke its \"shim\" method to shim Object.assign if it is unavailable.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](http://www.ecma-international.org/ecma-262/6.0/#sec-object.assign). In an ES6 environment, it will also work properly with `Symbol`s.\n\nTakes a minimum of 2 arguments: `target` and `source`.\nTakes a variable sized list of source arguments - at least 1, as many as you want.\nThrows a TypeError if the `target` argument is `null` or `undefined`.\n\nMost common usage:\n```js\nvar assign = require('object.assign').getPolyfill(); // returns native method if compliant\n\t/* or */\nvar assign = require('object.assign/polyfill')(); // returns native method if compliant\n```\n\n## Example\n\n```js\nvar assert = require('assert');\n\n// Multiple sources!\nvar target = { a: true };\nvar source1 = { b: true };\nvar source2 = { c: true };\nvar sourceN = { n: true };\n\nvar expected = {\n\ta: true,\n\tb: true,\n\tc: true,\n\tn: true\n};\n\nassign(target, source1, source2, sourceN);\nassert.deepEqual(target, expected); // AWESOME!\n```\n\n```js\nvar target = {\n\ta: true,\n\tb: true,\n\tc: true\n};\nvar source1 = {\n\tc: false,\n\td: false\n};\nvar sourceN = {\n\te: false\n};\n\nvar assigned = assign(target, source1, sourceN);\nassert.equal(target, assigned); // returns the target object\nassert.deepEqual(assigned, {\n\ta: true,\n\tb: true,\n\tc: false,\n\td: false,\n\te: false\n});\n```\n\n```js\n/* when Object.assign is not present */\ndelete Object.assign;\nvar shimmedAssign = require('object.assign').shim();\n\t/* or */\nvar shimmedAssign = require('object.assign/shim')();\n\nassert.equal(shimmedAssign, assign);\n\nvar target = {\n\ta: true,\n\tb: true,\n\tc: true\n};\nvar source = {\n\tc: false,\n\td: false,\n\te: false\n};\n\nvar assigned = assign(target, source);\nassert.deepEqual(Object.assign(target, source), assign(target, source));\n```\n\n```js\n/* when Object.assign is present */\nvar shimmedAssign = require('object.assign').shim();\nassert.equal(shimmedAssign, Object.assign);\n\nvar target = {\n\ta: true,\n\tb: true,\n\tc: true\n};\nvar source = {\n\tc: false,\n\td: false,\n\te: false\n};\n\nassert.deepEqual(Object.assign(target, source), assign(target, source));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[npm-url]: https://npmjs.org/package/object.assign\n[npm-version-svg]: http://versionbadg.es/ljharb/object.assign.svg\n[travis-svg]: https://travis-ci.org/ljharb/object.assign.svg\n[travis-url]: https://travis-ci.org/ljharb/object.assign\n[deps-svg]: https://david-dm.org/ljharb/object.assign.svg?theme=shields.io\n[deps-url]: https://david-dm.org/ljharb/object.assign\n[dev-deps-svg]: https://david-dm.org/ljharb/object.assign/dev-status.svg?theme=shields.io\n[dev-deps-url]: https://david-dm.org/ljharb/object.assign#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/object.assign.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/object.assign.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/object.assign.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=object.assign\n[codecov-image]: https://codecov.io/gh/ljharb/object.assign/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/object.assign/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/object.assign\n[actions-url]: https://github.com/ljharb/object.assign/actions\n", "readmeFilename": "README.md", "users": {"tedyhy": true, "dkannan": true, "ivanoats": true, "qqcome110": true, "flumpus-dev": true, "jamescostian": true, "jian263994241": true}}