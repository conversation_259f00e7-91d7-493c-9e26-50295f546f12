// 🚀 COMMAND REGISTRATION SCRIPT - PROFESSIONAL DEPLOYMENT
require('dotenv').config();
const { deployCommands } = require('./src/deployCommands.js');

console.log('🚀 Starting command registration...');
console.log(`📋 Token: ${process.env.TOKEN ? 'Found' : 'Missing'}`);
console.log(`📋 Client ID: ${process.env.CLIENT_ID || 'Missing'}`);

deployCommands().then(() => {
  console.log('✅ Command registration completed!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Command registration failed:', error);
  process.exit(1);
});
