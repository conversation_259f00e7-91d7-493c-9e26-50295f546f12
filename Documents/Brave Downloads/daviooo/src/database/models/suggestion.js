// 💡 PROFESSIONAL SUGGESTION SYSTEM DATABASE MODEL
// Enterprise-grade suggestion management with advanced features

const mongoose = require('mongoose');

// 📊 SUGGESTION ITEM SCHEMA
const suggestionItemSchema = new mongoose.Schema({
  messageId: {
    type: String,
    required: true,
    unique: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 2000
  },
  authorId: {
    type: String,
    required: true
  },
  category: {
    type: String,
    default: 'General',
    maxlength: 50
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'declined', 'implemented', 'considering'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },

  // 📝 REVIEW INFORMATION
  reviewedBy: {
    type: String,
    default: null
  },
  reviewedAt: {
    type: Date,
    default: null
  },
  reviewReason: {
    type: String,
    maxlength: 500,
    default: null
  },

  // 🗳️ VOTING SYSTEM
  upvotes: {
    type: Number,
    default: 0
  },
  downvotes: {
    type: Number,
    default: 0
  },
  voters: [{
    userId: String,
    vote: { type: String, enum: ['up', 'down'] },
    timestamp: { type: Date, default: Date.now }
  }],

  // 💬 COMMENTS SYSTEM
  comments: [{
    authorId: String,
    content: { type: String, maxlength: 1000 },
    timestamp: { type: Date, default: Date.now },
    isStaff: { type: Boolean, default: false }
  }],

  // 📅 TIMESTAMPS
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  scheduledReview: {
    type: Date,
    default: null
  },

  // 🏷️ METADATA
  tags: [{
    type: String,
    maxlength: 30
  }],
  anonymous: {
    type: Boolean,
    default: false
  },
  implementationDetails: {
    type: String,
    maxlength: 1000,
    default: null
  },

  // 📊 ANALYTICS
  views: {
    type: Number,
    default: 0
  },
  lastViewed: {
    type: Date,
    default: Date.now
  },

  // Legacy support
  accepted: {
    type: Boolean,
    default: false
  },
  declined: {
    type: Boolean,
    default: false
  }
});

// 🔧 SYSTEM CONFIGURATION SCHEMA
const configSchema = new mongoose.Schema({
  // 🎛️ BASIC SETTINGS
  anonymous: {
    type: Boolean,
    default: false
  },
  voting: {
    type: Boolean,
    default: true
  },
  cooldown: {
    type: Number,
    default: 5,
    min: 0,
    max: 1440
  },

  // 📂 CATEGORIES
  categories: [{
    type: String,
    maxlength: 50
  }],

  // 🎭 ROLES
  managerRole: {
    type: String,
    default: null
  },
  reviewerRole: {
    type: String,
    default: null
  },

  // 🔔 NOTIFICATIONS
  logChannel: {
    type: String,
    default: null
  },
  dmNotifications: {
    type: Boolean,
    default: true
  },

  // 📄 TEMPLATES
  templates: [{
    name: { type: String, maxlength: 50 },
    content: { type: String, maxlength: 1000 },
    category: { type: String, maxlength: 50 },
    createdBy: String,
    createdAt: { type: Date, default: Date.now }
  }],

  // 🎯 ADVANCED FEATURES
  autoArchiveDays: {
    type: Number,
    default: 90,
    min: 1,
    max: 365
  },
  requireApproval: {
    type: Boolean,
    default: false
  },
  maxSuggestionsPerUser: {
    type: Number,
    default: 10,
    min: 1,
    max: 100
  }
});

// 📊 MAIN SUGGESTION SCHEMA
const suggestionSchema = new mongoose.Schema({
  guildId: {
    type: String,
    required: true,
    unique: true
  },
  channelId: {
    type: String,
    required: true
  },

  // 💡 SUGGESTIONS ARRAY
  suggestions: [suggestionItemSchema],

  // ⚙️ CONFIGURATION
  config: {
    type: configSchema,
    default: () => ({
      anonymous: false,
      voting: true,
      cooldown: 5,
      categories: ['General', 'Bot Features', 'Server Improvements'],
      managerRole: null,
      reviewerRole: null,
      logChannel: null,
      dmNotifications: true,
      templates: [],
      autoArchiveDays: 90,
      requireApproval: false,
      maxSuggestionsPerUser: 10
    })
  },

  // 📈 ANALYTICS DATA
  analytics: {
    totalSuggestions: { type: Number, default: 0 },
    totalAccepted: { type: Number, default: 0 },
    totalDeclined: { type: Number, default: 0 },
    totalImplemented: { type: Number, default: 0 },
    averageResponseTime: { type: Number, default: 0 },
    lastReset: { type: Date, default: Date.now }
  },

  // 💾 BACKUP DATA
  backups: [{
    id: String,
    timestamp: { type: Date, default: Date.now },
    dataSize: Number,
    includeVotes: Boolean,
    createdBy: String
  }],

  // 📅 TIMESTAMPS
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 🔍 INDEXES FOR PERFORMANCE
suggestionSchema.index({ guildId: 1 });
suggestionSchema.index({ 'suggestions.messageId': 1 });
suggestionSchema.index({ 'suggestions.status': 1 });
suggestionSchema.index({ 'suggestions.authorId': 1 });
suggestionSchema.index({ 'suggestions.createdAt': -1 });
suggestionSchema.index({ 'suggestions.category': 1 });

// 🔄 MIDDLEWARE
suggestionSchema.pre('save', function(next) {
  this.updatedAt = new Date();

  // Update analytics
  const suggestions = this.suggestions;
  this.analytics.totalSuggestions = suggestions.length;
  this.analytics.totalAccepted = suggestions.filter(s => s.status === 'accepted').length;
  this.analytics.totalDeclined = suggestions.filter(s => s.status === 'declined').length;
  this.analytics.totalImplemented = suggestions.filter(s => s.status === 'implemented').length;

  next();
});

// 📊 METHODS
suggestionSchema.methods.getStats = function(period = 'all') {
  const suggestions = this.suggestions;
  let filtered = suggestions;

  if (period !== 'all') {
    const cutoff = new Date();
    switch (period) {
      case 'today':
        cutoff.setHours(0, 0, 0, 0);
        break;
      case 'week':
        cutoff.setDate(cutoff.getDate() - 7);
        break;
      case 'month':
        cutoff.setMonth(cutoff.getMonth() - 1);
        break;
    }
    filtered = suggestions.filter(s => s.createdAt >= cutoff);
  }

  return {
    total: filtered.length,
    accepted: filtered.filter(s => s.status === 'accepted').length,
    declined: filtered.filter(s => s.status === 'declined').length,
    pending: filtered.filter(s => s.status === 'pending').length,
    implemented: filtered.filter(s => s.status === 'implemented').length
  };
};

suggestionSchema.methods.getUserSuggestions = function(userId) {
  return this.suggestions.filter(s => s.authorId === userId);
};

suggestionSchema.methods.getSuggestionsByCategory = function(category) {
  return this.suggestions.filter(s => s.category === category);
};

module.exports = mongoose.model('Suggestion', suggestionSchema);