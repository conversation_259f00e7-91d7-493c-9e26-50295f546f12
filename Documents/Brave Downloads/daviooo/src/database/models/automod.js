const mongoose = require('mongoose');

// 🛡️ ENTERPRISE-GRADE AUTOMOD SCHEMA - PROFESSIONAL SECURITY SYSTEM
const autoModSchema = new mongoose.Schema({
  guildId: { type: String, required: true, unique: true },

  // 🚫 CONTENT FILTERS
  antiInvites: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'delete', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] },
    whitelist: { type: [String], default: [] }
  },
  antiLinks: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'delete', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] },
    whitelist: { type: [String], default: [] },
    allowedDomains: { type: [String], default: [] }
  },
  antiSpam: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'timeout', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] },
    messageLimit: { type: Number, default: 5 },
    timeWindow: { type: Number, default: 10 }
  },
  antiCaps: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'delete', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] },
    threshold: { type: Number, default: 70 }
  },
  antiScam: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'ban', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] }
  },
  flaggedWords: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'delete', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] },
    words: { type: [String], default: [] }
  },
  mentionSpam: {
    enabled: { type: Boolean, default: false },
    action: { type: String, default: 'timeout', enum: ['delete', 'warn', 'timeout', 'kick', 'ban'] },
    limit: { type: Number, default: 5 }
  },

  // 🔒 RAID PROTECTION
  raidProtection: {
    enabled: { type: Boolean, default: false },
    joinThreshold: { type: Number, default: 10 },
    timeWindow: { type: Number, default: 30 },
    action: { type: String, default: 'lockdown', enum: ['lockdown', 'kick', 'ban'] }
  },

  // ⚙️ GENERAL SETTINGS
  logChannel: { type: String, default: null },
  moderatorRoles: { type: [String], default: [] },
  immuneRoles: { type: [String], default: [] },
  immuneChannels: { type: [String], default: [] },

  // 📊 STATISTICS
  stats: {
    messagesDeleted: { type: Number, default: 0 },
    usersWarned: { type: Number, default: 0 },
    usersTimedOut: { type: Number, default: 0 },
    usersKicked: { type: Number, default: 0 },
    usersBanned: { type: Number, default: 0 },
    raidsBlocked: { type: Number, default: 0 }
  },

  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update timestamp on save
autoModSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('AutoMod', autoModSchema);