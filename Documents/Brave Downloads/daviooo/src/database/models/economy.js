// 🏗️ MINECRAFT-INSPIRED ECONOMY DATABASE MODEL - ENTERPRISE SYSTEM
// Complete economy system with currencies, items, jobs, businesses, and more

const mongoose = require('mongoose');

const economySchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true
  },
  guildId: {
    type: String,
    required: true
  },

  // 💰 MULTI-CURRENCY SYSTEM (Minecraft-inspired)
  coins: {
    type: Number,
    default: 100 // Main currency (like Gold)
  },
  gems: {
    type: Number,
    default: 0 // Premium currency (like Diamonds)
  },
  tokens: {
    type: Number,
    default: 0 // Special currency (like Emeralds)
  },
  bank: {
    type: Number,
    default: 0
  },

  // 📊 PROGRESSION SYSTEM
  xp: {
    type: Number,
    default: 0
  },
  level: {
    type: Number,
    default: 1
  },
  prestige: {
    type: Number,
    default: 0
  },

  // 🎒 INVENTORY SYSTEM (Minecraft-style)
  inventory: {
    // 🪵 BASIC RESOURCES
    wood: { type: Number, default: 0 },
    stone: { type: Number, default: 0 },
    iron: { type: Number, default: 0 },
    gold: { type: Number, default: 0 },
    diamond: { type: Number, default: 0 },

    // 🍞 FOOD ITEMS
    bread: { type: Number, default: 0 },
    apple: { type: Number, default: 0 },
    meat: { type: Number, default: 0 },

    // ⚔️ TOOLS & WEAPONS
    woodenSword: { type: Number, default: 0 },
    ironSword: { type: Number, default: 0 },
    diamondSword: { type: Number, default: 0 },
    pickaxe: { type: Number, default: 0 },

    // 🛡️ ARMOR
    helmet: { type: Number, default: 0 },
    chestplate: { type: Number, default: 0 },
    leggings: { type: Number, default: 0 },
    boots: { type: Number, default: 0 },

    // 🎁 SPECIAL ITEMS
    lootbox: { type: Number, default: 0 },
    key: { type: Number, default: 0 },
    potion: { type: Number, default: 0 }
  },

  // 💼 JOB SYSTEM
  job: {
    current: { type: String, default: null }, // miner, farmer, warrior, etc.
    level: { type: Number, default: 1 },
    experience: { type: Number, default: 0 },
    totalWorked: { type: Number, default: 0 }
  },

  // 🏢 BUSINESS SYSTEM
  business: {
    type: { type: String, default: null }, // shop, farm, mine, etc.
    level: { type: Number, default: 0 },
    income: { type: Number, default: 0 },
    employees: { type: Number, default: 0 },
    lastCollected: { type: Date, default: null }
  },

  // 🐾 PET SYSTEM
  pets: [{
    type: { type: String }, // dog, cat, dragon, etc.
    name: { type: String },
    level: { type: Number, default: 1 },
    happiness: { type: Number, default: 100 },
    lastFed: { type: Date, default: Date.now },
    earnings: { type: Number, default: 0 }
  }],

  // ⏰ COOLDOWNS
  lastDaily: { type: Date, default: null },
  lastWork: { type: Date, default: null },
  lastBeg: { type: Date, default: null },
  lastRob: { type: Date, default: null },
  lastMine: { type: Date, default: null },
  lastFarm: { type: Date, default: null },
  lastHunt: { type: Date, default: null },
  lastQuest: { type: Date, default: null },
  lastRaid: { type: Date, default: null },

  // 🎰 GAMBLING STATS
  gambling: {
    totalBet: { type: Number, default: 0 },
    totalWon: { type: Number, default: 0 },
    totalLost: { type: Number, default: 0 },
    winStreak: { type: Number, default: 0 },
    lossStreak: { type: Number, default: 0 }
  },

  // 📈 STATISTICS
  stats: {
    totalEarned: { type: Number, default: 0 },
    totalSpent: { type: Number, default: 0 },
    commandsUsed: { type: Number, default: 0 },
    itemsCrafted: { type: Number, default: 0 },
    itemsSold: { type: Number, default: 0 },
    questsCompleted: { type: Number, default: 0 },
    raidsCompleted: { type: Number, default: 0 }
  },

  // 🏆 ACHIEVEMENTS
  achievements: [{
    id: String,
    name: String,
    description: String,
    reward: Number,
    unlockedAt: { type: Date, default: Date.now }
  }],

  // 🎫 LOTTERY & EVENTS
  lottery: {
    tickets: { type: Number, default: 0 },
    totalSpent: { type: Number, default: 0 },
    totalWon: { type: Number, default: 0 }
  },

  // 📊 MARKET ACTIVITY
  market: {
    itemsSold: { type: Number, default: 0 },
    itemsBought: { type: Number, default: 0 },
    totalProfit: { type: Number, default: 0 }
  },

  // ⏰ TIMESTAMPS
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Create compound index for efficient queries
economySchema.index({ userId: 1, guildId: 1 }, { unique: true });

// Update timestamp on save
economySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Economy', economySchema);
