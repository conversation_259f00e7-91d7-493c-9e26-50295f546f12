// 💰 ECONOMY DATABASE MODEL - GUILD-BASED CURRENCY SYSTEM
// Professional economy system with XP, levels, and guild-specific data

const mongoose = require('mongoose');

const economySchema = new mongoose.Schema({
  userId: { 
    type: String, 
    required: true 
  },
  guildId: { 
    type: String, 
    required: true 
  },
  
  // 💰 CURRENCY SYSTEM
  balance: { 
    type: Number, 
    default: 100 // Starting balance
  },
  bank: { 
    type: Number, 
    default: 0 
  },
  
  // 📊 LEVEL SYSTEM
  xp: { 
    type: Number, 
    default: 0 
  },
  level: { 
    type: Number, 
    default: 1 
  },
  
  // ⏰ COOLDOWNS
  lastDaily: { 
    type: Date, 
    default: null 
  },
  lastWork: { 
    type: Date, 
    default: null 
  },
  lastBeg: { 
    type: Date, 
    default: null 
  },
  lastRob: { 
    type: Date, 
    default: null 
  },
  lastLootbox: { 
    type: Date, 
    default: null 
  },
  
  // 📈 STATISTICS
  totalEarned: { 
    type: Number, 
    default: 0 
  },
  totalSpent: { 
    type: Number, 
    default: 0 
  },
  commandsUsed: { 
    type: Number, 
    default: 0 
  },
  
  // 🎯 ACHIEVEMENTS
  achievements: [{
    name: String,
    unlockedAt: { type: Date, default: Date.now }
  }],
  
  // ⏰ TIMESTAMPS
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create compound index for efficient queries
economySchema.index({ userId: 1, guildId: 1 }, { unique: true });

// Update timestamp on save
economySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Economy', economySchema);
