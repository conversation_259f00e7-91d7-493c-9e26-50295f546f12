// 🤖 PROFESSIONAL CHATGPT DATABASE MODEL
// Enterprise-grade AI conversation management with advanced features

const mongoose = require('mongoose');

// 💬 CONVERSATION MESSAGE SCHEMA
const messageSchema = new mongoose.Schema({
  messageId: { 
    type: String, 
    required: true 
  },
  role: { 
    type: String, 
    enum: ['user', 'assistant', 'system'],
    required: true 
  },
  content: { 
    type: String, 
    required: true,
    maxlength: 4000 
  },
  authorId: { 
    type: String, 
    required: true 
  },
  model: { 
    type: String, 
    default: 'gpt-3.5-turbo' 
  },
  tokens: { 
    type: Number, 
    default: 0 
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  },
  
  // 📊 METADATA
  responseTime: { 
    type: Number, 
    default: 0 
  },
  cost: { 
    type: Number, 
    default: 0 
  },
  quality: { 
    type: Number, 
    min: 1,
    max: 5,
    default: null 
  }
});

// 🗣️ CONVERSATION THREAD SCHEMA
const conversationSchema = new mongoose.Schema({
  threadId: { 
    type: String, 
    required: true,
    unique: true 
  },
  authorId: { 
    type: String, 
    required: true 
  },
  topic: { 
    type: String, 
    maxlength: 200 
  },
  personality: { 
    type: String, 
    enum: ['assistant', 'friend', 'teacher', 'creative', 'technical'],
    default: 'assistant' 
  },
  model: { 
    type: String, 
    default: 'gpt-3.5-turbo' 
  },
  
  // 💬 MESSAGES
  messages: [messageSchema],
  
  // 📊 STATISTICS
  totalMessages: { 
    type: Number, 
    default: 0 
  },
  totalTokens: { 
    type: Number, 
    default: 0 
  },
  totalCost: { 
    type: Number, 
    default: 0 
  },
  averageResponseTime: { 
    type: Number, 
    default: 0 
  },
  
  // 📅 TIMESTAMPS
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  lastActivity: { 
    type: Date, 
    default: Date.now 
  },
  
  // 🏷️ STATUS
  active: { 
    type: Boolean, 
    default: true 
  },
  archived: { 
    type: Boolean, 
    default: false 
  }
});

// 👤 USER USAGE SCHEMA
const userUsageSchema = new mongoose.Schema({
  userId: { 
    type: String, 
    required: true 
  },
  
  // 📊 DAILY STATISTICS
  daily: {
    messages: { type: Number, default: 0 },
    tokens: { type: Number, default: 0 },
    cost: { type: Number, default: 0 },
    date: { type: Date, default: Date.now }
  },
  
  // 📈 TOTAL STATISTICS
  total: {
    messages: { type: Number, default: 0 },
    tokens: { type: Number, default: 0 },
    cost: { type: Number, default: 0 },
    conversations: { type: Number, default: 0 }
  },
  
  // ⚙️ USER PREFERENCES
  preferences: {
    defaultModel: { type: String, default: 'gpt-3.5-turbo' },
    defaultPersonality: { type: String, default: 'assistant' },
    privateByDefault: { type: Boolean, default: false },
    notifications: { type: Boolean, default: true }
  },
  
  // 🚫 LIMITS & RESTRICTIONS
  limits: {
    dailyMessages: { type: Number, default: 50 },
    maxTokensPerMessage: { type: Number, default: 2000 },
    cooldownSeconds: { type: Number, default: 5 }
  },
  
  // 📅 TIMESTAMPS
  firstUsed: { 
    type: Date, 
    default: Date.now 
  },
  lastUsed: { 
    type: Date, 
    default: Date.now 
  }
});

// ⚙️ SYSTEM CONFIGURATION SCHEMA
const configSchema = new mongoose.Schema({
  // 🏗️ BASIC SETTINGS
  channelId: { 
    type: String, 
    required: true 
  },
  model: { 
    type: String, 
    enum: ['gpt-4', 'gpt-3.5-turbo', 'claude'],
    default: 'gpt-3.5-turbo' 
  },
  personality: { 
    type: String, 
    enum: ['assistant', 'friend', 'teacher', 'creative', 'technical'],
    default: 'assistant' 
  },
  
  // 🔑 API CONFIGURATION
  apiKey: { 
    type: String, 
    default: null 
  },
  apiProvider: { 
    type: String, 
    enum: ['openai', 'anthropic'],
    default: 'openai' 
  },
  
  // 📊 LIMITS
  dailyLimit: { 
    type: Number, 
    default: 50,
    min: 1,
    max: 1000 
  },
  maxTokens: { 
    type: Number, 
    default: 2000,
    min: 100,
    max: 4000 
  },
  cooldown: { 
    type: Number, 
    default: 5,
    min: 0,
    max: 300 
  },
  
  // 🎛️ FEATURES
  conversationMemory: { 
    type: Boolean, 
    default: true 
  },
  autoModeration: { 
    type: Boolean, 
    default: true 
  },
  logConversations: { 
    type: Boolean, 
    default: true 
  },
  allowPrivateChats: { 
    type: Boolean, 
    default: true 
  },
  
  // 🔔 NOTIFICATIONS
  logChannel: { 
    type: String, 
    default: null 
  },
  notifyAdmins: { 
    type: Boolean, 
    default: false 
  },
  
  // 🛡️ MODERATION
  contentFilter: { 
    type: Boolean, 
    default: true 
  },
  spamProtection: { 
    type: Boolean, 
    default: true 
  },
  blacklistedWords: [{ 
    type: String, 
    maxlength: 50 
  }],
  
  // 👑 PREMIUM FEATURES
  premiumEnabled: { 
    type: Boolean, 
    default: false 
  },
  premiumModels: { 
    type: Boolean, 
    default: false 
  }
});

// 📊 MAIN CHATGPT SCHEMA
const chatgptSchema = new mongoose.Schema({
  guildId: { 
    type: String, 
    required: true,
    unique: true 
  },
  
  // ⚙️ CONFIGURATION
  config: {
    type: configSchema,
    default: () => ({
      model: 'gpt-3.5-turbo',
      personality: 'assistant',
      dailyLimit: 50,
      maxTokens: 2000,
      cooldown: 5,
      conversationMemory: true,
      autoModeration: true,
      logConversations: true,
      allowPrivateChats: true,
      contentFilter: true,
      spamProtection: true,
      blacklistedWords: [],
      premiumEnabled: false,
      premiumModels: false
    })
  },
  
  // 🗣️ CONVERSATIONS
  conversations: [conversationSchema],
  
  // 👥 USER USAGE
  userUsage: [userUsageSchema],
  
  // 📈 ANALYTICS
  analytics: {
    totalConversations: { type: Number, default: 0 },
    totalMessages: { type: Number, default: 0 },
    totalTokens: { type: Number, default: 0 },
    totalCost: { type: Number, default: 0 },
    averageResponseTime: { type: Number, default: 0 },
    popularModel: { type: String, default: 'gpt-3.5-turbo' },
    popularPersonality: { type: String, default: 'assistant' },
    activeUsers: { type: Number, default: 0 },
    lastReset: { type: Date, default: Date.now }
  },
  
  // 📅 TIMESTAMPS
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// 🔍 INDEXES FOR PERFORMANCE
chatgptSchema.index({ guildId: 1 });
chatgptSchema.index({ 'conversations.threadId': 1 });
chatgptSchema.index({ 'conversations.authorId': 1 });
chatgptSchema.index({ 'userUsage.userId': 1 });
chatgptSchema.index({ 'conversations.lastActivity': -1 });

// 🔄 MIDDLEWARE
chatgptSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Update analytics
  this.analytics.totalConversations = this.conversations.length;
  this.analytics.totalMessages = this.conversations.reduce((sum, conv) => sum + conv.totalMessages, 0);
  this.analytics.totalTokens = this.conversations.reduce((sum, conv) => sum + conv.totalTokens, 0);
  this.analytics.activeUsers = new Set(this.userUsage.map(u => u.userId)).size;
  
  next();
});

// 📊 METHODS
chatgptSchema.methods.getUserUsage = function(userId) {
  return this.userUsage.find(u => u.userId === userId);
};

chatgptSchema.methods.getActiveConversations = function() {
  return this.conversations.filter(c => c.active && !c.archived);
};

chatgptSchema.methods.getUserConversations = function(userId) {
  return this.conversations.filter(c => c.authorId === userId);
};

chatgptSchema.methods.getStats = function(period = 'all') {
  const conversations = this.conversations;
  let filtered = conversations;
  
  if (period !== 'all') {
    const cutoff = new Date();
    switch (period) {
      case 'today':
        cutoff.setHours(0, 0, 0, 0);
        break;
      case 'week':
        cutoff.setDate(cutoff.getDate() - 7);
        break;
      case 'month':
        cutoff.setMonth(cutoff.getMonth() - 1);
        break;
    }
    filtered = conversations.filter(c => c.lastActivity >= cutoff);
  }
  
  return {
    conversations: filtered.length,
    messages: filtered.reduce((sum, c) => sum + c.totalMessages, 0),
    tokens: filtered.reduce((sum, c) => sum + c.totalTokens, 0),
    cost: filtered.reduce((sum, c) => sum + c.totalCost, 0)
  };
};

module.exports = mongoose.model('ChatGPT', chatgptSchema);
