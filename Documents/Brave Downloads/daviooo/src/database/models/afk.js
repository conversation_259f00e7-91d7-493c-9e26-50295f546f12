// 😴 AFK DATABASE MODEL - AWAY FROM KEYBOARD SYSTEM
// Professional AFK status tracking and management

const mongoose = require('mongoose');

const afkSchema = new mongoose.Schema({
  userId: { 
    type: String, 
    required: true 
  },
  guildId: { 
    type: String, 
    required: true 
  },
  reason: { 
    type: String, 
    default: 'No reason provided',
    maxlength: 500
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  }
});

// Create compound index for efficient queries
afkSchema.index({ userId: 1, guildId: 1 }, { unique: true });

module.exports = mongoose.model('AFK', afkSchema);
