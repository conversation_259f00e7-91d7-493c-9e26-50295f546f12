// 📊 SERVER STATISTICS DATABASE MODEL - ANALYTICS SYSTEM
// Professional server analytics and statistics tracking

const mongoose = require('mongoose');

const serverStatsSchema = new mongoose.Schema({
  guildId: { 
    type: String, 
    required: true, 
    unique: true 
  },
  
  // 📈 TOTAL STATISTICS
  totalMessages: { 
    type: Number, 
    default: 0 
  },
  totalCommands: { 
    type: Number, 
    default: 0 
  },
  totalMembers: { 
    type: Number, 
    default: 0 
  },
  
  // 📅 DAILY STATISTICS
  dailyStats: {
    type: Map,
    of: {
      messages: { type: Number, default: 0 },
      commands: { type: Number, default: 0 },
      activeUsers: { type: Number, default: 0 },
      newMembers: { type: Number, default: 0 },
      leftMembers: { type: Number, default: 0 }
    },
    default: new Map()
  },
  
  // 👤 USER STATISTICS
  userStats: {
    type: Map,
    of: {
      totalMessages: { type: Number, default: 0 },
      totalCommands: { type: Number, default: 0 },
      lastActive: { type: Date, default: Date.now },
      joinDate: { type: Date, default: Date.now }
    },
    default: new Map()
  },
  
  // 📝 CHANNEL STATISTICS
  channelStats: {
    type: Map,
    of: {
      totalMessages: { type: Number, default: 0 },
      name: { type: String, default: 'Unknown' },
      type: { type: String, default: 'text' }
    },
    default: new Map()
  },
  
  // 🎯 COMMAND STATISTICS
  commandStats: {
    type: Map,
    of: {
      uses: { type: Number, default: 0 },
      lastUsed: { type: Date, default: Date.now }
    },
    default: new Map()
  },
  
  // ⏰ TIMESTAMPS
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Update timestamp on save
serverStatsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('ServerStats', serverStatsSchema);
