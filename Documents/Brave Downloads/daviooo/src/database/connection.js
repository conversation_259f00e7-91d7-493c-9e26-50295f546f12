// 🗄️ PROFESSIONAL DATABASE CONNECTION MANAGER - ACADEMIC EXCELLENCE
// Advanced MongoDB connection with comprehensive error handling and monitoring

const mongoose = require('mongoose');
const logger = require('../utils/logger');

class DatabaseManager {
  constructor() {
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 5;
    this.retryDelay = 5000; // 5 seconds
    this.stats = {
      connectTime: null,
      totalQueries: 0,
      failedQueries: 0,
      avgResponseTime: 0
    };
  }

  async connect() {
    if (this.isConnected) {
      logger.warning('Database already connected');
      return;
    }

    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/davio-bot';
    
    logger.startup('Connecting to MongoDB...');
    
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4,
      retryWrites: true,
      w: 'majority'
    };

    try {
      const startTime = Date.now();
      
      await mongoose.connect(mongoUri, options);
      
      this.stats.connectTime = Date.now() - startTime;
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      logger.success(`MongoDB connected successfully (${this.stats.connectTime}ms)`);
      logger.info(`Database: ${mongoose.connection.name}`);
      logger.info(`Host: ${mongoose.connection.host}:${mongoose.connection.port}`);
      
      this.setupEventListeners();
      this.setupQueryMonitoring();
      
    } catch (error) {
      this.connectionAttempts++;
      logger.error(`MongoDB connection failed (attempt ${this.connectionAttempts}/${this.maxRetries})`, error.message);
      
      if (this.connectionAttempts < this.maxRetries) {
        logger.info(`Retrying connection in ${this.retryDelay / 1000} seconds...`);
        setTimeout(() => this.connect(), this.retryDelay);
      } else {
        logger.error('Max connection attempts reached. Database unavailable.');
        throw new Error('Database connection failed after maximum retries');
      }
    }
  }

  setupEventListeners() {
    const db = mongoose.connection;

    db.on('connected', () => {
      logger.success('Mongoose connected to MongoDB');
    });

    db.on('error', (error) => {
      logger.error('MongoDB connection error', error.message);
      this.isConnected = false;
    });

    db.on('disconnected', () => {
      logger.warning('MongoDB disconnected');
      this.isConnected = false;
      
      // Attempt to reconnect
      if (this.connectionAttempts < this.maxRetries) {
        logger.info('Attempting to reconnect...');
        setTimeout(() => this.connect(), this.retryDelay);
      }
    });

    db.on('reconnected', () => {
      logger.success('MongoDB reconnected');
      this.isConnected = true;
    });

    // Handle process termination
    process.on('SIGINT', async () => {
      await this.disconnect();
      process.exit(0);
    });
  }

  setupQueryMonitoring() {
    // Monitor database queries for performance
    mongoose.set('debug', (collectionName, method, query, doc) => {
      this.stats.totalQueries++;
      
      // Log slow queries (>100ms)
      const startTime = Date.now();
      
      // This is a simplified monitoring - in production you'd want more sophisticated tracking
      if (process.env.NODE_ENV === 'development') {
        logger.info(`DB Query: ${collectionName}.${method}`, JSON.stringify(query));
      }
    });
  }

  async disconnect() {
    if (!this.isConnected) {
      logger.warning('Database not connected');
      return;
    }

    try {
      await mongoose.connection.close();
      this.isConnected = false;
      logger.success('MongoDB disconnected gracefully');
    } catch (error) {
      logger.error('Error disconnecting from MongoDB', error.message);
    }
  }

  // 📊 Get database statistics
  getStats() {
    return {
      isConnected: this.isConnected,
      connectionTime: this.stats.connectTime,
      totalQueries: this.stats.totalQueries,
      failedQueries: this.stats.failedQueries,
      database: mongoose.connection.name,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      readyState: mongoose.connection.readyState,
      collections: Object.keys(mongoose.connection.collections)
    };
  }

  // 🔍 Health check
  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', healthy: false };
      }

      // Simple ping to check connection
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'connected',
        healthy: true,
        responseTime: Date.now(),
        stats: this.getStats()
      };
    } catch (error) {
      logger.error('Database health check failed', error.message);
      return {
        status: 'error',
        healthy: false,
        error: error.message
      };
    }
  }

  // 🧹 Database maintenance
  async performMaintenance() {
    if (!this.isConnected) {
      logger.warning('Cannot perform maintenance - database not connected');
      return;
    }

    logger.info('Starting database maintenance...');
    
    try {
      // Clean up old giveaways (older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      
      const Giveaway = require('./models/Giveaway');
      const deletedGiveaways = await Giveaway.deleteMany({
        endsAt: { $lt: sixMonthsAgo },
        ended: true
      });
      
      if (deletedGiveaways.deletedCount > 0) {
        logger.success(`Cleaned up ${deletedGiveaways.deletedCount} old giveaways`);
      }
      
      // Add more maintenance tasks here as needed
      
      logger.success('Database maintenance completed');
    } catch (error) {
      logger.error('Database maintenance failed', error.message);
    }
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();

module.exports = databaseManager;
