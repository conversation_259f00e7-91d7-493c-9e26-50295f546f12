// Deploys all slash commands to Discord
const { REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');

async function deployCommands(client) {
  const commands = [];
  const commandsPath = path.join(__dirname, 'commands');
  const categories = fs.readdirSync(commandsPath);

  console.log('🔍 Scanning command categories...');

  for (const category of categories) {
    const categoryPath = path.join(commandsPath, category);
    if (!fs.lstatSync(categoryPath).isDirectory()) continue;

    // Look for index.js in each category
    const indexPath = path.join(categoryPath, 'index.js');
    if (fs.existsSync(indexPath)) {
      try {
        const command = require(indexPath);
        if (command.data && command.data.toJSON) {
          commands.push(command.data.toJSON());
          console.log(`✅ Loaded command: ${command.data.name} (${category})`);
        } else {
          console.log(`⚠️ Invalid command structure in ${category}/index.js`);
        }
      } catch (error) {
        console.error(`❌ Error loading ${category}/index.js:`, error.message);
      }
    } else {
      console.log(`⚠️ No index.js found in ${category}/`);
    }
  }

  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  try {
    console.log(`\n🌐 Registering ${commands.length} commands globally...`);
    await rest.put(Routes.applicationCommands(process.env.CLIENT_ID), { body: commands });
    console.log('✅ All commands registered successfully!\n');
  } catch (error) {
    console.error('❌ Error registering commands:', error);
  }
}

module.exports = { deployCommands };