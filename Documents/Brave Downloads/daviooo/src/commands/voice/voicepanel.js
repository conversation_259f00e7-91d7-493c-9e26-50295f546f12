// 🎛️ VOICE PANEL COMMAND - INTERACTIVE VOICE CONTROL
// Professional interactive voice management panel

const { Slash<PERSON><PERSON><PERSON><PERSON>uilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits, ComponentType } = require('discord.js');

// 🎨 PROFESSIONAL COLORS & EMOJIS
const COLORS = {
  SUCCESS: '#00D166',
  ERROR: '#FF3838',
  INFO: '#00B4D8',
  VOICE: '#7209B7'
};

const EMOJIS = {
  PANEL: '🎛️',
  MUTE: '🔇',
  UNMUTE: '🔊',
  DEAFEN: '🔕',
  UNDEAFEN: '🔉',
  LOCK: '🔒',
  UNLOCK: '🔓',
  DISCONNECT: '📞',
  REFRESH: '🔄',
  SETTINGS: '⚙️'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('voicepanel')
    .setDescription('🎛️ Interactive voice channel control panel')
    .setDefaultMemberPermissions(PermissionFlagsBits.MoveMembers),

  async execute(interaction) {
    try {
      // Check if user is in voice channel
      if (!interaction.member.voice.channel) {
        return interaction.reply({
          content: `${EMOJIS.ERROR} You must be in a voice channel to use the voice panel!`,
          ephemeral: true
        });
      }

      // Check permissions
      if (!interaction.member.permissions.has(PermissionFlagsBits.MoveMembers)) {
        return interaction.reply({
          content: `${EMOJIS.ERROR} You need **Move Members** permission to use the voice panel!`,
          ephemeral: true
        });
      }

      const voiceChannel = interaction.member.voice.channel;
      const embed = createPanelEmbed(voiceChannel);
      const buttons = createPanelButtons();

      const response = await interaction.reply({
        embeds: [embed],
        components: [buttons],
        ephemeral: true
      });

      // Create collector for button interactions
      const collector = response.createMessageComponentCollector({
        componentType: ComponentType.Button,
        time: 300000 // 5 minutes
      });

      collector.on('collect', async (buttonInteraction) => {
        if (buttonInteraction.user.id !== interaction.user.id) {
          return buttonInteraction.reply({
            content: `${EMOJIS.ERROR} Only the command user can use these buttons!`,
            ephemeral: true
          });
        }

        await handleButtonInteraction(buttonInteraction, voiceChannel);
      });

      collector.on('end', async () => {
        try {
          const disabledButtons = createPanelButtons(true);
          await interaction.editReply({
            components: [disabledButtons]
          });
        } catch (error) {
          console.error('Error disabling buttons:', error);
        }
      });

    } catch (error) {
      console.error('Voice Panel Error:', error);
      return interaction.reply({
        content: `${EMOJIS.ERROR} **Error:** Failed to create voice panel.`,
        ephemeral: true
      });
    }
  }
};

// 🎛️ CREATE PANEL EMBED
function createPanelEmbed(voiceChannel) {
  const members = voiceChannel.members;
  const memberList = members.size > 0 
    ? members.map(member => {
        const status = [];
        if (member.voice.mute) status.push('🔇');
        if (member.voice.deaf) status.push('🔕');
        if (member.voice.streaming) status.push('📺');
        if (member.voice.selfVideo) status.push('📹');
        return `${status.join('')} ${member.displayName}`;
      }).join('\n')
    : 'No users in channel';

  return new EmbedBuilder()
    .setColor(COLORS.VOICE)
    .setTitle(`${EMOJIS.PANEL} Voice Control Panel`)
    .setDescription(`**${voiceChannel.name}** Management`)
    .addFields(
      {
        name: '📊 Channel Info',
        value: [
          `👥 **Users:** ${members.size}${voiceChannel.userLimit > 0 ? `/${voiceChannel.userLimit}` : ''}`,
          `🔒 **Status:** ${voiceChannel.permissionsFor(voiceChannel.guild.roles.everyone).has(PermissionFlagsBits.Connect) ? 'Unlocked' : 'Locked'}`,
          `📍 **Category:** ${voiceChannel.parent?.name || 'None'}`
        ].join('\n'),
        inline: true
      },
      {
        name: '👥 Members',
        value: memberList.length > 1024 ? `${memberList.substring(0, 1020)}...` : memberList,
        inline: false
      }
    )
    .setTimestamp()
    .setFooter({ text: 'Panel expires in 5 minutes' });
}

// 🔘 CREATE PANEL BUTTONS
function createPanelButtons(disabled = false) {
  return new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('voice_mute_all')
        .setLabel('Mute All')
        .setEmoji(EMOJIS.MUTE)
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(disabled),
      new ButtonBuilder()
        .setCustomId('voice_unmute_all')
        .setLabel('Unmute All')
        .setEmoji(EMOJIS.UNMUTE)
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(disabled),
      new ButtonBuilder()
        .setCustomId('voice_lock')
        .setLabel('Lock')
        .setEmoji(EMOJIS.LOCK)
        .setStyle(ButtonStyle.Danger)
        .setDisabled(disabled),
      new ButtonBuilder()
        .setCustomId('voice_unlock')
        .setLabel('Unlock')
        .setEmoji(EMOJIS.UNLOCK)
        .setStyle(ButtonStyle.Success)
        .setDisabled(disabled),
      new ButtonBuilder()
        .setCustomId('voice_refresh')
        .setLabel('Refresh')
        .setEmoji(EMOJIS.REFRESH)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(disabled)
    );
}

// 🎯 HANDLE BUTTON INTERACTIONS
async function handleButtonInteraction(interaction, voiceChannel) {
  await interaction.deferUpdate();

  try {
    const action = interaction.customId.replace('voice_', '');
    let result = '';
    let color = COLORS.SUCCESS;

    switch (action) {
      case 'mute_all':
        const muteResults = await performMassAction(voiceChannel, 'mute');
        result = `${EMOJIS.MUTE} Muted ${muteResults.success} users${muteResults.failed > 0 ? ` (${muteResults.failed} failed)` : ''}`;
        break;

      case 'unmute_all':
        const unmuteResults = await performMassAction(voiceChannel, 'unmute');
        result = `${EMOJIS.UNMUTE} Unmuted ${unmuteResults.success} users${unmuteResults.failed > 0 ? ` (${unmuteResults.failed} failed)` : ''}`;
        break;

      case 'lock':
        await voiceChannel.permissionOverwrites.edit(voiceChannel.guild.roles.everyone, {
          Connect: false
        });
        result = `${EMOJIS.LOCK} Locked **${voiceChannel.name}**`;
        break;

      case 'unlock':
        await voiceChannel.permissionOverwrites.edit(voiceChannel.guild.roles.everyone, {
          Connect: null
        });
        result = `${EMOJIS.UNLOCK} Unlocked **${voiceChannel.name}**`;
        break;

      case 'refresh':
        result = `${EMOJIS.REFRESH} Panel refreshed`;
        color = COLORS.INFO;
        break;
    }

    // Update the embed with new information
    const updatedEmbed = createPanelEmbed(voiceChannel);
    if (result) {
      updatedEmbed.addFields({
        name: '✅ Last Action',
        value: result,
        inline: false
      });
      updatedEmbed.setColor(color);
    }

    const buttons = createPanelButtons();
    await interaction.editReply({
      embeds: [updatedEmbed],
      components: [buttons]
    });

  } catch (error) {
    console.error('Button Interaction Error:', error);
    
    const errorEmbed = new EmbedBuilder()
      .setColor(COLORS.ERROR)
      .setTitle(`${EMOJIS.ERROR} Action Failed`)
      .setDescription('Failed to execute the requested action. Check bot permissions!')
      .setTimestamp();

    await interaction.editReply({
      embeds: [errorEmbed],
      components: [createPanelButtons()]
    });
  }
}

// 🎯 PERFORM MASS ACTIONS
async function performMassAction(voiceChannel, action) {
  const members = voiceChannel.members;
  let success = 0;
  let failed = 0;

  for (const [memberId, member] of members) {
    try {
      switch (action) {
        case 'mute':
          await member.voice.setMute(true, 'Voice panel mass action');
          break;
        case 'unmute':
          await member.voice.setMute(false, 'Voice panel mass action');
          break;
        case 'deafen':
          await member.voice.setDeaf(true, 'Voice panel mass action');
          break;
        case 'undeafen':
          await member.voice.setDeaf(false, 'Voice panel mass action');
          break;
      }
      success++;
    } catch (error) {
      failed++;
      console.error(`Failed to ${action} ${member.user.tag}:`, error);
    }
  }

  return { success, failed };
}
