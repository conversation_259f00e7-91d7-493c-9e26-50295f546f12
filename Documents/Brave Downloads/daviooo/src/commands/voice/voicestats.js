// 📊 VOICE STATISTICS COMMAND - ADVANCED VOICE ANALYTICS
// Professional voice channel statistics and user tracking

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');

// 🎨 PROFESSIONAL COLORS & EMOJIS
const COLORS = {
  SUCCESS: '#00D166',
  INFO: '#00B4D8',
  VOICE: '#7209B7'
};

const EMOJIS = {
  STATS: '📊',
  VOICE: '🎵',
  USERS: '👥',
  TIME: '⏰',
  CROWN: '👑',
  MICROPHONE: '🎤',
  SPEAKER: '🔈',
  CHART: '📈'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('voicestats')
    .setDescription('📊 Advanced voice channel statistics and analytics')
    .addSubcommand(subcommand =>
      subcommand.setName('server')
        .setDescription('📊 Get server-wide voice statistics'))
    .addSubcommand(subcommand =>
      subcommand.setName('channel')
        .setDescription('📊 Get specific channel voice statistics')
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Voice channel to analyze')
            .addChannelTypes(ChannelType.GuildVoice)
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand.setName('user')
        .setDescription('📊 Get user voice statistics')
        .addUserOption(option =>
          option.setName('user')
            .setDescription('User to analyze (yourself if not specified)')))
    .addSubcommand(subcommand =>
      subcommand.setName('leaderboard')
        .setDescription('🏆 Voice activity leaderboard')
        .addStringOption(option =>
          option.setName('type')
            .setDescription('Leaderboard type')
            .addChoices(
              { name: '🎤 Most Active Users', value: 'active' },
              { name: '⏰ Longest Sessions', value: 'sessions' },
              { name: '📊 Channel Usage', value: 'channels' }
            )
            .setRequired(true))),

  async execute(interaction) {
    try {
      const subcommand = interaction.options.getSubcommand();
      
      switch (subcommand) {
        case 'server':
          return await handleServerStats(interaction);
        case 'channel':
          return await handleChannelStats(interaction);
        case 'user':
          return await handleUserStats(interaction);
        case 'leaderboard':
          return await handleLeaderboard(interaction);
      }
      
    } catch (error) {
      console.error('Voice Stats Error:', error);
      return interaction.reply({
        content: `${EMOJIS.ERROR} **Error:** Failed to retrieve voice statistics.`,
        ephemeral: true
      });
    }
  }
};

// 📊 SERVER STATISTICS
async function handleServerStats(interaction) {
  const guild = interaction.guild;
  const voiceChannels = guild.channels.cache.filter(ch => ch.type === ChannelType.GuildVoice);
  
  let totalUsers = 0;
  let mutedUsers = 0;
  let deafenedUsers = 0;
  let streamingUsers = 0;
  let cameraUsers = 0;
  
  const channelData = [];
  
  voiceChannels.forEach(channel => {
    const members = channel.members;
    totalUsers += members.size;
    
    members.forEach(member => {
      if (member.voice.mute) mutedUsers++;
      if (member.voice.deaf) deafenedUsers++;
      if (member.voice.streaming) streamingUsers++;
      if (member.voice.selfVideo) cameraUsers++;
    });
    
    if (members.size > 0) {
      channelData.push({
        name: channel.name,
        users: members.size,
        limit: channel.userLimit || '∞'
      });
    }
  });
  
  // Sort channels by user count
  channelData.sort((a, b) => b.users - a.users);
  const topChannels = channelData.slice(0, 5);
  
  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.STATS} Server Voice Statistics`)
    .setDescription(`**${guild.name}** Voice Activity Overview`)
    .addFields(
      { 
        name: '📊 General Statistics', 
        value: [
          `${EMOJIS.VOICE} **Total Channels:** ${voiceChannels.size}`,
          `${EMOJIS.USERS} **Active Users:** ${totalUsers}`,
          `🔇 **Muted Users:** ${mutedUsers}`,
          `🔕 **Deafened Users:** ${deafenedUsers}`,
          `📺 **Streaming:** ${streamingUsers}`,
          `📹 **Camera On:** ${cameraUsers}`
        ].join('\n'),
        inline: false
      }
    );
  
  if (topChannels.length > 0) {
    embed.addFields({
      name: '🏆 Most Active Channels',
      value: topChannels.map((ch, i) => 
        `**${i + 1}.** ${ch.name} - ${ch.users}/${ch.limit} users`
      ).join('\n'),
      inline: false
    });
  }
  
  embed.setTimestamp()
    .setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });
  
  return interaction.reply({ embeds: [embed] });
}

// 📊 CHANNEL STATISTICS
async function handleChannelStats(interaction) {
  const channel = interaction.options.getChannel('channel');
  const members = channel.members;
  
  if (members.size === 0) {
    return interaction.reply({
      content: `${EMOJIS.VOICE} **${channel.name}** is currently empty.`,
      ephemeral: true
    });
  }
  
  let mutedCount = 0;
  let deafenedCount = 0;
  let streamingCount = 0;
  let cameraCount = 0;
  
  const memberList = members.map(member => {
    if (member.voice.mute) mutedCount++;
    if (member.voice.deaf) deafenedCount++;
    if (member.voice.streaming) streamingCount++;
    if (member.voice.selfVideo) cameraCount++;
    
    const status = [];
    if (member.voice.mute) status.push('🔇');
    if (member.voice.deaf) status.push('🔕');
    if (member.voice.streaming) status.push('📺');
    if (member.voice.selfVideo) status.push('📹');
    
    return `${status.join('')} ${member.displayName}`;
  });
  
  const embed = new EmbedBuilder()
    .setColor(COLORS.VOICE)
    .setTitle(`${EMOJIS.VOICE} Channel Statistics`)
    .setDescription(`**${channel.name}** Activity Details`)
    .addFields(
      {
        name: '📊 Channel Overview',
        value: [
          `${EMOJIS.USERS} **Users:** ${members.size}${channel.userLimit > 0 ? `/${channel.userLimit}` : ''}`,
          `🔇 **Muted:** ${mutedCount}`,
          `🔕 **Deafened:** ${deafenedCount}`,
          `📺 **Streaming:** ${streamingCount}`,
          `📹 **Camera:** ${cameraCount}`,
          `🔒 **Status:** ${channel.permissionsFor(interaction.guild.roles.everyone).has(PermissionFlagsBits.Connect) ? 'Unlocked' : 'Locked'}`
        ].join('\n'),
        inline: true
      },
      {
        name: '👥 Current Members',
        value: memberList.join('\n').substring(0, 1024),
        inline: false
      }
    )
    .setTimestamp()
    .setFooter({ text: `Channel ID: ${channel.id}` });
  
  return interaction.reply({ embeds: [embed] });
}

// 👤 USER STATISTICS
async function handleUserStats(interaction) {
  const targetUser = interaction.options.getUser('user') || interaction.user;
  const member = await interaction.guild.members.fetch(targetUser.id).catch(() => null);
  
  if (!member) {
    return interaction.reply({
      content: `${EMOJIS.ERROR} User not found in this server!`,
      ephemeral: true
    });
  }
  
  const voiceState = member.voice;
  const isInVoice = !!voiceState.channel;
  
  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.MICROPHONE} User Voice Statistics`)
    .setDescription(`**${targetUser.tag}** Voice Activity`)
    .addFields(
      {
        name: '📊 Current Status',
        value: [
          `${EMOJIS.VOICE} **In Voice:** ${isInVoice ? `Yes (${voiceState.channel.name})` : 'No'}`,
          `🔇 **Muted:** ${voiceState.mute ? 'Yes' : 'No'}`,
          `🔕 **Deafened:** ${voiceState.deaf ? 'Yes' : 'No'}`,
          `📺 **Streaming:** ${voiceState.streaming ? 'Yes' : 'No'}`,
          `📹 **Camera:** ${voiceState.selfVideo ? 'Yes' : 'No'}`
        ].join('\n'),
        inline: true
      }
    )
    .setThumbnail(targetUser.displayAvatarURL())
    .setTimestamp()
    .setFooter({ text: `User ID: ${targetUser.id}` });
  
  return interaction.reply({ embeds: [embed] });
}

// 🏆 LEADERBOARD
async function handleLeaderboard(interaction) {
  const type = interaction.options.getString('type');
  const guild = interaction.guild;
  
  let embed;
  
  switch (type) {
    case 'active':
      embed = await createActiveUsersLeaderboard(guild);
      break;
    case 'channels':
      embed = await createChannelUsageLeaderboard(guild);
      break;
    default:
      embed = new EmbedBuilder()
        .setColor(COLORS.INFO)
        .setTitle('🏆 Voice Leaderboard')
        .setDescription('Leaderboard feature coming soon!');
  }
  
  return interaction.reply({ embeds: [embed] });
}

// 🏆 ACTIVE USERS LEADERBOARD
async function createActiveUsersLeaderboard(guild) {
  const voiceChannels = guild.channels.cache.filter(ch => ch.type === ChannelType.GuildVoice);
  const userActivity = new Map();
  
  voiceChannels.forEach(channel => {
    channel.members.forEach(member => {
      const current = userActivity.get(member.id) || 0;
      userActivity.set(member.id, current + 1);
    });
  });
  
  const sortedUsers = Array.from(userActivity.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);
  
  const leaderboard = sortedUsers.map((entry, index) => {
    const member = guild.members.cache.get(entry[0]);
    const medal = index < 3 ? ['🥇', '🥈', '🥉'][index] : `**${index + 1}.**`;
    return `${medal} ${member?.displayName || 'Unknown User'} - Active in ${entry[1]} channel${entry[1] > 1 ? 's' : ''}`;
  }).join('\n');
  
  return new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle('🏆 Most Active Voice Users')
    .setDescription(leaderboard || 'No voice activity found!')
    .setTimestamp();
}

// 📊 CHANNEL USAGE LEADERBOARD
async function createChannelUsageLeaderboard(guild) {
  const voiceChannels = guild.channels.cache.filter(ch => ch.type === ChannelType.GuildVoice);
  
  const channelData = voiceChannels.map(channel => ({
    name: channel.name,
    users: channel.members.size,
    limit: channel.userLimit || '∞'
  })).sort((a, b) => b.users - a.users).slice(0, 10);
  
  const leaderboard = channelData.map((channel, index) => {
    const medal = index < 3 ? ['🥇', '🥈', '🥉'][index] : `**${index + 1}.**`;
    return `${medal} ${channel.name} - ${channel.users}/${channel.limit} users`;
  }).join('\n');
  
  return new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle('📊 Most Popular Voice Channels')
    .setDescription(leaderboard || 'No voice channels found!')
    .setTimestamp();
}
