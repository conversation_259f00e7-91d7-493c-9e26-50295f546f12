const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const Suggestion = require('../../database/models/suggestion');

// 🎨 PROFESSIONAL DESIGN CONSTANTS
const COLORS = {
  PRIMARY: '#3498db',
  SUCCESS: '#2ecc71',
  WARNING: '#f39c12',
  ERROR: '#e74c3c',
  INFO: '#9b59b6',
  SUGGESTION: '#1abc9c',
  PENDING: '#95a5a6'
};

const EMOJIS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  IDEA: '💡',
  ID: '🆔',
  CATEGORY: '📂',
  ANONYMOUS: '🕶️',
  VOTE: '🗳️',
  TIME: '⏰',
  USER: '👤'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('suggest')
    .setDescription('💡 Submit a professional suggestion with advanced features')
    .addStringOption(option =>
      option.setName('suggestion')
        .setDescription('Your detailed suggestion (max 2000 characters)')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('category')
        .setDescription('Suggestion category')
        .setRequired(false)
        .addChoices(
          { name: '🤖 Bot Features', value: 'Bot Features' },
          { name: '🛠️ Server Improvements', value: 'Server Improvements' },
          { name: '🎮 Gaming Features', value: 'Gaming Features' },
          { name: '🎨 Design & UI', value: 'Design & UI' },
          { name: '🔧 Technical', value: 'Technical' },
          { name: '📊 Analytics', value: 'Analytics' },
          { name: '🎉 Fun Features', value: 'Fun Features' },
          { name: '📝 General', value: 'General' }
        ))
    .addBooleanOption(option =>
      option.setName('anonymous')
        .setDescription('Submit suggestion anonymously')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('priority')
        .setDescription('Suggested priority level')
        .setRequired(false)
        .addChoices(
          { name: '🔥 High Priority', value: 'high' },
          { name: '⚡ Medium Priority', value: 'medium' },
          { name: '📝 Low Priority', value: 'low' }
        ))
    .addStringOption(option =>
      option.setName('tags')
        .setDescription('Tags for your suggestion (comma-separated)')
        .setRequired(false)),

  async execute(context) {
    const interaction = context.interaction;
    const message = context.message;

    try {
      let suggestionText, category, anonymous, priority, tags, authorId, authorName;

      if (interaction) {
        // Slash command execution
        suggestionText = interaction.options.getString('suggestion');
        category = interaction.options.getString('category') || 'General';
        anonymous = interaction.options.getBoolean('anonymous') || false;
        priority = interaction.options.getString('priority') || 'medium';
        tags = interaction.options.getString('tags') || '';
        authorId = interaction.user.id;
        authorName = interaction.user.displayName;
      } else {
        // Legacy message command
        const args = message.content.split(' ').slice(1);
        suggestionText = args.join(' ');
        category = 'General';
        anonymous = false;
        priority = 'medium';
        tags = '';
        authorId = message.author.id;
        authorName = message.author.displayName;
      }

      // Validation
      if (!suggestionText || suggestionText.length < 10) {
        const errorMsg = `${EMOJIS.ERROR} Please provide a detailed suggestion (at least 10 characters).`;
        return interaction ?
          await interaction.reply({ content: errorMsg, ephemeral: true }) :
          await message.reply(errorMsg);
      }

      if (suggestionText.length > 2000) {
        const errorMsg = `${EMOJIS.ERROR} Suggestion too long! Maximum 2000 characters allowed.`;
        return interaction ?
          await interaction.reply({ content: errorMsg, ephemeral: true }) :
          await message.reply(errorMsg);
      }

      const guildId = interaction ? interaction.guild.id : message.guild.id;
      const suggestionData = await Suggestion.findOne({ guildId });

      if (!suggestionData) {
        const errorMsg = `${EMOJIS.ERROR} Suggestion system not set up. Ask an admin to use \`/suggestion setup channel\` first.`;
        return interaction ?
          await interaction.reply({ content: errorMsg, ephemeral: true }) :
          await message.reply(errorMsg);
      }

      // Check cooldown
      const userSuggestions = suggestionData.suggestions.filter(s => s.authorId === authorId);
      if (userSuggestions.length > 0) {
        const lastSuggestion = userSuggestions[userSuggestions.length - 1];
        const cooldownMs = suggestionData.config.cooldown * 60 * 1000;
        const timeSinceLastSuggestion = Date.now() - new Date(lastSuggestion.createdAt).getTime();

        if (timeSinceLastSuggestion < cooldownMs) {
          const remainingTime = Math.ceil((cooldownMs - timeSinceLastSuggestion) / 60000);
          const errorMsg = `${EMOJIS.TIME} Please wait ${remainingTime} more minutes before submitting another suggestion.`;
          return interaction ?
            await interaction.reply({ content: errorMsg, ephemeral: true }) :
            await message.reply(errorMsg);
        }
      }

      // Check user suggestion limit
      if (userSuggestions.length >= suggestionData.config.maxSuggestionsPerUser) {
        const errorMsg = `${EMOJIS.WARNING} You have reached the maximum number of suggestions (${suggestionData.config.maxSuggestionsPerUser}). Please wait for some to be reviewed.`;
        return interaction ?
          await interaction.reply({ content: errorMsg, ephemeral: true }) :
          await message.reply(errorMsg);
      }

      // Process tags
      const tagArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0).slice(0, 5) : [];

      // Create suggestion embed
      const suggestionId = `SUG-${Date.now()}`;
      const embed = new EmbedBuilder()
        .setColor(COLORS.SUGGESTION)
        .setTitle(`${EMOJIS.IDEA} New Suggestion`)
        .setDescription(suggestionText)
        .addFields(
          {
            name: `${EMOJIS.CATEGORY} Category`,
            value: category,
            inline: true
          },
          {
            name: `${EMOJIS.USER} Author`,
            value: anonymous ? 'Anonymous' : `<@${authorId}>`,
            inline: true
          },
          {
            name: `${EMOJIS.ID} Priority`,
            value: priority.charAt(0).toUpperCase() + priority.slice(1),
            inline: true
          }
        )
        .setFooter({
          text: `${EMOJIS.ID} Suggestion ID: ${suggestionId} • React to vote!`,
          iconURL: anonymous ? null : (interaction ? interaction.user.displayAvatarURL() : message.author.displayAvatarURL())
        })
        .setTimestamp();

      // Add tags if present
      if (tagArray.length > 0) {
        embed.addFields({
          name: '🏷️ Tags',
          value: tagArray.map(tag => `\`${tag}\``).join(' '),
          inline: false
        });
      }

      // Send to suggestion channel
      const channel = await (interaction ? interaction.guild.channels.fetch(suggestionData.channelId) : message.guild.channels.fetch(suggestionData.channelId));

      // Create voting buttons if voting is enabled
      let components = [];
      if (suggestionData.config.voting) {
        const voteRow = new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId(`vote_up_${suggestionId}`)
              .setLabel('👍 Upvote')
              .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
              .setCustomId(`vote_down_${suggestionId}`)
              .setLabel('👎 Downvote')
              .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
              .setCustomId(`suggestion_details_${suggestionId}`)
              .setLabel('📊 Details')
              .setStyle(ButtonStyle.Secondary)
          );
        components.push(voteRow);
      }

      const suggestionMessage = await channel.send({
        embeds: [embed],
        components: components
      });

      // Add legacy reactions if no buttons
      if (!suggestionData.config.voting) {
        await suggestionMessage.react('👍');
        await suggestionMessage.react('👎');
      }

      // Save to database
      const newSuggestion = {
        messageId: suggestionMessage.id,
        content: suggestionText,
        authorId: authorId,
        category: category,
        status: 'pending',
        priority: priority,
        anonymous: anonymous,
        tags: tagArray,
        upvotes: 0,
        downvotes: 0,
        voters: [],
        comments: [],
        views: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        // Legacy support
        accepted: false,
        declined: false
      };

      suggestionData.suggestions.push(newSuggestion);
      await suggestionData.save();

      // Success response
      const successEmbed = new EmbedBuilder()
        .setColor(COLORS.SUCCESS)
        .setTitle(`${EMOJIS.SUCCESS} Suggestion Submitted Successfully!`)
        .setDescription('**✅ Your suggestion has been submitted and is now pending review.**')
        .addFields(
          {
            name: '📊 **Submission Details**',
            value: [
              `**Suggestion ID:** ${suggestionId}`,
              `**Category:** ${category}`,
              `**Priority:** ${priority.charAt(0).toUpperCase() + priority.slice(1)}`,
              `**Anonymous:** ${anonymous ? 'Yes' : 'No'}`,
              `**Channel:** ${channel}`
            ].join('\n'),
            inline: true
          },
          {
            name: '🎯 **What Happens Next**',
            value: [
              `• Your suggestion is now visible in ${channel}`,
              `• Community members can vote on it`,
              `• Staff will review and provide feedback`,
              `• You'll be notified of status changes`,
              `• Check progress with \`/suggestion view details\``
            ].join('\n'),
            inline: true
          }
        )
        .setFooter({ text: 'Professional Suggestion System' })
        .setTimestamp();

      if (tagArray.length > 0) {
        successEmbed.addFields({
          name: '🏷️ **Tags Added**',
          value: tagArray.map(tag => `\`${tag}\``).join(' '),
          inline: false
        });
      }

      return interaction ?
        await interaction.reply({ embeds: [successEmbed], ephemeral: true }) :
        await message.reply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Suggest command error:', error);
      const errorMsg = `${EMOJIS.ERROR} An error occurred while submitting your suggestion. Please try again later.`;
      return interaction ?
        await interaction.reply({ content: errorMsg, ephemeral: true }) :
        await message.reply(errorMsg);
    }
  }
};