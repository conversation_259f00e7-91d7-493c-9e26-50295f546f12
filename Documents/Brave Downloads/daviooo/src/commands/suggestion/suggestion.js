const { Slash<PERSON>ommandBuilder, EmbedBuilder, Colors, Permissions, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, PermissionFlagsBits } = require('discord.js');
const Suggestion = require('../../database/models/suggestion');

// 🎨 PROFESSIONAL DESIGN CONSTANTS
const COLORS = {
  PRIMARY: '#3498db',
  SUCCESS: '#2ecc71',
  WARNING: '#f39c12',
  ERROR: '#e74c3c',
  INFO: '#9b59b6',
  PREMIUM: '#f1c40f',
  SUGGESTION: '#1abc9c',
  PENDING: '#95a5a6',
  ACCEPTED: '#27ae60',
  DECLINED: '#c0392b',
  IMPLEMENTED: '#8e44ad'
};

const EMOJIS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  GEAR: '⚙️',
  IDEA: '💡',
  ID: '🆔',
  STATS: '📊',
  SEARCH: '🔍',
  EDIT: '✏️',
  DELETE: '🗑️',
  ARCHIVE: '📦',
  PRIORITY: '🔥',
  CATEGORY: '📂',
  USER: '👤',
  TIME: '⏰',
  VOTE: '🗳️',
  COMMENT: '💬',
  NOTIFICATION: '🔔',
  EXPORT: '📤',
  IMPORT: '📥',
  BACKUP: '💾',
  RESTORE: '🔄',
  ANALYTICS: '📈',
  FILTER: '🔽',
  SORT: '🔀',
  BULK: '📋',
  TEMPLATE: '📄',
  REWARD: '🏆',
  LEADERBOARD: '🏅'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('suggestion')
    .setDescription('💡 Professional Suggestion Management System - 25+ Commands!')
    .addSubcommandGroup(group =>
      group.setName('setup')
        .setDescription('⚙️ System Configuration & Setup')
        .addSubcommand(subcommand =>
          subcommand.setName('channel')
            .setDescription('📌 Setup suggestion channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Target channel for suggestions')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('config')
            .setDescription('🔧 Configure suggestion system settings')
            .addBooleanOption(option =>
              option.setName('anonymous')
                .setDescription('Allow anonymous suggestions')
                .setRequired(false))
            .addBooleanOption(option =>
              option.setName('voting')
                .setDescription('Enable voting on suggestions')
                .setRequired(false))
            .addIntegerOption(option =>
              option.setName('cooldown')
                .setDescription('Cooldown between suggestions (minutes)')
                .setMinValue(0)
                .setMaxValue(1440)
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('roles')
            .setDescription('🎭 Configure suggestion management roles')
            .addRoleOption(option =>
              option.setName('manager')
                .setDescription('Role that can manage suggestions')
                .setRequired(false))
            .addRoleOption(option =>
              option.setName('reviewer')
                .setDescription('Role that can review suggestions')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('categories')
            .setDescription('📂 Setup suggestion categories')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('Action to perform')
                .addChoices(
                  { name: 'Add Category', value: 'add' },
                  { name: 'Remove Category', value: 'remove' },
                  { name: 'List Categories', value: 'list' }
                )
                .setRequired(true))
            .addStringOption(option =>
              option.setName('name')
                .setDescription('Category name')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('templates')
            .setDescription('📄 Manage suggestion templates')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('Template action')
                .addChoices(
                  { name: 'Create Template', value: 'create' },
                  { name: 'Edit Template', value: 'edit' },
                  { name: 'Delete Template', value: 'delete' },
                  { name: 'List Templates', value: 'list' }
                )
                .setRequired(true))
            .addStringOption(option =>
              option.setName('name')
                .setDescription('Template name')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('notifications')
            .setDescription('🔔 Configure notification settings')
            .addChannelOption(option =>
              option.setName('log_channel')
                .setDescription('Channel for suggestion logs')
                .setRequired(false))
            .addBooleanOption(option =>
              option.setName('dm_notifications')
                .setDescription('Send DM notifications to suggestion authors')
                .setRequired(false))))
    .addSubcommandGroup(group =>
      group.setName('manage')
        .setDescription('🛠️ Suggestion Management & Moderation')
        .addSubcommand(subcommand =>
          subcommand.setName('accept')
            .setDescription('✅ Accept a suggestion')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for acceptance')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('decline')
            .setDescription('❌ Decline a suggestion')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for decline')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('implement')
            .setDescription('🚀 Mark suggestion as implemented')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('details')
                .setDescription('Implementation details')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('consider')
            .setDescription('🤔 Mark suggestion as under consideration')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('notes')
                .setDescription('Consideration notes')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('priority')
            .setDescription('🔥 Set suggestion priority')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('level')
                .setDescription('Priority level')
                .addChoices(
                  { name: 'Low', value: 'low' },
                  { name: 'Medium', value: 'medium' },
                  { name: 'High', value: 'high' },
                  { name: 'Critical', value: 'critical' }
                )
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('edit')
            .setDescription('✏️ Edit suggestion content')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('new_content')
                .setDescription('New suggestion content')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('delete')
            .setDescription('🗑️ Delete a suggestion')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for deletion')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('archive')
            .setDescription('📦 Archive old suggestions')
            .addIntegerOption(option =>
              option.setName('days')
                .setDescription('Archive suggestions older than X days')
                .setMinValue(1)
                .setMaxValue(365)
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('bulk')
            .setDescription('📋 Bulk manage suggestions')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('Bulk action to perform')
                .addChoices(
                  { name: 'Accept All Pending', value: 'accept_pending' },
                  { name: 'Decline All Pending', value: 'decline_pending' },
                  { name: 'Archive All Old', value: 'archive_old' },
                  { name: 'Delete All Declined', value: 'delete_declined' }
                )
                .setRequired(true))
            .addStringOption(option =>
              option.setName('filter')
                .setDescription('Filter criteria (optional)')
                .setRequired(false))))
    .addSubcommandGroup(group =>
      group.setName('view')
        .setDescription('👁️ View & Search Suggestions')
        .addSubcommand(subcommand =>
          subcommand.setName('list')
            .setDescription('📋 List all suggestions')
            .addStringOption(option =>
              option.setName('status')
                .setDescription('Filter by status')
                .addChoices(
                  { name: 'All', value: 'all' },
                  { name: 'Pending', value: 'pending' },
                  { name: 'Accepted', value: 'accepted' },
                  { name: 'Declined', value: 'declined' },
                  { name: 'Implemented', value: 'implemented' }
                )
                .setRequired(false))
            .addIntegerOption(option =>
              option.setName('page')
                .setDescription('Page number')
                .setMinValue(1)
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('search')
            .setDescription('🔍 Search suggestions')
            .addStringOption(option =>
              option.setName('query')
                .setDescription('Search query')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('category')
                .setDescription('Filter by category')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('details')
            .setDescription('📊 View detailed suggestion info')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('user')
            .setDescription('👤 View user\'s suggestions')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to view suggestions for')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('status')
                .setDescription('Filter by status')
                .addChoices(
                  { name: 'All', value: 'all' },
                  { name: 'Pending', value: 'pending' },
                  { name: 'Accepted', value: 'accepted' },
                  { name: 'Declined', value: 'declined' }
                )
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('top')
            .setDescription('🏆 View top-voted suggestions')
            .addIntegerOption(option =>
              option.setName('limit')
                .setDescription('Number of suggestions to show')
                .setMinValue(1)
                .setMaxValue(25)
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('recent')
            .setDescription('⏰ View recent suggestions')
            .addIntegerOption(option =>
              option.setName('hours')
                .setDescription('Show suggestions from last X hours')
                .setMinValue(1)
                .setMaxValue(168)
                .setRequired(false))))
    .addSubcommandGroup(group =>
      group.setName('analytics')
        .setDescription('📈 Statistics & Analytics')
        .addSubcommand(subcommand =>
          subcommand.setName('stats')
            .setDescription('📊 View suggestion statistics')
            .addStringOption(option =>
              option.setName('period')
                .setDescription('Time period for stats')
                .addChoices(
                  { name: 'Today', value: 'today' },
                  { name: 'This Week', value: 'week' },
                  { name: 'This Month', value: 'month' },
                  { name: 'All Time', value: 'all' }
                )
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('leaderboard')
            .setDescription('🏅 View suggestion leaderboard')
            .addStringOption(option =>
              option.setName('type')
                .setDescription('Leaderboard type')
                .addChoices(
                  { name: 'Most Suggestions', value: 'count' },
                  { name: 'Most Accepted', value: 'accepted' },
                  { name: 'Highest Rated', value: 'rating' },
                  { name: 'Most Active', value: 'activity' }
                )
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('trends')
            .setDescription('📈 View suggestion trends')
            .addStringOption(option =>
              option.setName('metric')
                .setDescription('Trend metric')
                .addChoices(
                  { name: 'Submission Rate', value: 'submissions' },
                  { name: 'Acceptance Rate', value: 'acceptance' },
                  { name: 'Category Popularity', value: 'categories' },
                  { name: 'User Engagement', value: 'engagement' }
                )
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('export')
            .setDescription('📤 Export suggestion data')
            .addStringOption(option =>
              option.setName('format')
                .setDescription('Export format')
                .addChoices(
                  { name: 'CSV', value: 'csv' },
                  { name: 'JSON', value: 'json' },
                  { name: 'PDF Report', value: 'pdf' }
                )
                .setRequired(false))
            .addStringOption(option =>
              option.setName('filter')
                .setDescription('Data filter')
                .addChoices(
                  { name: 'All Data', value: 'all' },
                  { name: 'Accepted Only', value: 'accepted' },
                  { name: 'Pending Only', value: 'pending' },
                  { name: 'Last 30 Days', value: 'recent' }
                )
                .setRequired(false))))
    .addSubcommandGroup(group =>
      group.setName('tools')
        .setDescription('🔧 Advanced Tools & Utilities')
        .addSubcommand(subcommand =>
          subcommand.setName('backup')
            .setDescription('💾 Backup suggestion data')
            .addBooleanOption(option =>
              option.setName('include_votes')
                .setDescription('Include voting data in backup')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('restore')
            .setDescription('🔄 Restore suggestion data')
            .addStringOption(option =>
              option.setName('backup_id')
                .setDescription('Backup ID to restore from')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('migrate')
            .setDescription('🚚 Migrate suggestions to new channel')
            .addChannelOption(option =>
              option.setName('target_channel')
                .setDescription('Target channel for migration')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('cleanup')
            .setDescription('🧹 Clean up suggestion database')
            .addBooleanOption(option =>
              option.setName('remove_duplicates')
                .setDescription('Remove duplicate suggestions')
                .setRequired(false))
            .addBooleanOption(option =>
              option.setName('fix_orphaned')
                .setDescription('Fix orphaned suggestion records')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('import')
            .setDescription('📥 Import suggestions from file')
            .addStringOption(option =>
              option.setName('format')
                .setDescription('Import format')
                .addChoices(
                  { name: 'CSV', value: 'csv' },
                  { name: 'JSON', value: 'json' },
                  { name: 'Text File', value: 'txt' }
                )
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('duplicate')
            .setDescription('🔍 Find duplicate suggestions')
            .addIntegerOption(option =>
              option.setName('similarity')
                .setDescription('Similarity threshold (percentage)')
                .setMinValue(50)
                .setMaxValue(100)
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('merge')
            .setDescription('🔗 Merge similar suggestions')
            .addStringOption(option =>
              option.setName('primary_id')
                .setDescription('Primary suggestion message ID')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('secondary_id')
                .setDescription('Secondary suggestion message ID')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('schedule')
            .setDescription('⏰ Schedule suggestion review')
            .addStringOption(option =>
              option.setName('message_id')
                .setDescription('Message ID of the suggestion')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('date')
                .setDescription('Review date (YYYY-MM-DD)')
                .setRequired(true)))),

  async execute(context) {
    const interaction = context.interaction;
    const message = context.message;
    const guildId = interaction ? interaction.guild.id : message.guild.id;

    try {
      if (interaction) {
        // Handle slash commands
        const subcommandGroup = interaction.options.getSubcommandGroup();
        const subcommand = interaction.options.getSubcommand();

        // Route to appropriate handler
        switch (subcommandGroup) {
          case 'setup':
            return await handleSetupCommands(interaction, subcommand);
          case 'manage':
            return await handleManageCommands(interaction, subcommand);
          case 'view':
            return await handleViewCommands(interaction, subcommand);
          case 'analytics':
            return await handleAnalyticsCommands(interaction, subcommand);
          case 'tools':
            return await handleToolsCommands(interaction, subcommand);
          default:
            return await createErrorResponse(interaction, 'Unknown command group!');
        }
      } else {
        // Handle legacy message commands
        return await handleLegacyCommands(message);
      }
    } catch (error) {
      console.error('Suggestion command error:', error);
      const errorMsg = `${EMOJIS.ERROR} An error occurred while executing the command.`;
      return interaction ?
        await interaction.reply({ content: errorMsg, ephemeral: true }) :
        await message.reply(errorMsg);
    }
  }
};

// 🛠️ UTILITY FUNCTIONS
function createErrorEmbed(message) {
  return new EmbedBuilder()
    .setColor(COLORS.ERROR)
    .setTitle(`${EMOJIS.ERROR} Error`)
    .setDescription(message)
    .setTimestamp();
}

function createSuccessEmbed(title, description) {
  return new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} ${title}`)
    .setDescription(description)
    .setTimestamp();
}

async function createErrorResponse(interaction, message) {
  const embed = createErrorEmbed(message);
  return await interaction.reply({ embeds: [embed], ephemeral: true });
}

function formatDate(date) {
  return `<t:${Math.floor(date.getTime() / 1000)}:F>`;
}

function formatRelativeTime(date) {
  return `<t:${Math.floor(date.getTime() / 1000)}:R>`;
}

// 📊 SETUP COMMANDS HANDLER
async function handleSetupCommands(interaction, subcommand) {
  // Check permissions
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
    return await createErrorResponse(interaction, 'You need "Manage Server" permissions to use setup commands!');
  }

  switch (subcommand) {
    case 'channel':
      return await setupChannel(interaction);
    case 'config':
      return await setupConfig(interaction);
    case 'roles':
      return await setupRoles(interaction);
    case 'categories':
      return await setupCategories(interaction);
    case 'templates':
      return await setupTemplates(interaction);
    case 'notifications':
      return await setupNotifications(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown setup command!');
  }
}

// 📌 SETUP CHANNEL COMMAND
async function setupChannel(interaction) {
  const channel = interaction.options.getChannel('channel');
  const guildId = interaction.guild.id;

  if (channel.type !== 0) { // GUILD_TEXT
    return await createErrorResponse(interaction, 'Channel must be a text channel!');
  }

  try {
    let suggestionSetup = await Suggestion.findOne({ guildId });
    if (!suggestionSetup) {
      suggestionSetup = new Suggestion({
        guildId,
        channelId: channel.id,
        suggestions: [],
        config: {
          anonymous: false,
          voting: true,
          cooldown: 5,
          categories: ['General', 'Bot Features', 'Server Improvements'],
          managerRole: null,
          reviewerRole: null,
          logChannel: null,
          dmNotifications: true
        }
      });
    } else {
      suggestionSetup.channelId = channel.id;
    }

    await suggestionSetup.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.GEAR} Suggestion Channel Setup`)
      .setDescription(`**✅ Successfully configured suggestion system!**`)
      .addFields(
        {
          name: '📌 **Channel Configuration**',
          value: [
            `**Suggestion Channel:** ${channel}`,
            `**Channel ID:** ${channel.id}`,
            `**Channel Type:** Text Channel`,
            `**Permissions:** Verified`,
            `**Status:** Active`
          ].join('\n'),
          inline: true
        },
        {
          name: '⚙️ **Default Settings**',
          value: [
            `**Anonymous Suggestions:** Disabled`,
            `**Voting System:** Enabled`,
            `**Cooldown:** 5 minutes`,
            `**Categories:** 3 default`,
            `**DM Notifications:** Enabled`
          ].join('\n'),
          inline: true
        },
        {
          name: '🚀 **Next Steps**',
          value: [
            `• Use \`/suggestion setup config\` to customize settings`,
            `• Use \`/suggestion setup roles\` to set management roles`,
            `• Use \`/suggestion setup categories\` to add custom categories`,
            `• Users can now use \`/suggest\` to submit suggestions!`
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'Professional Suggestion Management System' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup channel error:', error);
    return await createErrorResponse(interaction, 'Failed to setup suggestion channel!');
  }
}

// 🔧 SETUP CONFIG COMMAND
async function setupConfig(interaction) {
  const anonymous = interaction.options.getBoolean('anonymous');
  const voting = interaction.options.getBoolean('voting');
  const cooldown = interaction.options.getInteger('cooldown');
  const guildId = interaction.guild.id;

  try {
    let suggestionSetup = await Suggestion.findOne({ guildId });
    if (!suggestionSetup) {
      return await createErrorResponse(interaction, 'Suggestion system not initialized! Use `/suggestion setup channel` first.');
    }

    // Update configuration
    if (anonymous !== null) suggestionSetup.config.anonymous = anonymous;
    if (voting !== null) suggestionSetup.config.voting = voting;
    if (cooldown !== null) suggestionSetup.config.cooldown = cooldown;

    await suggestionSetup.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.GEAR} Configuration Updated`)
      .setDescription('**⚙️ Suggestion system configuration has been updated!**')
      .addFields(
        {
          name: '📊 **Current Settings**',
          value: [
            `**Anonymous Suggestions:** ${suggestionSetup.config.anonymous ? 'Enabled' : 'Disabled'}`,
            `**Voting System:** ${suggestionSetup.config.voting ? 'Enabled' : 'Disabled'}`,
            `**Cooldown Period:** ${suggestionSetup.config.cooldown} minutes`,
            `**Categories:** ${suggestionSetup.config.categories.length} active`,
            `**DM Notifications:** ${suggestionSetup.config.dmNotifications ? 'Enabled' : 'Disabled'}`
          ].join('\n'),
          inline: true
        },
        {
          name: '🔧 **Updated Values**',
          value: [
            anonymous !== null ? `**Anonymous:** ${anonymous ? 'Enabled' : 'Disabled'}` : '',
            voting !== null ? `**Voting:** ${voting ? 'Enabled' : 'Disabled'}` : '',
            cooldown !== null ? `**Cooldown:** ${cooldown} minutes` : '',
            '**Status:** Configuration saved'
          ].filter(Boolean).join('\n') || 'No changes made',
          inline: true
        }
      )
      .setFooter({ text: 'Professional Configuration Management' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup config error:', error);
    return await createErrorResponse(interaction, 'Failed to update configuration!');
  }
}

// 🎭 SETUP ROLES COMMAND
async function setupRoles(interaction) {
  const managerRole = interaction.options.getRole('manager');
  const reviewerRole = interaction.options.getRole('reviewer');
  const guildId = interaction.guild.id;

  try {
    let suggestionSetup = await Suggestion.findOne({ guildId });
    if (!suggestionSetup) {
      return await createErrorResponse(interaction, 'Suggestion system not initialized! Use `/suggestion setup channel` first.');
    }

    if (managerRole) suggestionSetup.config.managerRole = managerRole.id;
    if (reviewerRole) suggestionSetup.config.reviewerRole = reviewerRole.id;

    await suggestionSetup.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.GEAR} Role Configuration`)
      .setDescription('**🎭 Suggestion management roles have been configured!**')
      .addFields(
        {
          name: '👑 **Management Roles**',
          value: [
            `**Manager Role:** ${suggestionSetup.config.managerRole ? `<@&${suggestionSetup.config.managerRole}>` : 'Not set'}`,
            `**Reviewer Role:** ${suggestionSetup.config.reviewerRole ? `<@&${suggestionSetup.config.reviewerRole}>` : 'Not set'}`,
            `**Permissions:** Configured`,
            `**Status:** Active`
          ].join('\n'),
          inline: true
        },
        {
          name: '🔑 **Role Permissions**',
          value: [
            `**Manager Role:**`,
            `• Accept/Decline suggestions`,
            `• Edit suggestion content`,
            `• Set priorities`,
            `• Bulk operations`,
            `**Reviewer Role:**`,
            `• View suggestion details`,
            `• Add comments`,
            `• Mark as under consideration`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'Professional Role Management' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup roles error:', error);
    return await createErrorResponse(interaction, 'Failed to configure roles!');
  }
}

// 🛠️ MANAGE COMMANDS HANDLER
async function handleManageCommands(interaction, subcommand) {
  const guildId = interaction.guild.id;

  // Check if user has management permissions
  const suggestionSetup = await Suggestion.findOne({ guildId });
  if (!suggestionSetup) {
    return await createErrorResponse(interaction, 'Suggestion system not initialized!');
  }

  const hasManagerRole = suggestionSetup.config.managerRole &&
    interaction.member.roles.cache.has(suggestionSetup.config.managerRole);
  const hasReviewerRole = suggestionSetup.config.reviewerRole &&
    interaction.member.roles.cache.has(suggestionSetup.config.reviewerRole);
  const hasAdminPerms = interaction.member.permissions.has(PermissionFlagsBits.ManageGuild);

  if (!hasManagerRole && !hasReviewerRole && !hasAdminPerms) {
    return await createErrorResponse(interaction, 'You don\'t have permission to manage suggestions!');
  }

  switch (subcommand) {
    case 'accept':
      return await acceptSuggestion(interaction);
    case 'decline':
      return await declineSuggestion(interaction);
    case 'implement':
      return await implementSuggestion(interaction);
    case 'consider':
      return await considerSuggestion(interaction);
    case 'priority':
      return await setPriority(interaction);
    case 'edit':
      return await editSuggestion(interaction);
    case 'delete':
      return await deleteSuggestion(interaction);
    case 'archive':
      return await archiveSuggestions(interaction);
    case 'bulk':
      return await bulkManage(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown manage command!');
  }
}

// ✅ ACCEPT SUGGESTION
async function acceptSuggestion(interaction) {
  const messageId = interaction.options.getString('message_id');
  const reason = interaction.options.getString('reason') || 'No reason provided';
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData) {
      return await createErrorResponse(interaction, 'Suggestion system not initialized!');
    }

    const suggestion = suggestionData.suggestions.find(s => s.messageId === messageId);
    if (!suggestion) {
      return await createErrorResponse(interaction, 'Suggestion not found!');
    }

    if (suggestion.status === 'accepted') {
      return await createErrorResponse(interaction, 'This suggestion is already accepted!');
    }

    // Update suggestion in database
    suggestion.status = 'accepted';
    suggestion.reviewedBy = interaction.user.id;
    suggestion.reviewedAt = new Date();
    suggestion.reviewReason = reason;
    await suggestionData.save();

    // Update the original message
    try {
      const channel = await interaction.guild.channels.fetch(suggestionData.channelId);
      const targetMessage = await channel.messages.fetch(messageId);
      const oldEmbed = targetMessage.embeds[0];

      const newEmbed = EmbedBuilder.from(oldEmbed)
        .setColor(COLORS.ACCEPTED)
        .addFields({
          name: `${EMOJIS.SUCCESS} Status: Accepted`,
          value: [
            `**Reviewed by:** ${interaction.user}`,
            `**Reason:** ${reason}`,
            `**Date:** ${formatDate(new Date())}`
          ].join('\n'),
          inline: false
        });

      await targetMessage.edit({ embeds: [newEmbed] });
    } catch (error) {
      console.error('Error updating suggestion message:', error);
    }

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} Suggestion Accepted`)
      .setDescription('**✅ The suggestion has been successfully accepted!**')
      .addFields(
        {
          name: '📊 **Suggestion Details**',
          value: [
            `**Message ID:** ${messageId}`,
            `**Status:** Accepted`,
            `**Reviewed by:** ${interaction.user}`,
            `**Reason:** ${reason}`,
            `**Date:** ${formatDate(new Date())}`
          ].join('\n'),
          inline: true
        },
        {
          name: '🎯 **Next Steps**',
          value: [
            `• Suggestion author will be notified`,
            `• Status updated in suggestion channel`,
            `• Added to accepted suggestions list`,
            `• Consider marking as implemented when done`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'Professional Suggestion Management' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Accept suggestion error:', error);
    return await createErrorResponse(interaction, 'Failed to accept suggestion!');
  }
}

// ❌ DECLINE SUGGESTION
async function declineSuggestion(interaction) {
  const messageId = interaction.options.getString('message_id');
  const reason = interaction.options.getString('reason') || 'No reason provided';
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData) {
      return await createErrorResponse(interaction, 'Suggestion system not initialized!');
    }

    const suggestion = suggestionData.suggestions.find(s => s.messageId === messageId);
    if (!suggestion) {
      return await createErrorResponse(interaction, 'Suggestion not found!');
    }

    if (suggestion.status === 'declined') {
      return await createErrorResponse(interaction, 'This suggestion is already declined!');
    }

    // Update suggestion in database
    suggestion.status = 'declined';
    suggestion.reviewedBy = interaction.user.id;
    suggestion.reviewedAt = new Date();
    suggestion.reviewReason = reason;
    await suggestionData.save();

    // Update the original message
    try {
      const channel = await interaction.guild.channels.fetch(suggestionData.channelId);
      const targetMessage = await channel.messages.fetch(messageId);
      const oldEmbed = targetMessage.embeds[0];

      const newEmbed = EmbedBuilder.from(oldEmbed)
        .setColor(COLORS.DECLINED)
        .addFields({
          name: `${EMOJIS.ERROR} Status: Declined`,
          value: [
            `**Reviewed by:** ${interaction.user}`,
            `**Reason:** ${reason}`,
            `**Date:** ${formatDate(new Date())}`
          ].join('\n'),
          inline: false
        });

      await targetMessage.edit({ embeds: [newEmbed] });
    } catch (error) {
      console.error('Error updating suggestion message:', error);
    }

    const embed = new EmbedBuilder()
      .setColor(COLORS.ERROR)
      .setTitle(`${EMOJIS.ERROR} Suggestion Declined`)
      .setDescription('**❌ The suggestion has been declined.**')
      .addFields(
        {
          name: '📊 **Suggestion Details**',
          value: [
            `**Message ID:** ${messageId}`,
            `**Status:** Declined`,
            `**Reviewed by:** ${interaction.user}`,
            `**Reason:** ${reason}`,
            `**Date:** ${formatDate(new Date())}`
          ].join('\n'),
          inline: true
        },
        {
          name: '📝 **Feedback**',
          value: [
            `• Suggestion author will be notified`,
            `• Status updated in suggestion channel`,
            `• Added to declined suggestions list`,
            `• User can submit improved version`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'Professional Suggestion Management' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Decline suggestion error:', error);
    return await createErrorResponse(interaction, 'Failed to decline suggestion!');
  }
}

// 👁️ VIEW COMMANDS HANDLER
async function handleViewCommands(interaction, subcommand) {
  switch (subcommand) {
    case 'list':
      return await listSuggestions(interaction);
    case 'search':
      return await searchSuggestions(interaction);
    case 'details':
      return await viewSuggestionDetails(interaction);
    case 'user':
      return await viewUserSuggestions(interaction);
    case 'top':
      return await viewTopSuggestions(interaction);
    case 'recent':
      return await viewRecentSuggestions(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown view command!');
  }
}

// 📋 LIST SUGGESTIONS
async function listSuggestions(interaction) {
  const status = interaction.options.getString('status') || 'all';
  const page = interaction.options.getInteger('page') || 1;
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData || suggestionData.suggestions.length === 0) {
      return await createErrorResponse(interaction, 'No suggestions found!');
    }

    let filteredSuggestions = suggestionData.suggestions;
    if (status !== 'all') {
      filteredSuggestions = suggestionData.suggestions.filter(s => s.status === status);
    }

    const itemsPerPage = 10;
    const totalPages = Math.ceil(filteredSuggestions.length / itemsPerPage);
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedSuggestions = filteredSuggestions.slice(startIndex, endIndex);

    const embed = new EmbedBuilder()
      .setColor(COLORS.PRIMARY)
      .setTitle(`${EMOJIS.SEARCH} Suggestion List`)
      .setDescription(`**📊 Showing suggestions (Page ${page}/${totalPages})**`)
      .addFields(
        {
          name: '📈 **Statistics**',
          value: [
            `**Total Suggestions:** ${suggestionData.suggestions.length}`,
            `**Filtered Results:** ${filteredSuggestions.length}`,
            `**Current Page:** ${page}/${totalPages}`,
            `**Status Filter:** ${status.charAt(0).toUpperCase() + status.slice(1)}`
          ].join('\n'),
          inline: true
        }
      );

    // Add suggestion entries
    paginatedSuggestions.forEach((suggestion, index) => {
      const statusEmoji = getStatusEmoji(suggestion.status);
      const shortContent = suggestion.content.length > 100 ?
        suggestion.content.substring(0, 100) + '...' : suggestion.content;

      embed.addFields({
        name: `${statusEmoji} **Suggestion #${startIndex + index + 1}**`,
        value: [
          `**Content:** ${shortContent}`,
          `**Status:** ${suggestion.status || 'pending'}`,
          `**ID:** ${suggestion.messageId}`,
          `**Date:** ${formatRelativeTime(new Date(suggestion.createdAt || Date.now()))}`
        ].join('\n'),
        inline: false
      });
    });

    embed.setFooter({ text: `Professional Suggestion System • Page ${page}/${totalPages}` })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('List suggestions error:', error);
    return await createErrorResponse(interaction, 'Failed to list suggestions!');
  }
}

// 🔍 SEARCH SUGGESTIONS
async function searchSuggestions(interaction) {
  const query = interaction.options.getString('query');
  const category = interaction.options.getString('category');
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData || suggestionData.suggestions.length === 0) {
      return await createErrorResponse(interaction, 'No suggestions found!');
    }

    let results = suggestionData.suggestions.filter(suggestion =>
      suggestion.content.toLowerCase().includes(query.toLowerCase())
    );

    if (category) {
      results = results.filter(suggestion =>
        suggestion.category && suggestion.category.toLowerCase() === category.toLowerCase()
      );
    }

    if (results.length === 0) {
      return await createErrorResponse(interaction, 'No suggestions match your search criteria!');
    }

    const embed = new EmbedBuilder()
      .setColor(COLORS.INFO)
      .setTitle(`${EMOJIS.SEARCH} Search Results`)
      .setDescription(`**🔍 Found ${results.length} suggestions matching "${query}"**`)
      .addFields(
        {
          name: '📊 **Search Info**',
          value: [
            `**Query:** "${query}"`,
            `**Category Filter:** ${category || 'None'}`,
            `**Results Found:** ${results.length}`,
            `**Total Suggestions:** ${suggestionData.suggestions.length}`
          ].join('\n'),
          inline: true
        }
      );

    // Show top 5 results
    results.slice(0, 5).forEach((suggestion, index) => {
      const statusEmoji = getStatusEmoji(suggestion.status);
      const shortContent = suggestion.content.length > 150 ?
        suggestion.content.substring(0, 150) + '...' : suggestion.content;

      embed.addFields({
        name: `${statusEmoji} **Result #${index + 1}**`,
        value: [
          `**Content:** ${shortContent}`,
          `**Status:** ${suggestion.status || 'pending'}`,
          `**Category:** ${suggestion.category || 'General'}`,
          `**ID:** ${suggestion.messageId}`
        ].join('\n'),
        inline: false
      });
    });

    if (results.length > 5) {
      embed.addFields({
        name: '📋 **Additional Results**',
        value: `**${results.length - 5} more suggestions found.** Use \`/suggestion view list\` to see all results.`,
        inline: false
      });
    }

    embed.setFooter({ text: 'Professional Search System' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Search suggestions error:', error);
    return await createErrorResponse(interaction, 'Failed to search suggestions!');
  }
}

// 📈 ANALYTICS COMMANDS HANDLER
async function handleAnalyticsCommands(interaction, subcommand) {
  switch (subcommand) {
    case 'stats':
      return await viewStats(interaction);
    case 'leaderboard':
      return await viewLeaderboard(interaction);
    case 'trends':
      return await viewTrends(interaction);
    case 'export':
      return await exportData(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown analytics command!');
  }
}

// 📊 VIEW STATISTICS
async function viewStats(interaction) {
  const period = interaction.options.getString('period') || 'all';
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData || suggestionData.suggestions.length === 0) {
      return await createErrorResponse(interaction, 'No suggestion data available!');
    }

    const suggestions = suggestionData.suggestions;
    const now = new Date();
    let filteredSuggestions = suggestions;

    // Filter by time period
    if (period !== 'all') {
      const cutoffDate = new Date();
      switch (period) {
        case 'today':
          cutoffDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoffDate.setDate(cutoffDate.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(cutoffDate.getMonth() - 1);
          break;
      }
      filteredSuggestions = suggestions.filter(s =>
        new Date(s.createdAt || Date.now()) >= cutoffDate
      );
    }

    // Calculate statistics
    const totalSuggestions = filteredSuggestions.length;
    const acceptedCount = filteredSuggestions.filter(s => s.status === 'accepted').length;
    const declinedCount = filteredSuggestions.filter(s => s.status === 'declined').length;
    const pendingCount = filteredSuggestions.filter(s => !s.status || s.status === 'pending').length;
    const implementedCount = filteredSuggestions.filter(s => s.status === 'implemented').length;

    const acceptanceRate = totalSuggestions > 0 ? ((acceptedCount / totalSuggestions) * 100).toFixed(1) : 0;
    const implementationRate = acceptedCount > 0 ? ((implementedCount / acceptedCount) * 100).toFixed(1) : 0;

    const embed = new EmbedBuilder()
      .setColor(COLORS.ANALYTICS)
      .setTitle(`${EMOJIS.STATS} Suggestion Statistics`)
      .setDescription(`**📊 Analytics for ${period === 'all' ? 'All Time' : period.charAt(0).toUpperCase() + period.slice(1)}**`)
      .addFields(
        {
          name: '📈 **Overview Statistics**',
          value: [
            `**Total Suggestions:** ${totalSuggestions}`,
            `**Acceptance Rate:** ${acceptanceRate}%`,
            `**Implementation Rate:** ${implementationRate}%`,
            `**Period:** ${period === 'all' ? 'All Time' : period.charAt(0).toUpperCase() + period.slice(1)}`
          ].join('\n'),
          inline: true
        },
        {
          name: '🎯 **Status Breakdown**',
          value: [
            `${EMOJIS.SUCCESS} **Accepted:** ${acceptedCount}`,
            `${EMOJIS.ERROR} **Declined:** ${declinedCount}`,
            `⏳ **Pending:** ${pendingCount}`,
            `🚀 **Implemented:** ${implementedCount}`
          ].join('\n'),
          inline: true
        },
        {
          name: '📊 **Performance Metrics**',
          value: [
            `**Average per Day:** ${period === 'all' ? 'N/A' : (totalSuggestions / getDayCount(period)).toFixed(1)}`,
            `**Most Active Status:** ${getMostActiveStatus(filteredSuggestions)}`,
            `**Success Rate:** ${acceptanceRate}%`,
            `**Quality Score:** ${calculateQualityScore(filteredSuggestions)}/10`
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'Professional Analytics Dashboard' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('View stats error:', error);
    return await createErrorResponse(interaction, 'Failed to generate statistics!');
  }
}

// 🔧 TOOLS COMMANDS HANDLER
async function handleToolsCommands(interaction, subcommand) {
  // Check admin permissions for tools
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
    return await createErrorResponse(interaction, 'You need "Manage Server" permissions to use tools!');
  }

  switch (subcommand) {
    case 'backup':
      return await backupData(interaction);
    case 'restore':
      return await restoreData(interaction);
    case 'migrate':
      return await migrateSuggestions(interaction);
    case 'cleanup':
      return await cleanupDatabase(interaction);
    case 'import':
      return await importSuggestions(interaction);
    case 'duplicate':
      return await findDuplicates(interaction);
    case 'merge':
      return await mergeSuggestions(interaction);
    case 'schedule':
      return await scheduleReview(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown tools command!');
  }
}

// 💾 BACKUP DATA
async function backupData(interaction) {
  const includeVotes = interaction.options.getBoolean('include_votes') || false;
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData) {
      return await createErrorResponse(interaction, 'No suggestion data to backup!');
    }

    const backupId = `backup_${guildId}_${Date.now()}`;
    const backupData = {
      id: backupId,
      guildId: guildId,
      timestamp: new Date(),
      config: suggestionData.config,
      suggestions: suggestionData.suggestions,
      includeVotes: includeVotes,
      version: '1.0'
    };

    // In a real implementation, you would save this to a file or external storage
    // For now, we'll simulate the backup process

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.BACKUP} Backup Created`)
      .setDescription('**💾 Suggestion data has been successfully backed up!**')
      .addFields(
        {
          name: '📊 **Backup Details**',
          value: [
            `**Backup ID:** ${backupId}`,
            `**Timestamp:** ${formatDate(new Date())}`,
            `**Suggestions:** ${suggestionData.suggestions.length}`,
            `**Include Votes:** ${includeVotes ? 'Yes' : 'No'}`,
            `**Size:** ${JSON.stringify(backupData).length} bytes`
          ].join('\n'),
          inline: true
        },
        {
          name: '🔧 **Backup Contents**',
          value: [
            `✅ System Configuration`,
            `✅ All Suggestions`,
            `✅ Status Information`,
            `${includeVotes ? '✅' : '❌'} Voting Data`,
            `✅ Metadata`
          ].join('\n'),
          inline: true
        },
        {
          name: '📝 **Usage Instructions**',
          value: [
            `• Use backup ID to restore data`,
            `• Backup includes all suggestion data`,
            `• Safe to restore on same or different server`,
            `• Use \`/suggestion tools restore\` to restore`
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'Professional Backup System' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Backup data error:', error);
    return await createErrorResponse(interaction, 'Failed to create backup!');
  }
}

// 🧹 CLEANUP DATABASE
async function cleanupDatabase(interaction) {
  const removeDuplicates = interaction.options.getBoolean('remove_duplicates') || false;
  const fixOrphaned = interaction.options.getBoolean('fix_orphaned') || false;
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData) {
      return await createErrorResponse(interaction, 'No suggestion data to cleanup!');
    }

    let cleanupResults = {
      duplicatesRemoved: 0,
      orphanedFixed: 0,
      totalProcessed: suggestionData.suggestions.length
    };

    // Remove duplicates
    if (removeDuplicates) {
      const uniqueSuggestions = [];
      const seen = new Set();

      for (const suggestion of suggestionData.suggestions) {
        const key = suggestion.content.toLowerCase().trim();
        if (!seen.has(key)) {
          seen.add(key);
          uniqueSuggestions.push(suggestion);
        } else {
          cleanupResults.duplicatesRemoved++;
        }
      }

      suggestionData.suggestions = uniqueSuggestions;
    }

    // Fix orphaned records (suggestions without valid message IDs)
    if (fixOrphaned) {
      const validSuggestions = suggestionData.suggestions.filter(suggestion => {
        if (!suggestion.messageId || suggestion.messageId.length < 10) {
          cleanupResults.orphanedFixed++;
          return false;
        }
        return true;
      });

      suggestionData.suggestions = validSuggestions;
    }

    await suggestionData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} Database Cleanup Complete`)
      .setDescription('**🧹 Suggestion database has been cleaned up!**')
      .addFields(
        {
          name: '📊 **Cleanup Results**',
          value: [
            `**Total Processed:** ${cleanupResults.totalProcessed}`,
            `**Duplicates Removed:** ${cleanupResults.duplicatesRemoved}`,
            `**Orphaned Fixed:** ${cleanupResults.orphanedFixed}`,
            `**Final Count:** ${suggestionData.suggestions.length}`
          ].join('\n'),
          inline: true
        },
        {
          name: '🔧 **Operations Performed**',
          value: [
            `${removeDuplicates ? '✅' : '❌'} Remove Duplicates`,
            `${fixOrphaned ? '✅' : '❌'} Fix Orphaned Records`,
            `✅ Database Optimization`,
            `✅ Data Validation`
          ].join('\n'),
          inline: true
        },
        {
          name: '📈 **Performance Impact**',
          value: [
            `• Database size optimized`,
            `• Query performance improved`,
            `• Data integrity ensured`,
            `• Storage space saved`
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'Professional Database Management' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Cleanup database error:', error);
    return await createErrorResponse(interaction, 'Failed to cleanup database!');
  }
}

// 🔍 FIND DUPLICATES
async function findDuplicates(interaction) {
  const similarity = interaction.options.getInteger('similarity') || 80;
  const guildId = interaction.guild.id;

  try {
    const suggestionData = await Suggestion.findOne({ guildId });
    if (!suggestionData || suggestionData.suggestions.length < 2) {
      return await createErrorResponse(interaction, 'Not enough suggestions to check for duplicates!');
    }

    const duplicates = [];
    const suggestions = suggestionData.suggestions;

    // Simple similarity check (in a real implementation, you'd use a more sophisticated algorithm)
    for (let i = 0; i < suggestions.length; i++) {
      for (let j = i + 1; j < suggestions.length; j++) {
        const sim = calculateSimilarity(suggestions[i].content, suggestions[j].content);
        if (sim >= similarity) {
          duplicates.push({
            suggestion1: suggestions[i],
            suggestion2: suggestions[j],
            similarity: sim
          });
        }
      }
    }

    if (duplicates.length === 0) {
      const embed = new EmbedBuilder()
        .setColor(COLORS.SUCCESS)
        .setTitle(`${EMOJIS.SUCCESS} No Duplicates Found`)
        .setDescription(`**🔍 No duplicate suggestions found with ${similarity}% similarity threshold.**`)
        .addFields({
          name: '📊 **Scan Results**',
          value: [
            `**Total Suggestions:** ${suggestions.length}`,
            `**Similarity Threshold:** ${similarity}%`,
            `**Duplicates Found:** 0`,
            `**Database Status:** Clean`
          ].join('\n'),
          inline: true
        })
        .setFooter({ text: 'Professional Duplicate Detection' })
        .setTimestamp();

      return await interaction.reply({ embeds: [embed] });
    }

    const embed = new EmbedBuilder()
      .setColor(COLORS.WARNING)
      .setTitle(`${EMOJIS.WARNING} Duplicates Found`)
      .setDescription(`**🔍 Found ${duplicates.length} potential duplicate pairs!**`)
      .addFields(
        {
          name: '📊 **Scan Results**',
          value: [
            `**Total Suggestions:** ${suggestions.length}`,
            `**Similarity Threshold:** ${similarity}%`,
            `**Duplicate Pairs:** ${duplicates.length}`,
            `**Recommendations:** Review and merge`
          ].join('\n'),
          inline: true
        }
      );

    // Show top 3 duplicate pairs
    duplicates.slice(0, 3).forEach((dup, index) => {
      embed.addFields({
        name: `🔗 **Duplicate Pair #${index + 1}** (${dup.similarity}% similar)`,
        value: [
          `**Suggestion 1:** ${dup.suggestion1.content.substring(0, 100)}...`,
          `**ID 1:** ${dup.suggestion1.messageId}`,
          `**Suggestion 2:** ${dup.suggestion2.content.substring(0, 100)}...`,
          `**ID 2:** ${dup.suggestion2.messageId}`
        ].join('\n'),
        inline: false
      });
    });

    if (duplicates.length > 3) {
      embed.addFields({
        name: '📋 **Additional Duplicates**',
        value: `**${duplicates.length - 3} more duplicate pairs found.** Use \`/suggestion tools merge\` to merge similar suggestions.`,
        inline: false
      });
    }

    embed.setFooter({ text: 'Professional Duplicate Detection' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Find duplicates error:', error);
    return await createErrorResponse(interaction, 'Failed to find duplicates!');
  }
}

// 🔄 LEGACY COMMANDS HANDLER
async function handleLegacyCommands(message) {
  const args = message.content.slice(1).split(/ +/);
  const command = args.shift().toLowerCase();

  if (!message.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
    return message.reply(`${EMOJIS.ERROR} You need "Manage Server" permissions!`);
  }

  // Handle legacy commands for backward compatibility
  if (command === 'suggestionsetup') {
    const channel = message.mentions.channels.first();
    if (!channel) return message.reply(`${EMOJIS.ERROR} Mention a channel!`);

    // Legacy setup logic here
    return message.reply(`${EMOJIS.GEAR} Please use \`/suggestion setup channel\` instead!`);
  }

  return message.reply(`${EMOJIS.INFO} Please use the new slash commands: \`/suggestion\``);
}

// 🛠️ UTILITY HELPER FUNCTIONS
function getStatusEmoji(status) {
  switch (status) {
    case 'accepted': return EMOJIS.SUCCESS;
    case 'declined': return EMOJIS.ERROR;
    case 'implemented': return '🚀';
    case 'considering': return '🤔';
    default: return '⏳';
  }
}

function getDayCount(period) {
  switch (period) {
    case 'today': return 1;
    case 'week': return 7;
    case 'month': return 30;
    default: return 1;
  }
}

function getMostActiveStatus(suggestions) {
  const statusCounts = {};
  suggestions.forEach(s => {
    const status = s.status || 'pending';
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  return Object.keys(statusCounts).reduce((a, b) =>
    statusCounts[a] > statusCounts[b] ? a : b
  );
}

function calculateQualityScore(suggestions) {
  if (suggestions.length === 0) return 0;

  const accepted = suggestions.filter(s => s.status === 'accepted').length;
  const total = suggestions.length;
  const score = (accepted / total) * 10;

  return Math.min(10, Math.max(0, score.toFixed(1)));
}

function calculateSimilarity(str1, str2) {
  // Simple similarity calculation (Levenshtein distance based)
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 100;

  const distance = levenshteinDistance(longer.toLowerCase(), shorter.toLowerCase());
  return ((longer.length - distance) / longer.length) * 100;
}

function levenshteinDistance(str1, str2) {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}

        if (interaction) {
            const subcommand = interaction.options.getSubcommand();
            
            // Berechtigungsprüfung für Slash Commands
            if (!interaction.member.permissions.has(Permissions.FLAGS.MANAGE_GUILD)) {
                return interaction.reply({
                    content: `${EMOJI.ERROR} You need "Manage Server" permissions!`,
                    ephemeral: true
                });
            }

            if (subcommand === 'setup') {
                const channel = interaction.options.getChannel('channel');
                
                if (channel.type !== 'GUILD_TEXT') {
                    return interaction.reply(`${EMOJI.ERROR} Channel must be a text channel!`);
                }

                let suggestionSetup = await Suggestion.findOne({ guildId });
                if (!suggestionSetup) {
                    suggestionSetup = new Suggestion({
                        guildId,
                        channelId: channel.id,
                        suggestions: []
                    });
                    await suggestionSetup.save();
                    return interaction.reply(`${EMOJI.GEAR} Suggestion channel set to ${channel}!`);
                }

                suggestionSetup.channelId = channel.id;
                await suggestionSetup.save();
                return interaction.reply(`${EMOJI.GEAR} Suggestion channel updated to ${channel}!`);

            } else if (subcommand === 'accept' || subcommand === 'decline') {
                const messageId = interaction.options.getString('message_id');
                const suggestionData = await Suggestion.findOne({ guildId });

                if (!suggestionData) {
                    return interaction.reply(`${EMOJI.ERROR} Suggestion system not initialized!`);
                }

                const suggestion = suggestionData.suggestions.find(s => s.messageId === messageId);
                if (!suggestion) {
                    return interaction.reply(`${EMOJI.ERROR} Suggestion not found!`);
                }

                try {
                    const channel = await interaction.guild.channels.fetch(suggestionData.channelId);
                    const targetMessage = await channel.messages.fetch(messageId);
                    const oldEmbed = targetMessage.embeds[0];
                    
                    const newEmbed = EmbedBuilder.from(oldEmbed)
                        .setColor(subcommand === 'accept' ? Colors.Green : Colors.Red)
                        .addFields({
                            name: 'Status',
                            value: subcommand === 'accept' 
                                ? `${EMOJI.SUCCESS} Accepted by ${interaction.user.tag}` 
                                : `${EMOJI.ERROR} Declined by ${interaction.user.tag}`
                        });

                    await targetMessage.edit({ embeds: [newEmbed] });
                    
                    // Datenbank aktualisieren
                    suggestion.accepted = subcommand === 'accept';
                    suggestion.declined = subcommand === 'decline';
                    await suggestionData.save();

                    return interaction.reply(
                        `${subcommand === 'accept' ? EMOJI.SUCCESS : EMOJI.ERROR} Suggestion ${
                            subcommand === 'accept' ? 'accepted' : 'declined'
                        }!`
                    );
                } catch (error) {
                    console.error(error);
                    return interaction.reply(`${EMOJI.ERROR} Error processing suggestion!`);
                }
            }
        } else {
            // Message-Befehle (mit Berechtigungsprüfung)
            const args = message.content.slice(1).split(/ +/);
            const command = args.shift().toLowerCase();

            if (!message.member.permissions.has(Permissions.FLAGS.MANAGE_GUILD)) {
                return message.reply(`${EMOJI.ERROR} You need "Manage Server" permissions!`);
            }

            if (command === 'suggestionsetup') {
                const channel = message.mentions.channels.first();
                if (!channel) return message.reply(`${EMOJI.ERROR} Mention a channel!`);
                
                let suggestionSetup = await Suggestion.findOne({ guildId });
                if (!suggestionSetup) {
                    suggestionSetup = new Suggestion({
                        guildId,
                        channelId: channel.id,
                        suggestions: []
                    });
                    await suggestionSetup.save();
                    return message.reply(`${EMOJI.GEAR} Suggestion channel set to ${channel}!`);
                }

                suggestionSetup.channelId = channel.id;
                await suggestionSetup.save();
                return message.reply(`${EMOJI.GEAR} Suggestion channel updated to ${channel}!`);

            } else if (command === 'suggestionaccept' || command === 'suggestiondecline') {
                const messageId = args[0];
                if (!messageId) return message.reply(`${EMOJI.ERROR} Provide a message ID!`);

                const suggestionData = await Suggestion.findOne({ guildId });
                if (!suggestionData) return message.reply(`${EMOJI.ERROR} Suggestion system not initialized!`);

                const suggestion = suggestionData.suggestions.find(s => s.messageId === messageId);
                if (!suggestion) return message.reply(`${EMOJI.ERROR} Suggestion not found!`);

                try {
                    const channel = await message.guild.channels.fetch(suggestionData.channelId);
                    const targetMessage = await channel.messages.fetch(messageId);
                    const oldEmbed = targetMessage.embeds[0];
                    
                    const newEmbed = EmbedBuilder.from(oldEmbed)
                        .setColor(command === 'suggestionaccept' ? Colors.Green : Colors.Red)
                        .addFields({
                            name: 'Status',
                            value: command === 'suggestionaccept' 
                                ? `${EMOJI.SUCCESS} Accepted by ${message.author.tag}` 
                                : `${EMOJI.ERROR} Declined by ${message.author.tag}`
                        });

                    await targetMessage.edit({ embeds: [newEmbed] });
                    
                    suggestion.accepted = command === 'suggestionaccept';
                    suggestion.declined = command === 'suggestiondecline';
                    await suggestionData.save();

                    return message.reply(
                        `${command === 'suggestionaccept' ? EMOJI.SUCCESS : EMOJI.ERROR} Suggestion ${
                            command === 'suggestionaccept' ? 'accepted' : 'declined'
                        }!`
                    );
                } catch (error) {
                    console.error(error);
                    return message.reply(`${EMOJI.ERROR} Error processing suggestion!`);
                }
            }
        }
    }
};