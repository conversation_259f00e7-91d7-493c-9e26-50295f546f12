// 🤖 PROFESSIONAL CHATGPT SYSTEM - ENTERPRISE AI INTEGRATION
// Advanced AI conversation system with comprehensive features for academic excellence

const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const ChatGPT = require('../../database/models/chatgpt');

// 🎨 PROFESSIONAL DESIGN CONSTANTS
const COLORS = {
  PRIMARY: '#00D4AA',
  SUCCESS: '#2ecc71',
  WARNING: '#f39c12',
  ERROR: '#e74c3c',
  INFO: '#3498db',
  AI: '#9b59b6',
  PREMIUM: '#f1c40f',
  CHAT: '#1abc9c',
  SETUP: '#e67e22',
  CONFIG: '#34495e'
};

const EMOJIS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  AI: '🤖',
  CHAT: '💬',
  SETUP: '⚙️',
  CONFIG: '🔧',
  PREMIUM: '⭐',
  BRAIN: '🧠',
  MAGIC: '✨',
  ROCKET: '🚀',
  GEAR: '⚙️',
  SHIELD: '🛡️',
  CROWN: '👑',
  FIRE: '🔥',
  DIAMOND: '💎',
  STAR: '⭐'
};

// 🧠 AI MODEL CONFIGURATIONS
const AI_MODELS = {
  'gpt-4': {
    name: 'GPT-4 Turbo',
    description: 'Most advanced AI model',
    maxTokens: 4000,
    premium: true,
    emoji: '👑'
  },
  'gpt-3.5-turbo': {
    name: 'GPT-3.5 Turbo',
    description: 'Fast and efficient AI',
    maxTokens: 2000,
    premium: false,
    emoji: '⚡'
  },
  'claude': {
    name: 'Claude AI',
    description: 'Anthropic\'s advanced AI',
    maxTokens: 3000,
    premium: true,
    emoji: '🎭'
  }
};

// 🎯 CONVERSATION PERSONALITIES
const PERSONALITIES = {
  'assistant': {
    name: 'Professional Assistant',
    prompt: 'You are DAVIO, a professional and helpful Discord bot assistant.',
    emoji: '🤖'
  },
  'friend': {
    name: 'Friendly Companion',
    prompt: 'You are DAVIO, a friendly and casual Discord bot who loves to chat.',
    emoji: '😊'
  },
  'teacher': {
    name: 'Educational Tutor',
    prompt: 'You are DAVIO, an educational AI tutor who explains things clearly.',
    emoji: '👨‍🏫'
  },
  'creative': {
    name: 'Creative Writer',
    prompt: 'You are DAVIO, a creative AI who loves storytelling and imagination.',
    emoji: '🎨'
  },
  'technical': {
    name: 'Technical Expert',
    prompt: 'You are DAVIO, a technical AI expert specializing in programming and technology.',
    emoji: '💻'
  }
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('chatgpt')
    .setDescription('🤖 Professional AI ChatGPT System with Advanced Features')
    
    // 🔧 SETUP SUBCOMMAND GROUP
    .addSubcommandGroup(group =>
      group.setName('setup')
        .setDescription('⚙️ AI System Configuration & Setup')
        .addSubcommand(subcommand =>
          subcommand.setName('channel')
            .setDescription('Setup AI chat channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel for AI conversations')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('apikey')
            .setDescription('Configure OpenAI API key (Admin only)')
            .addStringOption(option =>
              option.setName('key')
                .setDescription('Your OpenAI API key')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('model')
            .setDescription('Set default AI model')
            .addStringOption(option =>
              option.setName('model')
                .setDescription('AI model to use')
                .setRequired(true)
                .addChoices(
                  { name: '👑 GPT-4 Turbo (Premium)', value: 'gpt-4' },
                  { name: '⚡ GPT-3.5 Turbo (Standard)', value: 'gpt-3.5-turbo' },
                  { name: '🎭 Claude AI (Premium)', value: 'claude' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('personality')
            .setDescription('Set AI personality')
            .addStringOption(option =>
              option.setName('type')
                .setDescription('AI personality type')
                .setRequired(true)
                .addChoices(
                  { name: '🤖 Professional Assistant', value: 'assistant' },
                  { name: '😊 Friendly Companion', value: 'friend' },
                  { name: '👨‍🏫 Educational Tutor', value: 'teacher' },
                  { name: '🎨 Creative Writer', value: 'creative' },
                  { name: '💻 Technical Expert', value: 'technical' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('limits')
            .setDescription('Configure usage limits')
            .addIntegerOption(option =>
              option.setName('daily_limit')
                .setDescription('Daily messages per user')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(1000))
            .addIntegerOption(option =>
              option.setName('max_tokens')
                .setDescription('Maximum tokens per response')
                .setRequired(false)
                .setMinValue(100)
                .setMaxValue(4000))))
    
    // 💬 CHAT SUBCOMMAND GROUP
    .addSubcommandGroup(group =>
      group.setName('chat')
        .setDescription('💬 AI Conversation & Interaction')
        .addSubcommand(subcommand =>
          subcommand.setName('ask')
            .setDescription('Ask AI a question')
            .addStringOption(option =>
              option.setName('question')
                .setDescription('Your question for the AI')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('model')
                .setDescription('AI model to use')
                .setRequired(false)
                .addChoices(
                  { name: '👑 GPT-4 Turbo', value: 'gpt-4' },
                  { name: '⚡ GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
                  { name: '🎭 Claude AI', value: 'claude' }
                ))
            .addBooleanOption(option =>
              option.setName('private')
                .setDescription('Make response private')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('conversation')
            .setDescription('Start a conversation thread')
            .addStringOption(option =>
              option.setName('topic')
                .setDescription('Conversation topic')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('personality')
                .setDescription('AI personality for this conversation')
                .setRequired(false)
                .addChoices(
                  { name: '🤖 Professional', value: 'assistant' },
                  { name: '😊 Friendly', value: 'friend' },
                  { name: '👨‍🏫 Educational', value: 'teacher' },
                  { name: '🎨 Creative', value: 'creative' },
                  { name: '💻 Technical', value: 'technical' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('continue')
            .setDescription('Continue previous conversation')
            .addStringOption(option =>
              option.setName('message')
                .setDescription('Your message to continue the conversation')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('reset')
            .setDescription('Reset conversation history')
            .addBooleanOption(option =>
              option.setName('confirm')
                .setDescription('Confirm reset')
                .setRequired(true))))
    
    // 📊 MANAGE SUBCOMMAND GROUP
    .addSubcommandGroup(group =>
      group.setName('manage')
        .setDescription('🛠️ AI System Management')
        .addSubcommand(subcommand =>
          subcommand.setName('status')
            .setDescription('View AI system status'))
        .addSubcommand(subcommand =>
          subcommand.setName('usage')
            .setDescription('View usage statistics')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to check usage for')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('history')
            .setDescription('View conversation history')
            .addIntegerOption(option =>
              option.setName('limit')
                .setDescription('Number of messages to show')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(50)))
        .addSubcommand(subcommand =>
          subcommand.setName('clear')
            .setDescription('Clear conversation data (Admin only)')
            .addStringOption(option =>
              option.setName('type')
                .setDescription('What to clear')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ All Conversations', value: 'all' },
                  { name: '📊 Usage Statistics', value: 'stats' },
                  { name: '⚙️ Configuration', value: 'config' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('export')
            .setDescription('Export conversation data')
            .addStringOption(option =>
              option.setName('format')
                .setDescription('Export format')
                .setRequired(false)
                .addChoices(
                  { name: '📄 JSON', value: 'json' },
                  { name: '📝 TXT', value: 'txt' },
                  { name: '📊 CSV', value: 'csv' }
                )))),

  async execute(interaction) {
    const subcommandGroup = interaction.options.getSubcommandGroup();
    const subcommand = interaction.options.getSubcommand();

    try {
      switch (subcommandGroup) {
        case 'setup':
          return await handleSetupCommands(interaction, subcommand);
        case 'chat':
          return await handleChatCommands(interaction, subcommand);
        case 'manage':
          return await handleManageCommands(interaction, subcommand);
        default:
          return await createErrorResponse(interaction, 'Unknown command group!');
      }
    } catch (error) {
      console.error('ChatGPT Command Error:', error);
      return await createErrorResponse(interaction, 'An error occurred while processing your request!');
    }
  }
};

// 🛠️ UTILITY FUNCTIONS
async function createErrorResponse(interaction, message) {
  const embed = new EmbedBuilder()
    .setColor(COLORS.ERROR)
    .setTitle(`${EMOJIS.ERROR} Error`)
    .setDescription(message)
    .setTimestamp();

  return await interaction.reply({ embeds: [embed], ephemeral: true });
}

async function createSuccessResponse(interaction, title, description, fields = []) {
  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} ${title}`)
    .setDescription(description)
    .setTimestamp();

  if (fields.length > 0) {
    embed.addFields(fields);
  }

  return await interaction.reply({ embeds: [embed] });
}

// ⚙️ SETUP COMMANDS HANDLER
async function handleSetupCommands(interaction, subcommand) {
  // Check admin permissions for setup commands
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
    return await createErrorResponse(interaction, 'You need "Manage Server" permissions to use setup commands!');
  }

  switch (subcommand) {
    case 'channel':
      return await setupChannel(interaction);
    case 'apikey':
      return await setupApiKey(interaction);
    case 'model':
      return await setupModel(interaction);
    case 'personality':
      return await setupPersonality(interaction);
    case 'limits':
      return await setupLimits(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown setup command!');
  }
}

// 🏗️ SETUP CHANNEL
async function setupChannel(interaction) {
  const channel = interaction.options.getChannel('channel');
  const guildId = interaction.guild.id;

  try {
    let chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData) {
      chatgptData = new ChatGPT({
        guildId: guildId,
        config: {
          channelId: channel.id,
          model: 'gpt-3.5-turbo',
          personality: 'assistant',
          dailyLimit: 50,
          maxTokens: 2000,
          apiKey: null
        }
      });
    } else {
      chatgptData.config.channelId = channel.id;
    }

    await chatgptData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} AI Channel Setup Complete`)
      .setDescription('**🤖 ChatGPT system has been successfully configured!**')
      .addFields(
        {
          name: '📊 **Setup Details**',
          value: [
            `**Channel:** ${channel}`,
            `**Model:** ${AI_MODELS[chatgptData.config.model].name}`,
            `**Personality:** ${PERSONALITIES[chatgptData.config.personality].name}`,
            `**Daily Limit:** ${chatgptData.config.dailyLimit} messages`,
            `**Max Tokens:** ${chatgptData.config.maxTokens}`
          ].join('\n'),
          inline: true
        },
        {
          name: '🚀 **Next Steps**',
          value: [
            `• Configure API key with \`/chatgpt setup apikey\``,
            `• Customize AI model with \`/chatgpt setup model\``,
            `• Set personality with \`/chatgpt setup personality\``,
            `• Start chatting with \`/chatgpt chat ask\``,
            `• View status with \`/chatgpt manage status\``
          ].join('\n'),
          inline: true
        },
        {
          name: '💡 **Pro Tips**',
          value: [
            `• Use different personalities for different purposes`,
            `• Set appropriate daily limits to manage usage`,
            `• Premium models offer better responses`,
            `• Use private mode for sensitive conversations`
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'Professional AI System' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup channel error:', error);
    return await createErrorResponse(interaction, 'Failed to setup AI channel!');
  }
}

// 🔑 SETUP API KEY
async function setupApiKey(interaction) {
  const apiKey = interaction.options.getString('key');
  const guildId = interaction.guild.id;

  try {
    let chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData) {
      return await createErrorResponse(interaction, 'Please setup a channel first with `/chatgpt setup channel`!');
    }

    // Validate API key format (basic check)
    if (!apiKey.startsWith('sk-') || apiKey.length < 20) {
      return await createErrorResponse(interaction, 'Invalid API key format! Please provide a valid OpenAI API key.');
    }

    chatgptData.config.apiKey = apiKey;
    await chatgptData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} API Key Configured`)
      .setDescription('**🔑 OpenAI API key has been securely saved!**')
      .addFields(
        {
          name: '🔒 **Security Information**',
          value: [
            `**Key Status:** ✅ Valid Format`,
            `**Storage:** 🔐 Encrypted Database`,
            `**Access:** 👑 Admin Only`,
            `**Usage:** 📊 Tracked & Limited`
          ].join('\n'),
          inline: true
        },
        {
          name: '🚀 **System Ready**',
          value: [
            `• AI system is now fully operational`,
            `• All models are available for use`,
            `• Usage tracking is active`,
            `• Ready for conversations!`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'API Key Configured Successfully' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed], ephemeral: true });
  } catch (error) {
    console.error('Setup API key error:', error);
    return await createErrorResponse(interaction, 'Failed to configure API key!');
  }
}

// 🤖 SETUP MODEL
async function setupModel(interaction) {
  const model = interaction.options.getString('model');
  const guildId = interaction.guild.id;

  try {
    let chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData) {
      return await createErrorResponse(interaction, 'Please setup a channel first with `/chatgpt setup channel`!');
    }

    const modelInfo = AI_MODELS[model];
    chatgptData.config.model = model;
    chatgptData.config.maxTokens = modelInfo.maxTokens;
    await chatgptData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} AI Model Updated`)
      .setDescription(`**${modelInfo.emoji} Now using ${modelInfo.name}!**`)
      .addFields(
        {
          name: '🤖 **Model Details**',
          value: [
            `**Name:** ${modelInfo.name}`,
            `**Description:** ${modelInfo.description}`,
            `**Max Tokens:** ${modelInfo.maxTokens}`,
            `**Type:** ${modelInfo.premium ? '👑 Premium' : '⚡ Standard'}`
          ].join('\n'),
          inline: true
        },
        {
          name: '⚡ **Performance**',
          value: [
            `• Response quality: ${modelInfo.premium ? 'Excellent' : 'Good'}`,
            `• Speed: ${model.includes('3.5') ? 'Very Fast' : 'Fast'}`,
            `• Context: ${modelInfo.maxTokens} tokens`,
            `• Cost: ${modelInfo.premium ? 'Higher' : 'Lower'}`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'AI Model Configuration' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup model error:', error);
    return await createErrorResponse(interaction, 'Failed to update AI model!');
  }
}

// 🎭 SETUP PERSONALITY
async function setupPersonality(interaction) {
  const personality = interaction.options.getString('type');
  const guildId = interaction.guild.id;

  try {
    let chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData) {
      return await createErrorResponse(interaction, 'Please setup a channel first with `/chatgpt setup channel`!');
    }

    const personalityInfo = PERSONALITIES[personality];
    chatgptData.config.personality = personality;
    await chatgptData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} AI Personality Updated`)
      .setDescription(`**${personalityInfo.emoji} Now using ${personalityInfo.name}!**`)
      .addFields(
        {
          name: '🎭 **Personality Details**',
          value: [
            `**Name:** ${personalityInfo.name}`,
            `**Style:** ${personalityInfo.prompt}`,
            `**Best For:** ${getPersonalityUseCase(personality)}`,
            `**Tone:** ${getPersonalityTone(personality)}`
          ].join('\n'),
          inline: true
        },
        {
          name: '💡 **Usage Examples**',
          value: getPersonalityExamples(personality).join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'AI Personality Configuration' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup personality error:', error);
    return await createErrorResponse(interaction, 'Failed to update AI personality!');
  }
}

// 📅 UTILITY HELPER FUNCTIONS
function formatDate(date) {
  return new Intl.DateTimeFormat('de-DE', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

function getPersonalityUseCase(personality) {
  const useCases = {
    'assistant': 'General help and information',
    'friend': 'Casual conversations and fun',
    'teacher': 'Learning and explanations',
    'creative': 'Storytelling and imagination',
    'technical': 'Programming and tech support'
  };
  return useCases[personality] || 'General purpose';
}

function getPersonalityTone(personality) {
  const tones = {
    'assistant': 'Professional and helpful',
    'friend': 'Casual and friendly',
    'teacher': 'Educational and patient',
    'creative': 'Imaginative and inspiring',
    'technical': 'Precise and detailed'
  };
  return tones[personality] || 'Balanced';
}

function getPersonalityExamples(personality) {
  const examples = {
    'assistant': ['• Help with tasks', '• Answer questions', '• Provide information'],
    'friend': ['• Casual chatting', '• Fun conversations', '• Emotional support'],
    'teacher': ['• Explain concepts', '• Help with homework', '• Educational content'],
    'creative': ['• Write stories', '• Generate ideas', '• Creative projects'],
    'technical': ['• Code help', '• Tech support', '• Programming advice']
  };
  return examples[personality] || ['• General assistance'];
}

// 📊 SETUP LIMITS
async function setupLimits(interaction) {
  const dailyLimit = interaction.options.getInteger('daily_limit');
  const maxTokens = interaction.options.getInteger('max_tokens');
  const guildId = interaction.guild.id;

  try {
    let chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData) {
      return await createErrorResponse(interaction, 'Please setup a channel first with `/chatgpt setup channel`!');
    }

    // Update limits if provided
    if (dailyLimit !== null) {
      chatgptData.config.dailyLimit = dailyLimit;
    }
    if (maxTokens !== null) {
      chatgptData.config.maxTokens = maxTokens;
    }

    await chatgptData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SUCCESS} Usage Limits Updated`)
      .setDescription('**📊 AI usage limits have been configured!**')
      .addFields(
        {
          name: '📊 **Current Limits**',
          value: [
            `**Daily Messages:** ${chatgptData.config.dailyLimit} per user`,
            `**Max Tokens:** ${chatgptData.config.maxTokens} per response`,
            `**Cooldown:** ${chatgptData.config.cooldown} seconds`,
            `**Model:** ${AI_MODELS[chatgptData.config.model].name}`
          ].join('\n'),
          inline: true
        },
        {
          name: '⚡ **Performance Impact**',
          value: [
            `• Higher limits = more usage`,
            `• Lower limits = better control`,
            `• Balanced settings recommended`,
            `• Monitor usage regularly`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({ text: 'AI Usage Configuration' })
      .setTimestamp();

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Setup limits error:', error);
    return await createErrorResponse(interaction, 'Failed to update usage limits!');
  }
}

// 💬 CHAT COMMANDS HANDLER
async function handleChatCommands(interaction, subcommand) {
  switch (subcommand) {
    case 'ask':
      return await chatAsk(interaction);
    case 'conversation':
      return await chatConversation(interaction);
    case 'continue':
      return await chatContinue(interaction);
    case 'reset':
      return await chatReset(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown chat command!');
  }
}

// 🤖 CHAT ASK
async function chatAsk(interaction) {
  const question = interaction.options.getString('question');
  const model = interaction.options.getString('model');
  const isPrivate = interaction.options.getBoolean('private') || false;
  const guildId = interaction.guild.id;
  const userId = interaction.user.id;

  try {
    const chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData || !chatgptData.config.channelId) {
      return await createErrorResponse(interaction, 'AI system not configured! Ask an admin to use `/chatgpt setup channel` first.');
    }

    if (!chatgptData.config.apiKey) {
      return await createErrorResponse(interaction, 'API key not configured! Ask an admin to set it up first.');
    }

    // Check if in correct channel (unless private)
    if (!isPrivate && interaction.channel.id !== chatgptData.config.channelId) {
      return await createErrorResponse(interaction, `Please use the AI channel: <#${chatgptData.config.channelId}>`);
    }

    // Check user limits
    const userUsage = chatgptData.userUsage.find(u => u.userId === userId);
    if (userUsage && userUsage.daily.messages >= chatgptData.config.dailyLimit) {
      return await createErrorResponse(interaction, `Daily limit reached! You can send ${chatgptData.config.dailyLimit} messages per day.`);
    }

    await interaction.deferReply({ ephemeral: isPrivate });

    // Simulate AI response (in real implementation, you would call OpenAI API)
    const selectedModel = model || chatgptData.config.model;
    const personality = PERSONALITIES[chatgptData.config.personality];

    // Simulate response generation
    const responseTime = Math.random() * 2000 + 1000; // 1-3 seconds
    await new Promise(resolve => setTimeout(resolve, responseTime));

    const aiResponse = `**${personality.emoji} ${personality.name} Response:**\n\nI understand your question: "${question}"\n\nThis is a simulated AI response using ${AI_MODELS[selectedModel].name}. In a real implementation, this would be generated by the actual AI model with your API key.\n\n*Response generated in ${Math.round(responseTime)}ms*`;

    // Update usage statistics
    if (!userUsage) {
      chatgptData.userUsage.push({
        userId: userId,
        daily: { messages: 1, tokens: 150, cost: 0.001, date: new Date() },
        total: { messages: 1, tokens: 150, cost: 0.001, conversations: 0 }
      });
    } else {
      userUsage.daily.messages++;
      userUsage.daily.tokens += 150;
      userUsage.total.messages++;
      userUsage.total.tokens += 150;
      userUsage.lastUsed = new Date();
    }

    await chatgptData.save();

    const embed = new EmbedBuilder()
      .setColor(COLORS.AI)
      .setTitle(`${EMOJIS.AI} AI Response`)
      .setDescription(aiResponse)
      .addFields(
        {
          name: '🤖 **Model Info**',
          value: [
            `**Model:** ${AI_MODELS[selectedModel].name}`,
            `**Personality:** ${personality.name}`,
            `**Tokens:** ~150`,
            `**Response Time:** ${Math.round(responseTime)}ms`
          ].join('\n'),
          inline: true
        },
        {
          name: '📊 **Your Usage**',
          value: [
            `**Today:** ${userUsage?.daily.messages || 1}/${chatgptData.config.dailyLimit}`,
            `**Total:** ${userUsage?.total.messages || 1} messages`,
            `**Tokens Used:** ${userUsage?.total.tokens || 150}`,
            `**Status:** ${isPrivate ? '🔒 Private' : '🌐 Public'}`
          ].join('\n'),
          inline: true
        }
      )
      .setFooter({
        text: `Professional AI System • ${isPrivate ? 'Private Response' : 'Public Response'}`,
        iconURL: interaction.user.displayAvatarURL()
      })
      .setTimestamp();

    return await interaction.editReply({ embeds: [embed] });
  } catch (error) {
    console.error('Chat ask error:', error);
    return await interaction.editReply({
      content: `${EMOJIS.ERROR} An error occurred while processing your request!`
    });
  }
}

// 🛠️ MANAGE COMMANDS HANDLER
async function handleManageCommands(interaction, subcommand) {
  switch (subcommand) {
    case 'status':
      return await manageStatus(interaction);
    case 'usage':
      return await manageUsage(interaction);
    case 'history':
      return await manageHistory(interaction);
    case 'clear':
      return await manageClear(interaction);
    case 'export':
      return await manageExport(interaction);
    default:
      return await createErrorResponse(interaction, 'Unknown manage command!');
  }
}

// 📊 MANAGE STATUS
async function manageStatus(interaction) {
  const guildId = interaction.guild.id;

  try {
    const chatgptData = await ChatGPT.findOne({ guildId });
    if (!chatgptData) {
      return await createErrorResponse(interaction, 'AI system not configured! Use `/chatgpt setup channel` first.');
    }

    const config = chatgptData.config;
    const analytics = chatgptData.analytics;
    const isConfigured = config.channelId && config.apiKey;
    const channel = config.channelId ? `<#${config.channelId}>` : 'Not set';

    const embed = new EmbedBuilder()
      .setColor(isConfigured ? COLORS.SUCCESS : COLORS.WARNING)
      .setTitle(`${EMOJIS.AI} AI System Status`)
      .setDescription(`**🤖 ChatGPT System Overview**`)
      .addFields(
        {
          name: '⚙️ **Configuration**',
          value: [
            `**Status:** ${isConfigured ? '✅ Operational' : '⚠️ Incomplete'}`,
            `**Channel:** ${channel}`,
            `**Model:** ${AI_MODELS[config.model].name}`,
            `**Personality:** ${PERSONALITIES[config.personality].name}`,
            `**API Key:** ${config.apiKey ? '✅ Configured' : '❌ Missing'}`
          ].join('\n'),
          inline: true
        },
        {
          name: '📊 **Usage Limits**',
          value: [
            `**Daily Limit:** ${config.dailyLimit} messages`,
            `**Max Tokens:** ${config.maxTokens}`,
            `**Cooldown:** ${config.cooldown} seconds`,
            `**Memory:** ${config.conversationMemory ? '✅ Enabled' : '❌ Disabled'}`,
            `**Moderation:** ${config.autoModeration ? '✅ Active' : '❌ Inactive'}`
          ].join('\n'),
          inline: true
        },
        {
          name: '📈 **Analytics**',
          value: [
            `**Total Conversations:** ${analytics.totalConversations}`,
            `**Total Messages:** ${analytics.totalMessages}`,
            `**Total Tokens:** ${analytics.totalTokens.toLocaleString()}`,
            `**Active Users:** ${analytics.activeUsers}`,
            `**Popular Model:** ${analytics.popularModel}`
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'AI System Status Dashboard' })
      .setTimestamp();

    if (!isConfigured) {
      embed.addFields({
        name: '🚀 **Setup Required**',
        value: [
          `• Configure API key: \`/chatgpt setup apikey\``,
          `• Set AI model: \`/chatgpt setup model\``,
          `• Choose personality: \`/chatgpt setup personality\``,
          `• Adjust limits: \`/chatgpt setup limits\``
        ].join('\n'),
        inline: false
      });
    }

    return await interaction.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Manage status error:', error);
    return await createErrorResponse(interaction, 'Failed to get system status!');
  }
}
