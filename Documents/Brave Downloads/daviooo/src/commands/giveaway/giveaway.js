// Professional Modular Giveaway Command
const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const GiveawayManager = require('../../modules/giveaway/GiveawayManager');
const GiveawayButtons = require('../../modules/giveaway/GiveawayButtons');
const GiveawayValidator = require('../../modules/giveaway/GiveawayValidator');
const logger = require('../../utils/consoleLogger');

// Create global giveaway manager instance
let giveawayManager;

module.exports = {
  data: new SlashCommandBuilder()
    .setName('giveaway')
    .setDescription('🎉 Professional giveaway management system')
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
    .addSubcommand(subcommand =>
      subcommand
        .setName('start')
        .setDescription('🎉 Start a new giveaway')
        .addStringOption(option =>
          option.setName('duration')
            .setDescription('Duration (1m, 1h, 1d, etc.)')
            .setRequired(true))
        .addIntegerOption(option =>
          option.setName('winners')
            .setDescription('Number of winners (1-10)')
            .setRequired(true)
            .setMinValue(1)
            .setMaxValue(10))
        .addStringOption(option =>
          option.setName('prize')
            .setDescription('Prize description')
            .setRequired(true))
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Channel to start the giveaway in (default: current channel)')))
    .addSubcommand(subcommand =>
      subcommand
        .setName('end')
        .setDescription('🏁 End a giveaway early')
        .addStringOption(option =>
          option.setName('message_id')
            .setDescription('Message ID of the giveaway to end')
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('reroll')
        .setDescription('🎲 Reroll winners for an ended giveaway')
        .addStringOption(option =>
          option.setName('message_id')
            .setDescription('Message ID of the ended giveaway')
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('list')
        .setDescription('📋 List active giveaways in this server'))
    .addSubcommand(subcommand =>
      subcommand
        .setName('stats')
        .setDescription('📊 Show giveaway statistics for this server')),

  async execute(interaction) {
    try {
      // Initialize giveaway manager if not exists
      if (!giveawayManager) {
        giveawayManager = new GiveawayManager(interaction.client);
      }

      // Validate guild context
      const guildValidation = GiveawayValidator.validateGuildContext(interaction);
      if (!guildValidation.valid) {
        return await interaction.reply({
          content: `❌ ${guildValidation.error}`,
          ephemeral: true
        });
      }

      // Validate bot permissions
      const botValidation = GiveawayValidator.validateBotPermissions(interaction.guild);
      if (!botValidation.valid) {
        return await interaction.reply({
          content: `❌ ${botValidation.error}`,
          ephemeral: true
        });
      }

      const subCommand = interaction.options.getSubcommand();

      switch (subCommand) {
        case 'start':
          await giveawayManager.startGiveaway(interaction);
          break;

        case 'end':
          await giveawayManager.endGiveawayManually(interaction);
          break;

        case 'reroll':
          await giveawayManager.rerollGiveaway(interaction);
          break;

        case 'list':
          await this.handleList(interaction);
          break;

        case 'stats':
          await this.handleStats(interaction);
          break;

        default:
          await interaction.reply({
            content: '❌ Unknown subcommand!',
            ephemeral: true
          });
      }

    } catch (error) {
      logger.error(`Error in giveaway command: ${error.message}`);
      
      const errorMessage = '❌ An unexpected error occurred. Please try again.';
      
      if (interaction.deferred || interaction.replied) {
        await interaction.editReply({ content: errorMessage });
      } else {
        await interaction.reply({ content: errorMessage, ephemeral: true });
      }
    }
  },

  /**
   * Handle list subcommand
   */
  async handleList(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });

      const Giveaway = require('../../database/models/Giveaway');
      const activeGiveaways = await Giveaway.find({
        guildId: interaction.guildId,
        ended: false
      }).sort({ endsAt: 1 }).limit(10);

      if (activeGiveaways.length === 0) {
        return await interaction.editReply({
          content: '📋 No active giveaways in this server.',
          ephemeral: true
        });
      }

      const { EmbedBuilder } = require('discord.js');
      const embed = new EmbedBuilder()
        .setColor(0xFF1493)
        .setTitle('📋 Active Giveaways')
        .setDescription(`Found ${activeGiveaways.length} active giveaway(s)`)
        .setTimestamp();

      for (const giveaway of activeGiveaways) {
        const channel = interaction.guild.channels.cache.get(giveaway.channelId);
        const channelName = channel ? channel.name : 'Unknown Channel';
        
        embed.addFields({
          name: `🎉 ${giveaway.prize}`,
          value: `**Channel**: #${channelName}
**Winners**: ${giveaway.winnerCount}
**Participants**: ${giveaway.participants.length}
**Ends**: <t:${Math.floor(giveaway.endsAt.getTime() / 1000)}:R>
**Message ID**: \`${giveaway.messageId}\``,
          inline: false
        });
      }

      await interaction.editReply({ embeds: [embed], ephemeral: true });

    } catch (error) {
      logger.error(`Error listing giveaways: ${error.message}`);
      await interaction.editReply({
        content: '❌ Failed to list giveaways.',
        ephemeral: true
      });
    }
  },

  /**
   * Handle stats subcommand
   */
  async handleStats(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });

      const GiveawayUtils = require('../../modules/giveaway/GiveawayUtils');
      const stats = await GiveawayUtils.getGiveawayStats(interaction.guildId);

      const { EmbedBuilder } = require('discord.js');
      const embed = new EmbedBuilder()
        .setColor(0xFF1493)
        .setTitle('📊 Giveaway Statistics')
        .setDescription(`Statistics for **${interaction.guild.name}**`)
        .addFields(
          { name: '🎉 Total Giveaways', value: stats.total.toString(), inline: true },
          { name: '🟢 Active', value: stats.active.toString(), inline: true },
          { name: '✅ Completed', value: stats.completed.toString(), inline: true }
        )
        .setFooter({
          text: `Requested by ${interaction.user.username}`,
          iconURL: interaction.user.displayAvatarURL()
        })
        .setTimestamp();

      await interaction.editReply({ embeds: [embed], ephemeral: true });

    } catch (error) {
      logger.error(`Error showing giveaway stats: ${error.message}`);
      await interaction.editReply({
        content: '❌ Failed to get giveaway statistics.',
        ephemeral: true
      });
    }
  },

  /**
   * Initialize giveaway system
   */
  async initializeGiveawaySystem(client) {
    try {
      if (!giveawayManager) {
        giveawayManager = new GiveawayManager(client);
      }
      
      await giveawayManager.scheduleGiveaways();
      logger.success('Giveaway system initialized successfully');
    } catch (error) {
      logger.error(`Failed to initialize giveaway system: ${error.message}`);
    }
  },

  /**
   * Get giveaway manager instance
   */
  getGiveawayManager() {
    return giveawayManager;
  }
};

// Export button handlers for the interaction handler
module.exports.buttons = GiveawayButtons.getHandlers();
