const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, ChannelType, version } = require('discord.js');
const os = require('os');
const fs = require('fs');
const path = require('path');
const process = require('process');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('about')
    .setDescription('𝒊 ❘ Information about Davio ✨'),

  async execute(interaction) {
    const botAvatar = interaction.client.user.displayAvatarURL();
    const totalCommands = interaction.client.commands.size;

    // Count subcommands
    let subCommandCount = 0;
    interaction.client.commands.forEach(cmd => {
      if (cmd.data.options) {
        const subCommands = cmd.data.options.filter(opt => opt.type === 1);
        subCommandCount += subCommands.length;
      }
    });

    // Calculate actual uptime from bot's start time
    const uptimeTimestamp = Math.floor(interaction.client.readyTimestamp / 1000);

    // Fixed code lines count as requested
    const botCodeLines = 30032;

    // Collecting guild information
    let totalUsers = 0;
    let totalTextChannels = 0;
    let totalVoiceChannels = 0;

    await Promise.all(interaction.client.guilds.cache.map(async (guild) => {
      totalUsers += guild.memberCount;
      const channels = await guild.channels.fetch();
      channels.forEach(channel => {
        if (channel.type === ChannelType.GuildText) totalTextChannels++;
        if (channel.type === ChannelType.GuildVoice) totalVoiceChannels++;
      });
    }));

    const totalChannels = totalTextChannels + totalVoiceChannels;

    const embed = new EmbedBuilder()
      .setColor('#7289DA')
      .setAuthor({ name: 'Davio ✨', iconURL: botAvatar })
      .setDescription(`**<:Davio_Information:1355991405851443480> About Me**\n\nDavio ✨ is a multifunctional Discord bot offering **Moderation**, **Automod**, **Economy**, **Games**, **Fun**, **Ticket Management**, **Utility commands**, and much more! 🚀\n\n💡 Designed to make your server experience smoother and more enjoyable. ✨`)
      .addFields(
        { name: '> 🤖 Bot Version', value: '```text\nv12.6.4\n```', inline: false },
        { name: '> <:Davio_Developer:1356007636251971855> DEV/Owner', value: `<:Davio_DEV__OWNER__epicDavid__:1356007652743839824> -> [epic.__.david](https://discord.com/users/1182064854006251520)`, inline: true },
        { name: '> <:Davio_nodejs:1355991410813309048> Node.js', value: `\`\`\`text\n${process.version}\n\`\`\``, inline: true },
        { name: '> <:Davio_discordjs:1356007661480574986> Library', value: `\`\`\`text\ndiscord.js ${version}\n\`\`\``, inline: true },
        { name: '> <:Davio_Ticking:1356129000895942717> Uptime', value: `<t:${uptimeTimestamp}:R>`, inline: true },
        { name: '> <:Davio_Discord_Community_Server:1356007656363393235> Servers', value: `\`\`\`text\n${interaction.client.guilds.cache.size}\n\`\`\``, inline: true },
        { name: '> <:Davio_Discord_Members:1356130088013267097> Users', value: `\`\`\`text\n${totalUsers}\n\`\`\``, inline: true },
        { name: '> <:Davio_Discord_Channel:1356007654207783053> Total Channels', value: `\`\`\`text\n${totalChannels}\n\`\`\``, inline: true },
        { name: '> <:Davio_Discord_Text_Channel:1356007657755901972> Text Channels', value: `\`\`\`text\n${totalTextChannels}\n\`\`\``, inline: true },
        { name: '> <:Davio_Discord_Voice_Channel:1356008177639882872> Voice Channels', value: `\`\`\`text\n${totalVoiceChannels}\n\`\`\``, inline: true },
        { name: '> <:Davio_RAM:1356007641041862848> RAM Usage', value: `\`\`\`text\n${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2)} MB\n\`\`\``, inline: true },
        { name: '> 🏓 Latency', value: `\`\`\`text\n${interaction.client.ws.ping} ms\n\`\`\``, inline: true },
        { name: '> <:Davio_Linux:1355991408699248912> OS', value: `\`\`\`text\n${os.type()} ${os.release()}\n\`\`\``, inline: true },
        { name: '> <:Davio_slash_commands:1356007642597949623> Commands', value: `\`\`\`text\n${totalCommands} (${subCommandCount} subcommands)\n\`\`\``, inline: true },
        { name: '> <:Davio_Database:1356007634733629536> Database Connected', value: '```text\nYes\n```', inline: true },
        { name: '> <:Davio_JavaScript:1355991407260860456> Programming Language', value: '```text\nJavaScript\n```', inline: true },
        { name: '> 📊 Total Code Lines', value: `\`\`\`text\n${botCodeLines}\n\`\`\``, inline: true },
        { name: '> 📅 Created At', value: `\`\`\`text\n${interaction.client.user.createdAt.toDateString()}\n\`\`\``, inline: true }
      )
      .setThumbnail(botAvatar)
      .setFooter({ text: 'Thank you for using Davio✨!', iconURL: botAvatar });

    // Create button row with spacing using custom emojis
    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setLabel('   Support Server   ')
          .setEmoji({ name: 'Davio_Information', id: '1355991405851443480' })
          .setURL('https://discord.gg/aENUuAbnPs')
          .setStyle(ButtonStyle.Link),
        new ButtonBuilder()
          .setLabel('   Invite   ')
          .setEmoji({ name: 'Davio_Invite', id: '1356007639271735504' })
          .setURL('https://discord.com/oauth2/authorize?client_id=1296137985082789919')
          .setStyle(ButtonStyle.Link),
        new ButtonBuilder()
          .setLabel('   Vote   ')
          .setEmoji('🗳️')
          .setURL('https://top.gg/bot/1296137985082789919/vote')
          .setStyle(ButtonStyle.Link),
        new ButtonBuilder()
          .setLabel('   Docs   ')
          .setEmoji({ name: 'Davio_Documentation', id: '1356007637635825816' })
          .setURL('https://davio.top/')
          .setStyle(ButtonStyle.Link),
        new ButtonBuilder()
          .setLabel('   Donate   ')
          .setEmoji('💰')
          .setURL('https://soon.top')
          .setStyle(ButtonStyle.Link)
      );

    await interaction.reply({ embeds: [embed], components: [row] });
  }
};