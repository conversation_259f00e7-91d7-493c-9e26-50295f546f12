const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

// Define brand colors for consistency
const COLORS = {
    PRIMARY: 0x5865F2,    // Discord Blurple
    MODERATION: 0xFF5555, // Red for moderation
    ECONOMY: 0x77DD77,    // Green for economy
    FUN: 0xFFD700,        // Gold for fun
    UTILITY: 0x00BFFF     // Blue for utility
};

// Define commands for each category with descriptions and examples
const categories = {
    automod: {
        emoji: '🛡️',
        color: COLORS.MODERATION,
        description: 'Enterprise-Grade Server Protection System! 🔒✨',
        commands: [
            { name: 'filters invites', desc: 'Configure invite link protection' },
            { name: 'filters spam', desc: 'Advanced spam detection & prevention' },
            { name: 'filters links', desc: 'External link filtering system' },
            { name: 'filters caps', desc: 'Excessive caps protection' },
            { name: 'filters mentions', desc: 'Mention spam protection' },
            { name: 'filters scam', desc: 'Scam link detection' },
            { name: 'raid protection', desc: 'Advanced raid protection' },
            { name: 'settings logs', desc: 'Configure automod logging' },
            { name: 'status', desc: 'View protection status & stats' }
        ],
        example: '/automod filters spam timeout limit:5 window:10'
    },
    economy: {
        emoji: '💰',
        color: COLORS.ECONOMY,
        description: '💰 Have fun with economy system for your members. 📈',
        commands: [
            { name: 'bank', desc: 'Check your bank balance' },
            { name: 'beg', desc: 'Beg for money' },
            { name: 'cash', desc: 'Check your cash balance' },
            { name: 'checkbalance', desc: 'Check total balance' },
            { name: 'coinflip', desc: 'Flip a coin to win money' },
            { name: 'daily', desc: 'Claim daily rewards' },
            { name: 'deposit', desc: 'Deposit money to bank' },
            { name: 'give', desc: 'Give money to another user' },
            { name: 'leaderboard', desc: 'Show richest users' },
            { name: 'lootbox', desc: 'Open a lootbox for rewards' },
            { name: 'removemoney', desc: 'Remove money from a user' },
            { name: 'resetmoney', desc: 'Reset a user\'s balance' },
            { name: 'rob', desc: 'Rob another user' },
            { name: 'slots', desc: 'Play slots machine' },
            { name: 'withdraw', desc: 'Withdraw money from bank' },
            { name: 'work', desc: 'Work to earn money' }
        ],
        example: '/economy give @user 100'
    },
    fun: {
        emoji: '🎉',
        color: COLORS.FUN,
        description: '🎉 Make your server active with exciting commands. 🤹',
        commands: [
            { name: '8ball', desc: 'Ask the magic 8ball' },
            { name: 'animal', desc: 'Get a random animal image' },
            { name: 'cat', desc: 'Get a random cat image' },
            { name: 'crushrate', desc: 'Check crush compatibility' },
            { name: 'cry', desc: 'Express sadness with an emoji' },
            { name: 'dailychallenge', desc: 'Get a daily challenge' },
            { name: 'dog', desc: 'Get a random dog image' },
            { name: 'emojify', desc: 'Convert text to emoji' },
            { name: 'fact', desc: 'Get a random fact' },
            { name: 'gay', desc: 'Check gay rate' },
            { name: 'hack', desc: 'Pretend to hack someone' },
            { name: 'joke', desc: 'Get a random joke' },
            { name: 'meme', desc: 'Get a random meme' },
            { name: 'mindread', desc: 'Bot reads your mind' },
            { name: 'pug', desc: 'Get a random pug image' },
            { name: 'randompic', desc: 'Get a random picture' },
            { name: 'randompicker', desc: 'Pick a random option' },
            { name: 'woof', desc: 'Get a random dog sound' }
        ],
        example: '/fun joke'
    },
    giveaway: {
        emoji: '🎁',
        color: 0xFF69B4, // Pink for giveaway
        description: '🎁 Manage giveaways quickly and efficiently. 🎊',
        commands: [
            { name: 'start', desc: 'Start a new giveaway' },
            { name: 'reroll', desc: 'Reroll a giveaway winner' },
            { name: 'end', desc: 'End an active giveaway' }
        ],
        example: '/giveaway start 1d Prize'
    },
    minigames: {
        emoji: '🎮',
        color: 0x9B59B6, // Purple for minigames
        description: '🎮 Play cool minigames with your friends. 🕹️',
        commands: [
            { name: 'connect4', desc: 'Play Connect 4' },
            { name: 'dino', desc: 'Play Chrome Dino game' },
            { name: 'fasttype', desc: 'Test your typing speed' },
            { name: 'findemoji', desc: 'Find the matching emoji' },
            { name: 'flagquiz', desc: 'Guess the country flag' },
            { name: 'flappybird', desc: 'Play Flappy Bird' },
            { name: 'flood', desc: 'Play the Flood game' },
            { name: 'geometry', desc: 'Play Geometry Dash style game' },
            { name: 'guessnumber', desc: 'Guess the number' },
            { name: 'guessthepokemon', desc: 'Guess the Pokemon' },
            { name: 'hangman', desc: 'Play Hangman' },
            { name: 'jetpackjoyride', desc: 'Play Jetpack Joyride' },
            { name: 'rps', desc: 'Rock Paper Scissors' },
            { name: 'snake', desc: 'Play Snake game' },
            { name: 'tictactoe', desc: 'Play Tic Tac Toe' },
            { name: 'wordguesser', desc: 'Guess the word' }
        ],
        example: '/minigames tictactoe @user'
    },
    moderation: {
        emoji: '🛠️',
        color: 0xFF0000, // Red for moderation
        description: '🛠️ MOD tools to keep your server under control. ⛔',
        commands: [
            { name: 'announce', desc: 'Make an announcement' },
            { name: 'ban', desc: 'Ban a user' },
            { name: 'clear', desc: 'Clear messages' },
            { name: 'kick', desc: 'Kick a user' },
            { name: 'lock', desc: 'Lock a channel' },
            { name: 'membercount', desc: 'Show server member count' },
            { name: 'modpanel', desc: 'Open moderation panel' },
            { name: 'mute', desc: 'Mute a user' },
            { name: 'nick', desc: 'Change a user\'s nickname' },
            { name: 'purge', desc: 'Delete multiple messages' },
            { name: 'report', desc: 'Report a user' },
            { name: 'role', desc: 'Manage user roles' },
            { name: 'say', desc: 'Make the bot say something' },
            { name: 'serverinfo', desc: 'Display server information' },
            { name: 'setreportchannel', desc: 'Set report channel' },
            { name: 'slowmode', desc: 'Set channel slowmode' },
            { name: 'snipe', desc: 'See deleted messages' },
            { name: 'timeout', desc: 'Timeout a user' },
            { name: 'unban', desc: 'Unban a user' },
            { name: 'unlock', desc: 'Unlock a channel' },
            { name: 'unmute', desc: 'Unmute a user' },
            { name: 'untimeout', desc: 'Remove timeout from a user' },
            { name: 'userinfo', desc: 'Display user information' },
            { name: 'warn', desc: 'Warn a user' },
            { name: 'warnings', desc: 'Check user warnings' }
        ],
        example: '/moderation ban @user'
    },
    suggestion: {
        emoji: '💡',
        color: 0xFFAC1C, // Orange for suggestions
        description: '💡 Share members feedback and ideas seamlessly. 📬',
        commands: [
            { name: 'suggest', desc: 'Submit a suggestion' },
            { name: 'suggestion', desc: 'Manage suggestions' }
        ],
        example: '/suggestion suggest Your idea'
    },
    ticket: {
        emoji: '🎫',
        color: 0x00CED1, // Teal for tickets
        description: '🎫 Simplify support with an easy ticketing system. 🛒',
        commands: [
            { name: 'setup', desc: 'Set up ticket system' },
            { name: 'add', desc: 'Add user to ticket' },
            { name: 'close', desc: 'Close a ticket' },
            { name: 'transcript', desc: 'Get ticket transcript' },
            { name: 'remove', desc: 'Remove user from ticket' }
        ],
        example: '/ticket setup'
    },
    tools: {
        emoji: '⚙️',
        color: 0x808080, // Gray for tools
        description: '⚙️ Useful tools for your server management. 🔥',
        commands: [
            { name: 'embed', desc: 'Create a custom embed' },
            { name: 'poll', desc: 'Create a poll' },
            { name: 'timer', desc: 'Set a timer' },
            { name: 'logo', desc: 'Generate a logo' }
        ],
        example: '/tools poll'
    },
    ai: {
        emoji: '🤖',
        color: '#9b59b6', // Purple for AI
        description: '🤖 Advanced AI & ChatGPT Integration System! 🧠✨',
        commands: [
            { name: 'chatgpt setup channel', desc: 'Setup AI chat channel' },
            { name: 'chatgpt setup apikey', desc: 'Configure OpenAI API key' },
            { name: 'chatgpt setup model', desc: 'Set default AI model' },
            { name: 'chatgpt setup personality', desc: 'Configure AI personality' },
            { name: 'chatgpt setup limits', desc: 'Set usage limits' },
            { name: 'chatgpt chat ask', desc: 'Ask AI a question' },
            { name: 'chatgpt chat conversation', desc: 'Start conversation thread' },
            { name: 'chatgpt chat continue', desc: 'Continue conversation' },
            { name: 'chatgpt chat reset', desc: 'Reset conversation history' },
            { name: 'chatgpt manage status', desc: 'View AI system status' },
            { name: 'chatgpt manage usage', desc: 'View usage statistics' },
            { name: 'chatgpt manage history', desc: 'View conversation history' },
            { name: 'chatgpt manage clear', desc: 'Clear conversation data' },
            { name: 'chatgpt manage export', desc: 'Export conversation data' }
        ],
        example: '/chatgpt chat ask'
    },
    utility: {
        emoji: '⚡',
        color: COLORS.UTILITY,
        description: '⚡ Provides quick and useful functions. ⚡',
        commands: [
            { name: 'about', desc: 'About the bot' },
            { name: 'avatar', desc: 'Show user avatar' },
            { name: 'botinfo', desc: 'Bot information' },
            { name: 'enlarge', desc: 'Enlarge an emoji' },
            { name: 'feedback', desc: 'Send feedback' },
            { name: 'guild-list', desc: 'List of servers' },
            { name: 'help', desc: 'Show help menu' },
            { name: 'nuke', desc: 'Nuke a channel' },
            { name: 'ping', desc: 'Check bot latency' },
            { name: 'server', desc: 'Server information' },
            { name: 'stats', desc: 'Bot statistics' },
            { name: 'support', desc: 'Get support' },
            { name: 'supportinfo', desc: 'Support information' },
            { name: 'translate', desc: 'Translate text' },
            { name: 'uptime', desc: 'Check bot uptime' }
        ],
        example: '/utility ping'
    }
};

// Pre-create components to avoid memory leaks
const CACHED_COMPONENTS = {
    categorySelect: null,
    homeRow: null,
    categoryRow: null,
    linkRow: null
};

// Helper function to capitalize first letter
function capitalizeFirst(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

// Generate all components once and cache them
function initComponents() {
    if (CACHED_COMPONENTS.categorySelect) return; // Already initialized
    
    // Category dropdown menu
    CACHED_COMPONENTS.categorySelect = new ActionRowBuilder().addComponents(
        new StringSelectMenuBuilder()
            .setCustomId('select-category')
            .setPlaceholder('Select category')
            .addOptions(
                Object.entries(categories).map(([key, cat]) => ({
                    label: capitalizeFirst(key),
                    value: key,
                    emoji: cat.emoji,
                    description: cat.description.substring(0, 50) // Shorter for mobile
                }))
            )
    );
    
    // Home row buttons
    CACHED_COMPONENTS.homeRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('home')
            .setLabel('Home')
            .setEmoji('🏠')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('faq')
            .setLabel('FAQ')
            .setEmoji('❓')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setLabel('Support')
            .setURL('https://discord.gg/aENUuAbnPs')
            .setEmoji('ℹ️')
            .setStyle(ButtonStyle.Link),
        new ButtonBuilder()
            .setLabel('Invite')
            .setURL('https://discord.com/oauth2/authorize?client_id=1296137985082789919&permissions=8&scope=bot%20applications.commands')
            .setEmoji('📨')
            .setStyle(ButtonStyle.Link)
    );
    
    // Category row buttons (with back button)
    CACHED_COMPONENTS.categoryRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('back')
            .setLabel('Back')
            .setEmoji('⬅️')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('faq')
            .setLabel('FAQ')
            .setEmoji('❓')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setLabel('Support')
            .setURL('https://discord.gg/aENUuAbnPs')
            .setEmoji('ℹ️')
            .setStyle(ButtonStyle.Link),
        new ButtonBuilder()
            .setLabel('Invite')
            .setURL('https://discord.com/oauth2/authorize?client_id=1296137985082789919&permissions=8&scope=bot%20applications.commands')
            .setEmoji('📨')
            .setStyle(ButtonStyle.Link)
    );
    
    // Documentation link row
    CACHED_COMPONENTS.linkRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setLabel('Documentation')
            .setURL('https://davio.astro-hosting.net')
            .setEmoji('📚')
            .setStyle(ButtonStyle.Link)
    );
}

// Helper function to get UI components for specific modes
function getComponents(mode = 'home', categoryKey = null) {
    // Make sure components are initialized
    initComponents();
    
    const components = [
        CACHED_COMPONENTS.categorySelect
    ];
    
    if (mode === 'home') {
        // Clone the row to avoid modifying the cached version
        const homeRow = ActionRowBuilder.from(CACHED_COMPONENTS.homeRow.toJSON());
        components.push(homeRow);
    } else if (mode === 'category') {
        // Clone the row to avoid modifying the cached version
        const categoryRow = ActionRowBuilder.from(CACHED_COMPONENTS.categoryRow.toJSON());
        
        // Set active category indicator in dropdown
        const selectMenu = components[0].components[0];
        selectMenu.options.forEach(option => {
            if (option.data.value === categoryKey) {
                option.data.default = true;  // Visual indicator for active category
            } else {
                option.data.default = false; // Make sure other options are not selected
            }
        });
        
        components.push(categoryRow);
    }
    
    components.push(CACHED_COMPONENTS.linkRow);
    return components;
}

// Function to generate the home embed
function generateHomeEmbed(botIcon) {
    return new EmbedBuilder()
        .setColor(COLORS.PRIMARY)
        .setTitle('__*Davio ✨ Help Menu 📖*__')
        .setDescription('It is a multipurpose bot for your Discord Server. It can help you to build a good server. ☻')
        .setThumbnail(botIcon)
        .addFields(
            { 
                name: '📌 __BOT INFO__', 
                value: `> :arrow_right: Prefix: \`d!\`\n> :arrow_right: Discord.js Version: \`${require('discord.js').version}\`\n> :arrow_right: Running on Node: \`${process.versions.node}\`` 
            },
            { 
                name: '📋 __Available Categories__', 
                value: Object.entries(categories)
                    .map(([key, cat]) => `${cat.emoji} ${capitalizeFirst(key)}`)
                    .join('\n')
            }
        )
        .setFooter({ text: 'Please select a category from the dropdown menu below. 🔽' });
}

// Function to generate category embed
function generateCategoryEmbed(category) {
    const categoryData = categories[category];
    if (!categoryData) return null;

    const embed = new EmbedBuilder()
        .setColor(categoryData.color || COLORS.PRIMARY)
        .setTitle(`${categoryData.emoji} ${capitalizeFirst(category)}`)
        .setDescription(categoryData.description);

    // Format commands in a cleaner way with descriptions
    const commands = categoryData.commands.map(cmd => {
        if (typeof cmd === 'string') {
            return `\`${cmd}\``;
        } else {
            return `\`${cmd.name}\` - ${cmd.desc}`;
        }
    });

    // Split commands into smaller groups if needed
    const COMMANDS_PER_FIELD = 10;
    for (let i = 0; i < commands.length; i += COMMANDS_PER_FIELD) {
        const fieldCommands = commands.slice(i, i + COMMANDS_PER_FIELD);
        embed.addFields({ 
            name: i === 0 ? 'Commands' : '\u200B', 
            value: fieldCommands.join('\n') 
        });
    }

    embed.addFields({ 
        name: 'Example', 
        value: `${categoryData.example}` 
    });

    return embed;
}

// Function to generate the FAQ embed
function generateFaqEmbed() {
    return new EmbedBuilder()
        .setColor(COLORS.PRIMARY)
        .setTitle('Frequently Asked Questions (FAQ)')
        .setDescription(
            `
1. **What are the main features of this bot?**  
💼 The bot has economy features, moderation tools, automated actions, role management, utility commands, and more. 📝

2. **How can YOU add the bot to your server?**  
🔗 You can invite the bot using an invite link. Click the link in /help. 🔥

3. **Does the bot support multiple languages?**  
🌍 Currently, the bot supports English. Future updates may include more language support. 😃

4. **Can I submit feedback or report issues?**  
📝 Yes, you can report issues or provide feedback by joining the [support server](https://discord.gg/aENUuAbnPs). 🆘

5. **Why is the bot not responding to my commands?**  
⚙️ Ensure the bot is online and has the correct permissions. Check if you're using the correct command prefix (d!).
Only slash commands soon more! 🔜

6. **Can the bot be used in private messages?**  
📩 Only economy commands and commands not specific to the server work in private messages. 💰

7. **Is the bot safe?**  
🔒 Yes, it has robust safety protections! 🛡️

8. **Where can I find full documentation for the bot?**  
📚 Documentation will soon be available at 

💡 → https://davio.astro-hosting.net/ ← 💡

9. **What does the ERROR CODE » 💀| You met a rare bot error « mean?**  
It is either because you do not have the necessary permissions to execute the command or it is a bot error!
⚠️ If an error occurs, report it to the support server! [CLICK HERE](https://discord.gg/aENUuAbnPs)
            `
        );
}

// Function to generate the timeout embed
function generateTimeoutEmbed() {
    return new EmbedBuilder()
        .setColor(COLORS.PRIMARY)
        .setTitle('Help Menu Closed')
        .setDescription('⏱️ This help menu has timed out after 5 minutes of inactivity.')
        .addFields(
            { 
                name: 'Need more help?', 
                value: 'Use `/help` command again to open a new help menu or join our [support server](https://discord.gg/aENUuAbnPs) for assistance.' 
            }
        )
        .setFooter({ text: 'Thank you for using Davio! ✨' });
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('⚡|Displays the help menu ❓ for Davio ✨. 📖')
        .addStringOption(option =>
            option.setName('category')
                .setDescription('Select a category to get help for')
                .setRequired(false)
                .addChoices(
                    ...Object.keys(categories).map(cat => ({
                        name: capitalizeFirst(cat),
                        value: cat
                    }))
                )),

    async execute(interaction) {
        // Defer the reply to have more time to process
        await interaction.deferReply({ ephemeral: false });
        
        try {
            const botIcon = interaction.client.user.displayAvatarURL();
            const selectedCategory = interaction.options.getString('category');

            // Initialize components on first use
            initComponents();

            if (selectedCategory) {
                const categoryEmbed = generateCategoryEmbed(selectedCategory);
                if (categoryEmbed) {
                    await interaction.editReply({ 
                        embeds: [categoryEmbed],
                        components: getComponents('category', selectedCategory)
                    });
                } else {
                    await interaction.editReply({ 
                        content: 'Invalid category selected. Please try again with a valid category.',
                        ephemeral: true 
                    });
                    return;
                }
            } else {
                // Send the help menu
                await interaction.editReply({ 
                    embeds: [generateHomeEmbed(botIcon)],
                    components: getComponents('home')
                });
            }

            // Create collector with a timeout
            const message = await interaction.fetchReply();
            const collector = message.createMessageComponentCollector({ 
                filter: i => i.user.id === interaction.user.id,
                time: 300000 // 5-minute timeout
            });

            collector.on('collect', async i => {
                try {
                    if (i.customId === 'home' || i.customId === 'back') {
                        await i.deferUpdate();
                        await i.editReply({ 
                            embeds: [generateHomeEmbed(botIcon)],
                            components: getComponents('home')
                        });
                    } else if (i.customId === 'faq') {
                        // Send FAQ as a separate message
                        await i.deferUpdate();
                        await i.followUp({ 
                            embeds: [generateFaqEmbed()], 
                            ephemeral: true 
                        });
                    } else if (i.customId === 'select-category') {
                        const categoryEmbed = generateCategoryEmbed(i.values[0]);
                        if (categoryEmbed) {
                            await i.deferUpdate();
                            await i.editReply({ 
                                embeds: [categoryEmbed],
                                components: getComponents('category', i.values[0])
                            });
                        }
                    }
                    
                    // Reset collector timer on each interaction
                    collector.resetTimer();
                } catch (error) {
                    console.error('Error handling interaction:', error);
                    await i.followUp({ 
                        content: 'There was an error processing your request. Please try again.',
                        ephemeral: true 
                    }).catch(() => {});
                }
            });

            // Handle collector end
            collector.on('end', async (collected, reason) => {
                try {
                    // Display a timeout message and remove interactive components
                    if (reason === 'time') {
                        await message.edit({ 
                            embeds: [generateTimeoutEmbed()],
                            components: [] 
                        }).catch(() => {}); // Ignore errors if message is deleted
                    } else {
                        // For other reasons just remove the components
                        await message.edit({ 
                            components: [] 
                        }).catch(() => {});
                    }
                } catch (error) {
                    console.error('Error updating help message after timeout:', error);
                }
            });
        } catch (error) {
            console.error('Error executing help command:', error);
            await interaction.editReply({ 
                content: 'There was an error processing your request. Please try again or contact support if the issue persists.',
                ephemeral: true 
            }).catch(() => {});
        }
    }
};