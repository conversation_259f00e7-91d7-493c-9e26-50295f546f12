const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonStyle } = require('discord.js');

// Professional color palette for Davio ✨
const DAVIO_COLORS = {
    PRIMARY: 0x7289DA,    // Discord Blurple
    SUCCESS: 0x43B581,    // Green
    INFO: 0x00D4FF,       // Cyan
    PREMIUM: 0xFFD700     // Gold
};

// Get a beautiful gradient color
function getDavioColor() {
    const colors = [DAVIO_COLORS.PRIMARY, DAVIO_COLORS.SUCCESS, DAVIO_COLORS.INFO, DAVIO_COLORS.PREMIUM];
    return colors[Math.floor(Math.random() * colors.length)];
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('supportinfo')
        .setDescription('Displays comprehensive information about Davio ✨ bot and support'),
    
    cooldown: 5000, // 5 seconds cooldown
    category: 'bot',

    async execute(interaction) {
        try {
            // Create main welcome embed
            const welcomeEmbed = new EmbedBuilder()
                .setColor(getDavioColor())
                .setTitle('🌟 Welcome to Davio ✨ Support!')
                .setDescription(`
**Davio ✨** is your comprehensive Discord bot solution, designed to transform your server into an engaging, well-moderated, and fun community space.

🎯 **Our Mission**: Provide you with the most reliable, feature-rich, and user-friendly bot experience possible.

🚀 **What makes us special**: Advanced automation, intuitive commands, and a passionate community dedicated to helping you succeed.
                `.trim())
                .setThumbnail(interaction.client.user.displayAvatarURL({ size: 256 }))
                .setTimestamp()
                .setFooter({ 
                    text: '✨ Thank you for choosing Davio! Together, we\'ll make your server extraordinary.',
                    iconURL: interaction.client.user.displayAvatarURL()
                });

            // Create features embed
            const featuresEmbed = new EmbedBuilder()
                .setColor(getDavioColor())
                .setTitle('🔥 What Davio ✨ Offers')
                .addFields(
                    {
                        name: '🛡️ Advanced Automod',
                        value: 'Intelligent message filtering, spam protection, and customizable moderation rules to keep your server safe.',
                        inline: true
                    },
                    {
                        name: '💰 Economy System',
                        value: 'Complete virtual economy with coins, daily rewards, work commands, and interactive trading between members.',
                        inline: true
                    },
                    {
                        name: '🎉 Fun & Games',
                        value: 'Memes, jokes, mini-games like Snake, Trivia, and entertainment to keep your community active and engaged.',
                        inline: true
                    },
                    {
                        name: '🔨 Moderation Tools',
                        value: 'Complete moderation suite with ban, kick, timeout, warnings, and detailed logging capabilities.',
                        inline: true
                    },
                    {
                        name: '🎫 Support Tickets',
                        value: 'Professional ticket system with private channels, role permissions, and transcript generation.',
                        inline: true
                    },
                    {
                        name: '⚙️ Utility Commands',
                        value: 'Avatar display, server info, polls, timers, and other essential server management tools.',
                        inline: true
                    }
                );

            // Create support embed
            const supportEmbed = new EmbedBuilder()
                .setColor(getDavioColor())
                .setTitle('💬 Community & Support')
                .setDescription(`
**🎯 Why You're Here:**
**1️⃣ Expert Support** - Our dedicated team and community experts are ready to help
**2️⃣ Stay Updated** - Get the latest features, updates, and exclusive announcements  
**3️⃣ Community** - Connect with other server owners and share best practices
**4️⃣ Feedback** - Your suggestions shape Davio's future development

**⚡ How to Use Commands:**
Use **/** followed by the command (e.g., \`/bot help\` or \`/economy daily\`) for the best bot interaction experience.

**📊 Join our support server to access all features and get help!**
                `.trim());

            // Create professional buttons
            const buttons = [
                new ButtonBuilder()
                    .setLabel('🎮 Play with Davio')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.com/channels/1259089720017944649/1297125768333361152'),
                
                new ButtonBuilder()
                    .setLabel('💬 Community Chat')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.com/channels/1259089720017944649/1283091192191389719'),
                
                new ButtonBuilder()
                    .setLabel('🆘 Get Help')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.com/channels/1259089720017944649/1303491745190907987'),
                
                new ButtonBuilder()
                    .setLabel('📰 Latest News')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.com/channels/1259089720017944649/1320757002003288064'),
                
                new ButtonBuilder()
                    .setLabel('➕ Add to Server')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.com/channels/1259089720017944649/1308762414954577970')
            ];

            // Create action rows (max 5 buttons per row)
            const buttonRow = new ActionRowBuilder().addComponents(buttons);

            // Create join server button
            const joinButton = new ButtonBuilder()
                .setLabel('🌟 Join Support Server')
                .setStyle(ButtonStyle.Link)
                .setURL('https://discord.gg/aENUuAbnPs');

            const joinRow = new ActionRowBuilder().addComponents(joinButton);

            // Send the embeds with buttons
            await interaction.reply({
                embeds: [welcomeEmbed, featuresEmbed, supportEmbed],
                components: [buttonRow, joinRow],
                ephemeral: false
            });

        } catch (error) {
            console.error('Error in supportinfo command:', error);
            
            // Fallback response
            const errorEmbed = new EmbedBuilder()
                .setColor(0xF04747)
                .setTitle('❌ Error')
                .setDescription('An error occurred while displaying support information. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
