// 🏗️ MINECRAFT-INSPIRED ECONOMY SYSTEM - ENTERPRISE COMMAND HUB
// Complete economy system with currencies, items, jobs, businesses, and more

const { Slash<PERSON><PERSON>mandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Economy = require('../database/models/economy');

// 🎨 DESIGN CONSTANTS
const COLORS = {
  SUCCESS: '#2ecc71',
  ERROR: '#e74c3c',
  WARNING: '#f39c12',
  INFO: '#3498db',
  PREMIUM: '#9b59b6',
  GOLD: '#f1c40f'
};

const EMOJIS = {
  COIN: '🪙',
  GEM: '💎',
  TOKEN: '🎫',
  BANK: '🏦',
  SHOP: '🛒',
  INVENTORY: '🎒',
  WORK: '⚒️',
  BUSINESS: '🏢',
  PET: '🐾',
  QUEST: '📜',
  LOTTERY: '🎰',
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('economy')
    .setDescription('🏗️ Complete Minecraft-inspired Economy System')
    
    // 💰 CURRENCY GROUP
    .addSubcommandGroup(group =>
      group
        .setName('currency')
        .setDescription('💰 Manage your currencies and balances')
        .addSubcommand(subcommand =>
          subcommand
            .setName('balance')
            .setDescription('💰 Check your current balance'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('daily')
            .setDescription('🎁 Collect your daily reward'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('transfer')
            .setDescription('💸 Send money to another user')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('👤 User to send money to')
                .setRequired(true))
            .addIntegerOption(option =>
              option.setName('amount')
                .setDescription('💰 Amount to send')
                .setRequired(true)
                .setMinValue(1)))
        .addSubcommand(subcommand =>
          subcommand
            .setName('leaderboard')
            .setDescription('🏆 View the richest users')))
    
    // 🛒 SHOP GROUP
    .addSubcommandGroup(group =>
      group
        .setName('shop')
        .setDescription('🛒 Buy and sell items')
        .addSubcommand(subcommand =>
          subcommand
            .setName('view')
            .setDescription('🛒 View the shop'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('buy')
            .setDescription('💰 Buy an item from the shop')
            .addStringOption(option =>
              option.setName('item')
                .setDescription('🎁 Item to buy')
                .setRequired(true)
                .addChoices(
                  { name: '🪵 Wood (10 coins)', value: 'wood' },
                  { name: '🪨 Stone (15 coins)', value: 'stone' },
                  { name: '⚔️ Iron Sword (100 coins)', value: 'ironSword' },
                  { name: '🍞 Bread (5 coins)', value: 'bread' },
                  { name: '🎁 Lootbox (50 coins)', value: 'lootbox' }
                ))
            .addIntegerOption(option =>
              option.setName('quantity')
                .setDescription('📦 Quantity to buy')
                .setMinValue(1)
                .setMaxValue(100)))
        .addSubcommand(subcommand =>
          subcommand
            .setName('sell')
            .setDescription('💰 Sell items from your inventory')
            .addStringOption(option =>
              option.setName('item')
                .setDescription('🎁 Item to sell')
                .setRequired(true))
            .addIntegerOption(option =>
              option.setName('quantity')
                .setDescription('📦 Quantity to sell')
                .setMinValue(1))))
    
    // 🎒 INVENTORY GROUP
    .addSubcommandGroup(group =>
      group
        .setName('inventory')
        .setDescription('🎒 Manage your items and inventory')
        .addSubcommand(subcommand =>
          subcommand
            .setName('view')
            .setDescription('🎒 View your inventory'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('craft')
            .setDescription('🔨 Craft items from materials')
            .addStringOption(option =>
              option.setName('item')
                .setDescription('🎁 Item to craft')
                .setRequired(true)
                .addChoices(
                  { name: '⚔️ Wooden Sword (5 Wood)', value: 'woodenSword' },
                  { name: '⚔️ Iron Sword (3 Iron)', value: 'ironSword' },
                  { name: '🍞 Bread (2 Wheat)', value: 'bread' },
                  { name: '🛡️ Iron Helmet (5 Iron)', value: 'helmet' }
                ))))
    
    // ⚒️ WORK GROUP
    .addSubcommandGroup(group =>
      group
        .setName('work')
        .setDescription('⚒️ Work and earn money')
        .addSubcommand(subcommand =>
          subcommand
            .setName('jobs')
            .setDescription('💼 View available jobs'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('apply')
            .setDescription('📝 Apply for a job')
            .addStringOption(option =>
              option.setName('job')
                .setDescription('💼 Job to apply for')
                .setRequired(true)
                .addChoices(
                  { name: '⛏️ Miner', value: 'miner' },
                  { name: '🌾 Farmer', value: 'farmer' },
                  { name: '⚔️ Warrior', value: 'warrior' },
                  { name: '🏪 Merchant', value: 'merchant' }
                )))
        .addSubcommand(subcommand =>
          subcommand
            .setName('start')
            .setDescription('⚒️ Start working at your job')))
    
    // 🎰 GAMBLING GROUP
    .addSubcommandGroup(group =>
      group
        .setName('gambling')
        .setDescription('🎰 Risk your money for bigger rewards')
        .addSubcommand(subcommand =>
          subcommand
            .setName('coinflip')
            .setDescription('🪙 Flip a coin and double your money')
            .addIntegerOption(option =>
              option.setName('bet')
                .setDescription('💰 Amount to bet')
                .setRequired(true)
                .setMinValue(1)))
        .addSubcommand(subcommand =>
          subcommand
            .setName('blackjack')
            .setDescription('🃏 Play blackjack against the house')
            .addIntegerOption(option =>
              option.setName('bet')
                .setDescription('💰 Amount to bet')
                .setRequired(true)
                .setMinValue(1)))
        .addSubcommand(subcommand =>
          subcommand
            .setName('lottery')
            .setDescription('🎫 Buy lottery tickets')
            .addIntegerOption(option =>
              option.setName('tickets')
                .setDescription('🎫 Number of tickets to buy')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(10))))
    
    // 🏢 BUSINESS GROUP
    .addSubcommandGroup(group =>
      group
        .setName('business')
        .setDescription('🏢 Start and manage your business')
        .addSubcommand(subcommand =>
          subcommand
            .setName('start')
            .setDescription('🏗️ Start a new business')
            .addStringOption(option =>
              option.setName('type')
                .setDescription('🏢 Type of business')
                .setRequired(true)
                .addChoices(
                  { name: '🏪 Shop (500 coins)', value: 'shop' },
                  { name: '🌾 Farm (300 coins)', value: 'farm' },
                  { name: '⛏️ Mine (800 coins)', value: 'mine' },
                  { name: '🏭 Factory (1000 coins)', value: 'factory' }
                )))
        .addSubcommand(subcommand =>
          subcommand
            .setName('manage')
            .setDescription('📊 Manage your business'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('collect')
            .setDescription('💰 Collect business income')))
    
    // 📊 STATS GROUP
    .addSubcommandGroup(group =>
      group
        .setName('stats')
        .setDescription('📊 View your statistics and achievements')
        .addSubcommand(subcommand =>
          subcommand
            .setName('profile')
            .setDescription('👤 View your complete profile'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('achievements')
            .setDescription('🏆 View your achievements'))
        .addSubcommand(subcommand =>
          subcommand
            .setName('cooldowns')
            .setDescription('⏰ Check your command cooldowns'))),

  async execute(interaction) {
    const group = interaction.options.getSubcommandGroup();
    const subcommand = interaction.options.getSubcommand();
    
    // Route to appropriate handler
    switch (group) {
      case 'currency':
        return await handleCurrency(interaction, subcommand);
      case 'shop':
        return await handleShop(interaction, subcommand);
      case 'inventory':
        return await handleInventory(interaction, subcommand);
      case 'work':
        return await handleWork(interaction, subcommand);
      case 'gambling':
        return await handleGambling(interaction, subcommand);
      case 'business':
        return await handleBusiness(interaction, subcommand);
      case 'stats':
        return await handleStats(interaction, subcommand);
    }
  }
};

// 💰 CURRENCY HANDLERS
async function handleCurrency(interaction, subcommand) {
  switch (subcommand) {
    case 'balance':
      return await showBalance(interaction);
    case 'daily':
      return await claimDaily(interaction);
    case 'transfer':
      return await transferMoney(interaction);
    case 'leaderboard':
      return await showLeaderboard(interaction);
  }
}

// 🛒 SHOP HANDLERS
async function handleShop(interaction, subcommand) {
  switch (subcommand) {
    case 'view':
      return await showShop(interaction);
    case 'buy':
      return await buyItem(interaction);
    case 'sell':
      return await sellItem(interaction);
  }
}

// 🎒 INVENTORY HANDLERS
async function handleInventory(interaction, subcommand) {
  switch (subcommand) {
    case 'view':
      return await showInventory(interaction);
    case 'craft':
      return await craftItem(interaction);
  }
}

// ⚒️ WORK HANDLERS
async function handleWork(interaction, subcommand) {
  switch (subcommand) {
    case 'jobs':
      return await showJobs(interaction);
    case 'apply':
      return await applyJob(interaction);
    case 'start':
      return await startWork(interaction);
  }
}

// 🎰 GAMBLING HANDLERS
async function handleGambling(interaction, subcommand) {
  switch (subcommand) {
    case 'coinflip':
      return await playCoinflip(interaction);
    case 'blackjack':
      return await playBlackjack(interaction);
    case 'lottery':
      return await buyLottery(interaction);
  }
}

// 🏢 BUSINESS HANDLERS
async function handleBusiness(interaction, subcommand) {
  switch (subcommand) {
    case 'start':
      return await startBusiness(interaction);
    case 'manage':
      return await manageBusiness(interaction);
    case 'collect':
      return await collectIncome(interaction);
  }
}

// 📊 STATS HANDLERS
async function handleStats(interaction, subcommand) {
  switch (subcommand) {
    case 'profile':
      return await showProfile(interaction);
    case 'achievements':
      return await showAchievements(interaction);
    case 'cooldowns':
      return await showCooldowns(interaction);
  }
}

// 🛠️ UTILITY FUNCTIONS
async function getOrCreateUser(userId, guildId) {
  let user = await Economy.findOne({ userId, guildId });
  if (!user) {
    user = new Economy({ userId, guildId });
    await user.save();
  }
  return user;
}

function formatNumber(num) {
  return num.toLocaleString();
}

function createErrorEmbed(message) {
  return new EmbedBuilder()
    .setColor(COLORS.ERROR)
    .setTitle(`${EMOJIS.ERROR} Error`)
    .setDescription(message)
    .setTimestamp();
}

// 💰 BALANCE COMMAND
async function showBalance(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  const totalWealth = user.coins + user.gems * 10 + user.tokens * 5 + user.bank;

  const embed = new EmbedBuilder()
    .setColor(COLORS.GOLD)
    .setTitle(`${EMOJIS.COIN} ${interaction.user.username}'s Wallet`)
    .setDescription('**💰 Your Minecraft-inspired Economy Status**')
    .addFields(
      {
        name: '🪙 **Primary Currencies**',
        value: [
          `${EMOJIS.COIN} **Coins:** ${formatNumber(user.coins)}`,
          `💎 **Gems:** ${formatNumber(user.gems)}`,
          `🎫 **Tokens:** ${formatNumber(user.tokens)}`,
          `🏦 **Bank:** ${formatNumber(user.bank)}`
        ].join('\n'),
        inline: true
      },
      {
        name: '📊 **Wealth Summary**',
        value: [
          `💰 **Total Worth:** ${formatNumber(totalWealth)}`,
          `📈 **Level:** ${user.level}`,
          `⭐ **Prestige:** ${user.prestige}`,
          `🎯 **XP:** ${formatNumber(user.xp)}`
        ].join('\n'),
        inline: true
      },
      {
        name: '💼 **Career Info**',
        value: [
          `⚒️ **Job:** ${user.job.current || 'Unemployed'}`,
          `🏢 **Business:** ${user.business.type || 'None'}`,
          `🐾 **Pets:** ${user.pets.length}`,
          `🏆 **Achievements:** ${user.achievements.length}`
        ].join('\n'),
        inline: false
      }
    )
    .setThumbnail(interaction.user.displayAvatarURL())
    .setFooter({ text: 'Minecraft-inspired Economy System' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🎁 DAILY REWARD COMMAND
async function claimDaily(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  const now = new Date();
  const lastDaily = user.lastDaily;

  // Check if user can claim daily (24 hours)
  if (lastDaily && (now - lastDaily) < 24 * 60 * 60 * 1000) {
    const timeLeft = 24 * 60 * 60 * 1000 - (now - lastDaily);
    const hours = Math.floor(timeLeft / (60 * 60 * 1000));
    const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));

    const embed = createErrorEmbed(`You can claim your daily reward in **${hours}h ${minutes}m**!`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Calculate rewards based on level and streak
  const baseCoins = 50;
  const levelBonus = user.level * 5;
  const prestigeBonus = user.prestige * 25;
  const totalCoins = baseCoins + levelBonus + prestigeBonus;

  // Bonus items
  const bonusGems = Math.random() < 0.1 ? Math.floor(Math.random() * 3) + 1 : 0;
  const bonusTokens = Math.random() < 0.05 ? 1 : 0;

  // Update user
  user.coins += totalCoins;
  user.gems += bonusGems;
  user.tokens += bonusTokens;
  user.lastDaily = now;
  user.stats.totalEarned += totalCoins;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Daily Reward Claimed!`)
    .setDescription('**🎁 Your daily treasures have arrived!**')
    .addFields(
      {
        name: '💰 **Rewards Earned**',
        value: [
          `🪙 **+${formatNumber(totalCoins)} Coins**`,
          bonusGems > 0 ? `💎 **+${bonusGems} Gems** (Lucky!)` : '',
          bonusTokens > 0 ? `🎫 **+${bonusTokens} Token** (Rare!)` : ''
        ].filter(Boolean).join('\n'),
        inline: true
      },
      {
        name: '📊 **Bonus Breakdown**',
        value: [
          `🎯 **Base:** ${baseCoins} coins`,
          `📈 **Level Bonus:** +${levelBonus} coins`,
          `⭐ **Prestige Bonus:** +${prestigeBonus} coins`
        ].join('\n'),
        inline: true
      }
    )
    .setFooter({ text: 'Come back tomorrow for more rewards!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 💸 TRANSFER MONEY COMMAND
async function transferMoney(interaction) {
  const targetUser = interaction.options.getUser('user');
  const amount = interaction.options.getInteger('amount');

  if (targetUser.id === interaction.user.id) {
    const embed = createErrorEmbed('You cannot transfer money to yourself!');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  if (targetUser.bot) {
    const embed = createErrorEmbed('You cannot transfer money to bots!');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const sender = await getOrCreateUser(interaction.user.id, interaction.guild.id);
  const receiver = await getOrCreateUser(targetUser.id, interaction.guild.id);

  if (sender.coins < amount) {
    const embed = createErrorEmbed(`You don't have enough coins! You have ${formatNumber(sender.coins)} coins.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Transfer with 2% fee
  const fee = Math.floor(amount * 0.02);
  const actualAmount = amount - fee;

  sender.coins -= amount;
  receiver.coins += actualAmount;
  sender.stats.totalSpent += amount;
  receiver.stats.totalEarned += actualAmount;

  await sender.save();
  await receiver.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Transfer Successful!`)
    .setDescription('**💸 Money has been transferred successfully!**')
    .addFields(
      {
        name: '📤 **Sender**',
        value: [
          `👤 **${interaction.user.username}**`,
          `💰 **Sent:** ${formatNumber(amount)} coins`,
          `💳 **Fee:** ${formatNumber(fee)} coins`,
          `🪙 **Remaining:** ${formatNumber(sender.coins)} coins`
        ].join('\n'),
        inline: true
      },
      {
        name: '📥 **Receiver**',
        value: [
          `👤 **${targetUser.username}**`,
          `💰 **Received:** ${formatNumber(actualAmount)} coins`,
          `🪙 **New Balance:** ${formatNumber(receiver.coins)} coins`
        ].join('\n'),
        inline: true
      }
    )
    .setFooter({ text: 'Transfer completed with 2% processing fee' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🏆 LEADERBOARD COMMAND
async function showLeaderboard(interaction) {
  const users = await Economy.find({ guildId: interaction.guild.id })
    .sort({ coins: -1 })
    .limit(10);

  if (users.length === 0) {
    const embed = createErrorEmbed('No users found in the economy system!');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const leaderboardText = await Promise.all(
    users.map(async (user, index) => {
      try {
        const discordUser = await interaction.client.users.fetch(user.userId);
        const totalWealth = user.coins + user.gems * 10 + user.tokens * 5 + user.bank;
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
        return `${medal} **${discordUser.username}** - ${formatNumber(totalWealth)} total wealth`;
      } catch {
        return `${index + 1}. **Unknown User** - ${formatNumber(user.coins)} coins`;
      }
    })
  );

  const embed = new EmbedBuilder()
    .setColor(COLORS.GOLD)
    .setTitle(`🏆 ${interaction.guild.name} - Wealth Leaderboard`)
    .setDescription('**💰 Top 10 Richest Members**\n\n' + leaderboardText.join('\n'))
    .setFooter({ text: 'Based on total wealth (coins + gems + tokens + bank)' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🛒 SHOP VIEW COMMAND
async function showShop(interaction) {
  const shopItems = [
    { name: '🪵 Wood', price: 10, description: 'Basic building material' },
    { name: '🪨 Stone', price: 15, description: 'Sturdy construction material' },
    { name: '⚔️ Iron Sword', price: 100, description: 'Sharp weapon for combat' },
    { name: '🍞 Bread', price: 5, description: 'Restores health and hunger' },
    { name: '🎁 Lootbox', price: 50, description: 'Contains random valuable items' },
    { name: '💎 Gem', price: 200, description: 'Premium currency' },
    { name: '🎫 Token', price: 100, description: 'Special event currency' },
    { name: '🛡️ Iron Helmet', price: 75, description: 'Protective headgear' }
  ];

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.SHOP} Minecraft-inspired Shop`)
    .setDescription('**🛒 Welcome to the Village Trading Post!**\n*Use `/economy shop buy` to purchase items*')
    .addFields(
      {
        name: '⚔️ **Weapons & Tools**',
        value: [
          '⚔️ **Iron Sword** - 100 coins',
          '🛡️ **Iron Helmet** - 75 coins'
        ].join('\n'),
        inline: true
      },
      {
        name: '🏗️ **Materials**',
        value: [
          '🪵 **Wood** - 10 coins',
          '🪨 **Stone** - 15 coins'
        ].join('\n'),
        inline: true
      },
      {
        name: '🍽️ **Food & Items**',
        value: [
          '🍞 **Bread** - 5 coins',
          '🎁 **Lootbox** - 50 coins'
        ].join('\n'),
        inline: true
      },
      {
        name: '💎 **Premium Currencies**',
        value: [
          '💎 **Gem** - 200 coins',
          '🎫 **Token** - 100 coins'
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: 'Prices may vary based on server events' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 💰 BUY ITEM COMMAND
async function buyItem(interaction) {
  const itemName = interaction.options.getString('item');
  const quantity = interaction.options.getInteger('quantity') || 1;

  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  const shopPrices = {
    wood: 10,
    stone: 15,
    ironSword: 100,
    bread: 5,
    lootbox: 50
  };

  const itemDisplayNames = {
    wood: '🪵 Wood',
    stone: '🪨 Stone',
    ironSword: '⚔️ Iron Sword',
    bread: '🍞 Bread',
    lootbox: '🎁 Lootbox'
  };

  if (!shopPrices[itemName]) {
    const embed = createErrorEmbed('Invalid item! Use `/economy shop view` to see available items.');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const totalCost = shopPrices[itemName] * quantity;

  if (user.coins < totalCost) {
    const embed = createErrorEmbed(`You need ${formatNumber(totalCost)} coins but only have ${formatNumber(user.coins)} coins!`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Process purchase
  user.coins -= totalCost;
  user.inventory[itemName] += quantity;
  user.stats.totalSpent += totalCost;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Purchase Successful!`)
    .setDescription('**🛒 Items added to your inventory!**')
    .addFields(
      {
        name: '🎁 **Purchase Details**',
        value: [
          `**Item:** ${itemDisplayNames[itemName]}`,
          `**Quantity:** ${quantity}`,
          `**Total Cost:** ${formatNumber(totalCost)} coins`,
          `**Remaining Coins:** ${formatNumber(user.coins)}`
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: 'Thank you for shopping with us!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🎒 INVENTORY VIEW COMMAND
async function showInventory(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  const inventoryItems = [];
  const inventory = user.inventory;

  // Group items by category
  const categories = {
    '🏗️ **Materials**': ['wood', 'stone', 'iron', 'gold', 'diamond'],
    '🍽️ **Food**': ['bread', 'apple', 'meat'],
    '⚔️ **Weapons**': ['woodenSword', 'ironSword', 'diamondSword'],
    '🛡️ **Armor**': ['helmet', 'chestplate', 'leggings', 'boots'],
    '🎁 **Special Items**': ['lootbox', 'key', 'potion']
  };

  const itemNames = {
    wood: '🪵 Wood', stone: '🪨 Stone', iron: '⚙️ Iron', gold: '🟡 Gold', diamond: '💎 Diamond',
    bread: '🍞 Bread', apple: '🍎 Apple', meat: '🥩 Meat',
    woodenSword: '⚔️ Wooden Sword', ironSword: '⚔️ Iron Sword', diamondSword: '⚔️ Diamond Sword',
    helmet: '🛡️ Helmet', chestplate: '🛡️ Chestplate', leggings: '🛡️ Leggings', boots: '🛡️ Boots',
    lootbox: '🎁 Lootbox', key: '🗝️ Key', potion: '🧪 Potion', pickaxe: '⛏️ Pickaxe'
  };

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.INVENTORY} ${interaction.user.username}'s Inventory`)
    .setDescription('**🎒 Your Minecraft-inspired Item Collection**');

  let hasItems = false;

  for (const [categoryName, items] of Object.entries(categories)) {
    const categoryItems = items
      .filter(item => inventory[item] > 0)
      .map(item => `${itemNames[item] || item}: **${inventory[item]}**`);

    if (categoryItems.length > 0) {
      embed.addFields({
        name: categoryName,
        value: categoryItems.join('\n'),
        inline: true
      });
      hasItems = true;
    }
  }

  if (!hasItems) {
    embed.setDescription('**🎒 Your inventory is empty!**\nVisit the shop to buy some items: `/economy shop view`');
  }

  embed.setFooter({ text: 'Use /economy inventory craft to create new items!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🔨 CRAFT ITEM COMMAND
async function craftItem(interaction) {
  const itemName = interaction.options.getString('item');
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  const recipes = {
    woodenSword: { materials: { wood: 5 }, result: 'woodenSword' },
    ironSword: { materials: { iron: 3 }, result: 'ironSword' },
    bread: { materials: { wood: 2 }, result: 'bread' }, // Using wood as wheat substitute
    helmet: { materials: { iron: 5 }, result: 'helmet' }
  };

  const recipe = recipes[itemName];
  if (!recipe) {
    const embed = createErrorEmbed('Invalid recipe! Check available crafting recipes.');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Check if user has required materials
  for (const [material, amount] of Object.entries(recipe.materials)) {
    if (user.inventory[material] < amount) {
      const embed = createErrorEmbed(`You need ${amount} ${material} but only have ${user.inventory[material]}!`);
      return await interaction.reply({ embeds: [embed], ephemeral: true });
    }
  }

  // Consume materials and create item
  for (const [material, amount] of Object.entries(recipe.materials)) {
    user.inventory[material] -= amount;
  }
  user.inventory[recipe.result] += 1;
  user.stats.itemsCrafted += 1;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Crafting Successful!`)
    .setDescription('**🔨 You have successfully crafted a new item!**')
    .addFields({
      name: '🎁 **Crafted Item**',
      value: `You crafted: **${itemName}**`,
      inline: false
    })
    .setFooter({ text: 'Keep crafting to become a master artisan!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 💼 JOBS VIEW COMMAND
async function showJobs(interaction) {
  const jobs = [
    { name: '⛏️ Miner', pay: '15-30 coins', description: 'Mine valuable resources' },
    { name: '🌾 Farmer', pay: '10-25 coins', description: 'Grow crops and raise animals' },
    { name: '⚔️ Warrior', pay: '20-40 coins', description: 'Fight monsters and protect villages' },
    { name: '🏪 Merchant', pay: '12-35 coins', description: 'Trade goods and manage shops' }
  ];

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.WORK} Available Jobs`)
    .setDescription('**💼 Choose your career path in the Minecraft world!**\n*Use `/economy work apply` to get a job*')
    .addFields(
      jobs.map(job => ({
        name: job.name,
        value: `💰 **Pay:** ${job.pay}\n📋 **Description:** ${job.description}`,
        inline: true
      }))
    )
    .setFooter({ text: 'Higher job levels = better pay!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 📝 APPLY JOB COMMAND
async function applyJob(interaction) {
  const jobName = interaction.options.getString('job');
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (user.job.current === jobName) {
    const embed = createErrorEmbed(`You already work as a ${jobName}!`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  user.job.current = jobName;
  user.job.level = 1;
  user.job.experience = 0;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Job Application Accepted!`)
    .setDescription(`**💼 Welcome to your new career as a ${jobName}!**`)
    .addFields({
      name: '🎯 **Job Details**',
      value: [
        `**Position:** ${jobName}`,
        `**Level:** 1`,
        `**Experience:** 0`,
        `**Status:** Ready to work!`
      ].join('\n'),
      inline: false
    })
    .setFooter({ text: 'Use /economy work start to begin working!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// ⚒️ START WORK COMMAND
async function startWork(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (!user.job.current) {
    const embed = createErrorEmbed('You need a job first! Use `/economy work apply` to get one.');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const now = new Date();
  const lastWork = user.lastWork;

  // 4 hour cooldown
  if (lastWork && (now - lastWork) < 4 * 60 * 60 * 1000) {
    const timeLeft = 4 * 60 * 60 * 1000 - (now - lastWork);
    const hours = Math.floor(timeLeft / (60 * 60 * 1000));
    const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));

    const embed = createErrorEmbed(`You're tired! Rest for **${hours}h ${minutes}m** before working again.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Calculate pay based on job and level
  const jobPay = {
    miner: { min: 15, max: 30 },
    farmer: { min: 10, max: 25 },
    warrior: { min: 20, max: 40 },
    merchant: { min: 12, max: 35 }
  };

  const pay = jobPay[user.job.current];
  const basePay = Math.floor(Math.random() * (pay.max - pay.min + 1)) + pay.min;
  const levelBonus = user.job.level * 2;
  const totalPay = basePay + levelBonus;

  // Random bonus items based on job
  let bonusItems = {};
  if (user.job.current === 'miner') {
    if (Math.random() < 0.3) bonusItems.stone = Math.floor(Math.random() * 3) + 1;
    if (Math.random() < 0.1) bonusItems.iron = 1;
  } else if (user.job.current === 'farmer') {
    if (Math.random() < 0.4) bonusItems.bread = Math.floor(Math.random() * 2) + 1;
  }

  // Update user
  user.coins += totalPay;
  user.job.experience += 10;
  user.job.totalWorked += 1;
  user.lastWork = now;
  user.stats.totalEarned += totalPay;

  // Add bonus items
  for (const [item, amount] of Object.entries(bonusItems)) {
    user.inventory[item] += amount;
  }

  // Level up job if enough experience
  const newJobLevel = Math.floor(user.job.experience / 100) + 1;
  if (newJobLevel > user.job.level) {
    user.job.level = newJobLevel;
  }

  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Work Completed!`)
    .setDescription(`**⚒️ Great job working as a ${user.job.current}!**`)
    .addFields(
      {
        name: '💰 **Earnings**',
        value: [
          `🪙 **Base Pay:** ${basePay} coins`,
          `📈 **Level Bonus:** +${levelBonus} coins`,
          `💰 **Total Earned:** ${totalPay} coins`
        ].join('\n'),
        inline: true
      },
      {
        name: '📊 **Job Progress**',
        value: [
          `📈 **Level:** ${user.job.level}`,
          `🎯 **Experience:** ${user.job.experience}`,
          `⚒️ **Times Worked:** ${user.job.totalWorked}`
        ].join('\n'),
        inline: true
      }
    )
    .setFooter({ text: 'Come back in 4 hours to work again!' })
    .setTimestamp();

  if (Object.keys(bonusItems).length > 0) {
    const bonusText = Object.entries(bonusItems)
      .map(([item, amount]) => `${item}: +${amount}`)
      .join(', ');
    embed.addFields({
      name: '🎁 **Bonus Items Found!**',
      value: bonusText,
      inline: false
    });
  }

  return await interaction.reply({ embeds: [embed] });
}

// 🪙 COINFLIP GAMBLING COMMAND
async function playCoinflip(interaction) {
  const bet = interaction.options.getInteger('bet');
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (user.coins < bet) {
    const embed = createErrorEmbed(`You don't have enough coins! You have ${formatNumber(user.coins)} coins.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const userChoice = Math.random() < 0.5 ? 'heads' : 'tails';
  const result = Math.random() < 0.5 ? 'heads' : 'tails';
  const won = userChoice === result;

  if (won) {
    user.coins += bet;
    user.gambling.totalWon += bet;
    user.gambling.winStreak += 1;
    user.gambling.lossStreak = 0;
    user.stats.totalEarned += bet;
  } else {
    user.coins -= bet;
    user.gambling.totalLost += bet;
    user.gambling.lossStreak += 1;
    user.gambling.winStreak = 0;
    user.stats.totalSpent += bet;
  }

  user.gambling.totalBet += bet;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(won ? COLORS.SUCCESS : COLORS.ERROR)
    .setTitle(`🪙 Coinflip Result`)
    .setDescription(`**The coin landed on ${result}!**`)
    .addFields(
      {
        name: won ? '🎉 **You Won!**' : '💸 **You Lost!**',
        value: [
          `**Your Choice:** ${userChoice}`,
          `**Result:** ${result}`,
          `**Bet:** ${formatNumber(bet)} coins`,
          won ? `**Won:** +${formatNumber(bet)} coins` : `**Lost:** -${formatNumber(bet)} coins`,
          `**New Balance:** ${formatNumber(user.coins)} coins`
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: `${won ? 'Lucky!' : 'Better luck next time!'} Win Streak: ${user.gambling.winStreak}` })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🃏 BLACKJACK GAMBLING COMMAND
async function playBlackjack(interaction) {
  const bet = interaction.options.getInteger('bet');
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (user.coins < bet) {
    const embed = createErrorEmbed(`You don't have enough coins! You have ${formatNumber(user.coins)} coins.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Simple blackjack simulation
  const playerHand = Math.floor(Math.random() * 11) + 15; // 15-25
  const dealerHand = Math.floor(Math.random() * 11) + 15; // 15-25

  let result;
  let winnings = 0;

  if (playerHand > 21) {
    result = 'bust';
    winnings = -bet;
  } else if (dealerHand > 21) {
    result = 'dealer_bust';
    winnings = bet;
  } else if (playerHand > dealerHand) {
    result = 'win';
    winnings = bet;
  } else if (playerHand === dealerHand) {
    result = 'tie';
    winnings = 0;
  } else {
    result = 'lose';
    winnings = -bet;
  }

  user.coins += winnings;
  user.gambling.totalBet += bet;

  if (winnings > 0) {
    user.gambling.totalWon += winnings;
    user.stats.totalEarned += winnings;
  } else if (winnings < 0) {
    user.gambling.totalLost += Math.abs(winnings);
    user.stats.totalSpent += Math.abs(winnings);
  }

  await user.save();

  const resultMessages = {
    bust: '💥 **You Busted!**',
    dealer_bust: '🎉 **Dealer Busted - You Win!**',
    win: '🎉 **You Win!**',
    tie: '🤝 **It\'s a Tie!**',
    lose: '💸 **You Lose!**'
  };

  const embed = new EmbedBuilder()
    .setColor(winnings >= 0 ? COLORS.SUCCESS : COLORS.ERROR)
    .setTitle(`🃏 Blackjack Result`)
    .setDescription(resultMessages[result])
    .addFields(
      {
        name: '🎴 **Game Results**',
        value: [
          `**Your Hand:** ${playerHand}`,
          `**Dealer Hand:** ${dealerHand}`,
          `**Bet:** ${formatNumber(bet)} coins`,
          winnings > 0 ? `**Won:** +${formatNumber(winnings)} coins` :
          winnings < 0 ? `**Lost:** ${formatNumber(Math.abs(winnings))} coins` : '**No Change**',
          `**New Balance:** ${formatNumber(user.coins)} coins`
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: 'Try your luck again!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🎫 LOTTERY COMMAND
async function buyLottery(interaction) {
  const tickets = interaction.options.getInteger('tickets');
  const ticketPrice = 25;
  const totalCost = tickets * ticketPrice;

  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (user.coins < totalCost) {
    const embed = createErrorEmbed(`You need ${formatNumber(totalCost)} coins but only have ${formatNumber(user.coins)} coins!`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  user.coins -= totalCost;
  user.lottery.tickets += tickets;
  user.lottery.totalSpent += totalCost;
  user.stats.totalSpent += totalCost;
  await user.save();

  // Simple lottery win chance (10% chance per ticket)
  let winnings = 0;
  for (let i = 0; i < tickets; i++) {
    if (Math.random() < 0.1) {
      const prize = Math.floor(Math.random() * 100) + 50; // 50-150 coins
      winnings += prize;
    }
  }

  if (winnings > 0) {
    user.coins += winnings;
    user.lottery.totalWon += winnings;
    user.stats.totalEarned += winnings;
    await user.save();
  }

  const embed = new EmbedBuilder()
    .setColor(winnings > 0 ? COLORS.SUCCESS : COLORS.INFO)
    .setTitle(`🎫 Lottery Tickets Purchased!`)
    .setDescription(winnings > 0 ? '🎉 **You won some prizes!**' : '🎫 **Good luck in future draws!**')
    .addFields(
      {
        name: '🎟️ **Purchase Details**',
        value: [
          `**Tickets Bought:** ${tickets}`,
          `**Cost:** ${formatNumber(totalCost)} coins`,
          `**Total Tickets:** ${user.lottery.tickets}`,
          winnings > 0 ? `**Instant Winnings:** +${formatNumber(winnings)} coins` : '**No instant wins this time**',
          `**New Balance:** ${formatNumber(user.coins)} coins`
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: 'The more tickets you have, the better your chances!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🏗️ START BUSINESS COMMAND
async function startBusiness(interaction) {
  const businessType = interaction.options.getString('type');
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (user.business.type) {
    const embed = createErrorEmbed(`You already own a ${user.business.type}! Manage it with \`/economy business manage\`.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const businessCosts = {
    shop: 500,
    farm: 300,
    mine: 800,
    factory: 1000
  };

  const cost = businessCosts[businessType];

  if (user.coins < cost) {
    const embed = createErrorEmbed(`You need ${formatNumber(cost)} coins to start a ${businessType}! You have ${formatNumber(user.coins)} coins.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  user.coins -= cost;
  user.business.type = businessType;
  user.business.level = 1;
  user.business.income = 0;
  user.business.employees = 0;
  user.business.lastCollected = new Date();
  user.stats.totalSpent += cost;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Business Started!`)
    .setDescription(`**🏗️ Congratulations on starting your ${businessType}!**`)
    .addFields(
      {
        name: '🏢 **Business Details**',
        value: [
          `**Type:** ${businessType}`,
          `**Level:** 1`,
          `**Startup Cost:** ${formatNumber(cost)} coins`,
          `**Remaining Coins:** ${formatNumber(user.coins)}`,
          `**Status:** Ready to operate!`
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: 'Use /economy business collect to earn passive income!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 📊 MANAGE BUSINESS COMMAND
async function manageBusiness(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (!user.business.type) {
    const embed = createErrorEmbed('You don\'t own a business! Start one with `/economy business start`.');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const businessInfo = {
    shop: { emoji: '🏪', income: 25, description: 'Sells goods to villagers' },
    farm: { emoji: '🌾', income: 15, description: 'Produces food and materials' },
    mine: { emoji: '⛏️', income: 35, description: 'Extracts valuable resources' },
    factory: { emoji: '🏭', income: 50, description: 'Manufactures advanced items' }
  };

  const info = businessInfo[user.business.type];
  const hourlyIncome = info.income * user.business.level;

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${info.emoji} Your ${user.business.type} Management`)
    .setDescription(`**🏢 ${info.description}**`)
    .addFields(
      {
        name: '📊 **Business Stats**',
        value: [
          `**Level:** ${user.business.level}`,
          `**Employees:** ${user.business.employees}`,
          `**Hourly Income:** ${formatNumber(hourlyIncome)} coins`,
          `**Total Earned:** ${formatNumber(user.business.income)} coins`
        ].join('\n'),
        inline: true
      },
      {
        name: '⚙️ **Operations**',
        value: [
          `**Status:** Active`,
          `**Last Collection:** <t:${Math.floor(user.business.lastCollected.getTime() / 1000)}:R>`,
          `**Efficiency:** ${user.business.level * 20}%`,
          `**Upgrade Cost:** ${formatNumber(user.business.level * 200)} coins`
        ].join('\n'),
        inline: true
      }
    )
    .setFooter({ text: 'Collect income regularly to maximize profits!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 💰 COLLECT BUSINESS INCOME COMMAND
async function collectIncome(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  if (!user.business.type) {
    const embed = createErrorEmbed('You don\'t own a business! Start one with `/economy business start`.');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const now = new Date();
  const lastCollected = user.business.lastCollected;
  const hoursElapsed = Math.floor((now - lastCollected) / (60 * 60 * 1000));

  if (hoursElapsed < 1) {
    const minutesLeft = 60 - Math.floor(((now - lastCollected) % (60 * 60 * 1000)) / (60 * 1000));
    const embed = createErrorEmbed(`You can collect income in **${minutesLeft} minutes**!`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const businessInfo = {
    shop: 25,
    farm: 15,
    mine: 35,
    factory: 50
  };

  const baseIncome = businessInfo[user.business.type];
  const hourlyIncome = baseIncome * user.business.level;
  const totalIncome = Math.min(hourlyIncome * hoursElapsed, hourlyIncome * 24); // Max 24 hours

  user.coins += totalIncome;
  user.business.income += totalIncome;
  user.business.lastCollected = now;
  user.stats.totalEarned += totalIncome;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Income Collected!`)
    .setDescription(`**💰 Your ${user.business.type} has generated income!**`)
    .addFields(
      {
        name: '💵 **Income Details**',
        value: [
          `**Hours Elapsed:** ${hoursElapsed}`,
          `**Hourly Rate:** ${formatNumber(hourlyIncome)} coins`,
          `**Total Collected:** ${formatNumber(totalIncome)} coins`,
          `**New Balance:** ${formatNumber(user.coins)} coins`
        ].join('\n'),
        inline: false
      }
    )
    .setFooter({ text: 'Your business continues to generate income!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 👤 PROFILE COMMAND
async function showProfile(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  const totalWealth = user.coins + user.gems * 10 + user.tokens * 5 + user.bank;
  const netWorth = user.stats.totalEarned - user.stats.totalSpent;

  const embed = new EmbedBuilder()
    .setColor(COLORS.PREMIUM)
    .setTitle(`👤 ${interaction.user.username}'s Profile`)
    .setDescription('**🏗️ Complete Minecraft Economy Profile**')
    .addFields(
      {
        name: '💰 **Wealth Overview**',
        value: [
          `🪙 **Coins:** ${formatNumber(user.coins)}`,
          `💎 **Gems:** ${formatNumber(user.gems)}`,
          `🎫 **Tokens:** ${formatNumber(user.tokens)}`,
          `🏦 **Bank:** ${formatNumber(user.bank)}`,
          `💎 **Total Worth:** ${formatNumber(totalWealth)}`
        ].join('\n'),
        inline: true
      },
      {
        name: '📊 **Progression**',
        value: [
          `📈 **Level:** ${user.level}`,
          `⭐ **Prestige:** ${user.prestige}`,
          `🎯 **XP:** ${formatNumber(user.xp)}`,
          `🏆 **Achievements:** ${user.achievements.length}`,
          `📋 **Commands Used:** ${user.stats.commandsUsed}`
        ].join('\n'),
        inline: true
      },
      {
        name: '💼 **Career & Business**',
        value: [
          `⚒️ **Job:** ${user.job.current || 'Unemployed'}`,
          `📈 **Job Level:** ${user.job.level}`,
          `🏢 **Business:** ${user.business.type || 'None'}`,
          `🐾 **Pets:** ${user.pets.length}`,
          `🎫 **Lottery Tickets:** ${user.lottery.tickets}`
        ].join('\n'),
        inline: false
      },
      {
        name: '📈 **Statistics**',
        value: [
          `💰 **Total Earned:** ${formatNumber(user.stats.totalEarned)}`,
          `💸 **Total Spent:** ${formatNumber(user.stats.totalSpent)}`,
          `📊 **Net Worth:** ${formatNumber(netWorth)}`,
          `🔨 **Items Crafted:** ${user.stats.itemsCrafted}`,
          `🛒 **Items Sold:** ${user.stats.itemsSold}`
        ].join('\n'),
        inline: true
      },
      {
        name: '🎰 **Gambling Stats**',
        value: [
          `💰 **Total Bet:** ${formatNumber(user.gambling.totalBet)}`,
          `🎉 **Total Won:** ${formatNumber(user.gambling.totalWon)}`,
          `💸 **Total Lost:** ${formatNumber(user.gambling.totalLost)}`,
          `🔥 **Win Streak:** ${user.gambling.winStreak}`,
          `❄️ **Loss Streak:** ${user.gambling.lossStreak}`
        ].join('\n'),
        inline: true
      }
    )
    .setThumbnail(interaction.user.displayAvatarURL())
    .setFooter({ text: 'Minecraft-inspired Economy Profile' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 🏆 ACHIEVEMENTS COMMAND
async function showAchievements(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  // Define available achievements
  const allAchievements = [
    { id: 'first_coin', name: '💰 First Coin', description: 'Earn your first coin', reward: 10 },
    { id: 'rich_player', name: '🤑 Rich Player', description: 'Accumulate 1,000 coins', reward: 100 },
    { id: 'millionaire', name: '💎 Millionaire', description: 'Reach 1,000,000 total wealth', reward: 1000 },
    { id: 'worker', name: '⚒️ Hard Worker', description: 'Work 10 times', reward: 50 },
    { id: 'crafter', name: '🔨 Master Crafter', description: 'Craft 25 items', reward: 75 },
    { id: 'gambler', name: '🎰 High Roller', description: 'Bet 10,000 coins total', reward: 200 },
    { id: 'business_owner', name: '🏢 Entrepreneur', description: 'Start a business', reward: 150 }
  ];

  // Check for new achievements
  const newAchievements = [];
  const unlockedIds = user.achievements.map(a => a.id);

  for (const achievement of allAchievements) {
    if (!unlockedIds.includes(achievement.id)) {
      let unlocked = false;

      switch (achievement.id) {
        case 'first_coin':
          unlocked = user.coins > 0 || user.stats.totalEarned > 0;
          break;
        case 'rich_player':
          unlocked = user.coins >= 1000;
          break;
        case 'millionaire':
          unlocked = (user.coins + user.gems * 10 + user.tokens * 5 + user.bank) >= 1000000;
          break;
        case 'worker':
          unlocked = user.job.totalWorked >= 10;
          break;
        case 'crafter':
          unlocked = user.stats.itemsCrafted >= 25;
          break;
        case 'gambler':
          unlocked = user.gambling.totalBet >= 10000;
          break;
        case 'business_owner':
          unlocked = user.business.type !== null;
          break;
      }

      if (unlocked) {
        user.achievements.push({
          id: achievement.id,
          name: achievement.name,
          description: achievement.description,
          reward: achievement.reward,
          unlockedAt: new Date()
        });
        user.coins += achievement.reward;
        newAchievements.push(achievement);
      }
    }
  }

  if (newAchievements.length > 0) {
    await user.save();
  }

  const embed = new EmbedBuilder()
    .setColor(COLORS.PREMIUM)
    .setTitle(`🏆 ${interaction.user.username}'s Achievements`)
    .setDescription(`**🎯 Progress: ${user.achievements.length}/${allAchievements.length} Unlocked**`);

  if (user.achievements.length > 0) {
    const unlockedText = user.achievements
      .map(a => `${a.name}\n*${a.description}* - **+${a.reward} coins**`)
      .join('\n\n');

    embed.addFields({
      name: '✅ **Unlocked Achievements**',
      value: unlockedText.length > 1024 ? unlockedText.substring(0, 1020) + '...' : unlockedText,
      inline: false
    });
  }

  const lockedAchievements = allAchievements.filter(a =>
    !user.achievements.some(ua => ua.id === a.id)
  );

  if (lockedAchievements.length > 0) {
    const lockedText = lockedAchievements
      .slice(0, 5) // Show only first 5 locked
      .map(a => `🔒 ${a.name}\n*${a.description}* - **${a.reward} coins**`)
      .join('\n\n');

    embed.addFields({
      name: '🔒 **Locked Achievements**',
      value: lockedText,
      inline: false
    });
  }

  if (newAchievements.length > 0) {
    const newText = newAchievements
      .map(a => `🎉 **${a.name}** - +${a.reward} coins!`)
      .join('\n');

    embed.addFields({
      name: '🎉 **New Achievements Unlocked!**',
      value: newText,
      inline: false
    });
  }

  embed.setFooter({ text: 'Keep playing to unlock more achievements!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// ⏰ COOLDOWNS COMMAND
async function showCooldowns(interaction) {
  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);
  const now = new Date();

  const cooldowns = [
    { name: '🎁 Daily Reward', last: user.lastDaily, duration: 24 * 60 * 60 * 1000 },
    { name: '⚒️ Work', last: user.lastWork, duration: 4 * 60 * 60 * 1000 },
    { name: '🏢 Business Collection', last: user.business.lastCollected, duration: 60 * 60 * 1000 }
  ];

  const cooldownStatus = cooldowns.map(cd => {
    if (!cd.last) {
      return `${cd.name}: **Ready!** ✅`;
    }

    const timeLeft = cd.duration - (now - cd.last);

    if (timeLeft <= 0) {
      return `${cd.name}: **Ready!** ✅`;
    }

    const hours = Math.floor(timeLeft / (60 * 60 * 1000));
    const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));

    return `${cd.name}: **${hours}h ${minutes}m** ⏰`;
  });

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`⏰ ${interaction.user.username}'s Cooldowns`)
    .setDescription('**🕐 Command availability status**')
    .addFields({
      name: '📋 **Cooldown Status**',
      value: cooldownStatus.join('\n'),
      inline: false
    })
    .setFooter({ text: 'Cooldowns prevent spam and maintain game balance' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}

// 💰 SELL ITEM COMMAND (Additional handler)
async function sellItem(interaction) {
  const itemName = interaction.options.getString('item');
  const quantity = interaction.options.getInteger('quantity') || 1;

  const user = await getOrCreateUser(interaction.user.id, interaction.guild.id);

  // Check if user has the item
  if (!user.inventory[itemName] || user.inventory[itemName] < quantity) {
    const embed = createErrorEmbed(`You don't have enough ${itemName}! You have ${user.inventory[itemName] || 0}.`);
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // Sell prices (50% of buy price)
  const sellPrices = {
    wood: 5,
    stone: 7,
    iron: 12,
    bread: 2,
    ironSword: 50,
    helmet: 37
  };

  const sellPrice = sellPrices[itemName];
  if (!sellPrice) {
    const embed = createErrorEmbed('This item cannot be sold!');
    return await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const totalEarnings = sellPrice * quantity;

  // Process sale
  user.inventory[itemName] -= quantity;
  user.coins += totalEarnings;
  user.stats.itemsSold += quantity;
  user.stats.totalEarned += totalEarnings;
  await user.save();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Items Sold!`)
    .setDescription('**💰 Your items have been sold successfully!**')
    .addFields({
      name: '💵 **Sale Details**',
      value: [
        `**Item:** ${itemName}`,
        `**Quantity:** ${quantity}`,
        `**Price per item:** ${sellPrice} coins`,
        `**Total Earned:** ${formatNumber(totalEarnings)} coins`,
        `**New Balance:** ${formatNumber(user.coins)} coins`
      ].join('\n'),
      inline: false
    })
    .setFooter({ text: 'Thanks for trading with us!' })
    .setTimestamp();

  return await interaction.reply({ embeds: [embed] });
}
