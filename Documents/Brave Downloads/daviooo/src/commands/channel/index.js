// commands/automod/index.js
const fs = require('fs');
const path = require('path');
const {
  SlashCommandBuilder,
  SlashCommandSubcommandBuilder
} = require('discord.js');

// Get the folder name as the command name
const categoryName = path.basename(__dirname);

const builder = new SlashCommandBuilder()
  .setName(categoryName)
  .setDescription(`All ${categoryName} subcommands`);

const executors = new Map();

// Load each .js file except index.js
fs.readdirSync(__dirname)
  .filter(file => file.endsWith('.js') && file !== 'index.js')
  .forEach(file => {
    const cmd = require(path.join(__dirname, file));

    let subBuilder;
    if (cmd.data instanceof SlashCommandSubcommandBuilder) {
      // Already a subcommand
      subBuilder = cmd.data;
    } else if (cmd.data instanceof SlashCommandBuilder) {
      // Convert top-level command into subcommand
      const json = cmd.data.toJSON();
      subBuilder = new SlashCommandSubcommandBuilder()
        .setName(json.name)
        .setDescription(json.description || '');

      // Copy options correctly
      for (const opt of json.options ?? []) {
        let optBuilder;

        switch (opt.type) {
          // **Skip structural types** (they're already handled by addSubcommand)
          case 1: // SUB_COMMAND
          case 2: // SUB_COMMAND_GROUP
            continue;

          case 3: // STRING
            optBuilder = subBuilder.addStringOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 4: // INTEGER
            optBuilder = subBuilder.addIntegerOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 5: // BOOLEAN
            optBuilder = subBuilder.addBooleanOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 6: // USER
            optBuilder = subBuilder.addUserOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 7: // CHANNEL
            optBuilder = subBuilder.addChannelOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 8: // ROLE
            optBuilder = subBuilder.addRoleOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 9: // MENTIONABLE
            optBuilder = subBuilder.addMentionableOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 10: // NUMBER (FLOAT)
            optBuilder = subBuilder.addNumberOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          case 11: // ATTACHMENT
            optBuilder = subBuilder.addAttachmentOption(o =>
              o.setName(opt.name)
               .setDescription(opt.description || '')
               .setRequired(Boolean(opt.required))
            ).options.at(-1);
            break;
          default:
            console.warn(`Unknown option type ${opt.type} for subcommand ${json.name}`);
            continue;
        }

        // Handle Choices if available
        if (opt.choices) {
          optBuilder.addChoices(...opt.choices.map(choice => ({
            name: choice.name,
            value: choice.value
          })));
        }
      }
    } else {
      console.warn(`Skipping ${file}: Not a valid command export.`);
      return;
    }

    builder.addSubcommand(subBuilder);
    executors.set(subBuilder.name, cmd.execute);
  });

// Export the top-level command for this category
module.exports = {
  data: builder,
  async execute(interaction) {
    const sub = interaction.options.getSubcommand();
    const run = executors.get(sub);
    if (!run) {
      return interaction.reply({ content: '❌ Unknown subcommand', ephemeral: true });
    }
    try {
      await run(interaction);
    } catch (err) {
      console.error(err);
      await interaction.reply({ content: '❌ Error executing subcommand', ephemeral: true });
    }
  },
};
