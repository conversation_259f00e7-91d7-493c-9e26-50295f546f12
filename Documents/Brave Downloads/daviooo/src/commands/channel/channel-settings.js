// 🏛️ ENTERPRISE CHANNEL MANAGEMENT SYSTEM - ACADEMIC EXCELLENCE
// Professional channel configuration and management with advanced features

const {
  SlashCommandBuilder,
  PermissionFlagsBits,
  EmbedBuilder,
  ChannelType,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
  OverwriteType
} = require('discord.js');

// 🎨 PROFESSIONAL ACADEMIC COLORS & EMOJIS
const COLORS = {
  SUCCESS: '#00D166',
  ERROR: '#FF3838',
  WARNING: '#FFB02E',
  INFO: '#00B4D8',
  PREMIUM: '#7209B7',
  ACADEMIC: '#1E3A8A'
};

const EMOJIS = {
  SETTINGS: '⚙️',
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  CHANNEL: '📝',
  LOCK: '🔒',
  UNLOCK: '🔓',
  SLOWMODE: '⏱️',
  TOPIC: '📋',
  RENAME: '✏️',
  PERMISSIONS: '🛡️',
  CATEGORY: '📁',
  ARCHIVE: '📦',
  CLONE: '📄',
  STATS: '📊',
  CROWN: '👑',
  SHIELD: '🛡️',
  GEAR: '⚙️'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('channel')
    .setDescription('🏛️ Enterprise Channel Management System - Academic Grade')
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels)

    // 📝 BASIC SETTINGS GROUP
    .addSubcommandGroup(group =>
      group.setName('settings')
        .setDescription('📝 Basic channel configuration')
        .addSubcommand(subcommand =>
          subcommand.setName('slowmode')
            .setDescription('⏱️ Configure channel slowmode with presets')
            .addStringOption(option =>
              option.setName('duration')
                .setDescription('Slowmode duration preset or custom')
                .addChoices(
                  { name: '🚫 Disabled', value: '0' },
                  { name: '⚡ 5 seconds', value: '5' },
                  { name: '🔥 10 seconds', value: '10' },
                  { name: '⏰ 30 seconds', value: '30' },
                  { name: '🕐 1 minute', value: '60' },
                  { name: '🕕 5 minutes', value: '300' },
                  { name: '🕙 10 minutes', value: '600' },
                  { name: '🔒 Custom', value: 'custom' }
                )
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to modify (current if not specified)')
                .addChannelTypes(ChannelType.GuildText, ChannelType.GuildVoice, ChannelType.GuildForum))
            .addIntegerOption(option =>
              option.setName('custom_seconds')
                .setDescription('Custom slowmode in seconds (0-21600)')
                .setMinValue(0)
                .setMaxValue(21600)))
        .addSubcommand(subcommand =>
          subcommand.setName('topic')
            .setDescription('📋 Set channel topic with templates')
            .addStringOption(option =>
              option.setName('template')
                .setDescription('Topic template or custom')
                .addChoices(
                  { name: '💬 General Chat', value: 'general' },
                  { name: '📢 Announcements', value: 'announcements' },
                  { name: '❓ Support & Help', value: 'support' },
                  { name: '🎮 Gaming Discussion', value: 'gaming' },
                  { name: '📚 Study & Learning', value: 'study' },
                  { name: '🎨 Creative Showcase', value: 'creative' },
                  { name: '✏️ Custom Topic', value: 'custom' }
                )
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to modify (current if not specified)')
                .addChannelTypes(ChannelType.GuildText, ChannelType.GuildForum))
            .addStringOption(option =>
              option.setName('custom_topic')
                .setDescription('Custom topic text (max 1024 characters)')
                .setMaxLength(1024)))
        .addSubcommand(subcommand =>
          subcommand.setName('rename')
            .setDescription('✏️ Rename channel with validation')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to rename')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('name')
                .setDescription('New channel name (auto-formatted)')
                .setMaxLength(100)
                .setRequired(true))
            .addBooleanOption(option =>
              option.setName('auto_format')
                .setDescription('Auto-format name (lowercase, hyphens)')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('nsfw')
            .setDescription('🔞 Toggle NSFW status for channel')
            .addBooleanOption(option =>
              option.setName('enabled')
                .setDescription('Enable or disable NSFW')
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to modify (current if not specified)')
                .addChannelTypes(ChannelType.GuildText))))

    // 🛡️ PERMISSIONS GROUP
    .addSubcommandGroup(group =>
      group.setName('permissions')
        .setDescription('🛡️ Advanced permission management')
        .addSubcommand(subcommand =>
          subcommand.setName('lock')
            .setDescription('🔒 Lock channel for specific roles')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to lock (current if not specified)'))
            .addRoleOption(option =>
              option.setName('role')
                .setDescription('Role to restrict (everyone if not specified)'))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for locking the channel')
                .setMaxLength(512)))
        .addSubcommand(subcommand =>
          subcommand.setName('unlock')
            .setDescription('🔓 Unlock channel permissions')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to unlock (current if not specified)'))
            .addRoleOption(option =>
              option.setName('role')
                .setDescription('Role to restore permissions for')))
        .addSubcommand(subcommand =>
          subcommand.setName('view')
            .setDescription('👁️ View channel permissions overview')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to analyze (current if not specified)')))
        .addSubcommand(subcommand =>
          subcommand.setName('sync')
            .setDescription('🔄 Sync permissions with category')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to sync')
                .setRequired(true))))

    // 📁 MANAGEMENT GROUP
    .addSubcommandGroup(group =>
      group.setName('manage')
        .setDescription('📁 Advanced channel management')
        .addSubcommand(subcommand =>
          subcommand.setName('clone')
            .setDescription('📄 Clone channel with all settings')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to clone')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('name')
                .setDescription('Name for cloned channel')
                .setMaxLength(100))
            .addBooleanOption(option =>
              option.setName('include_messages')
                .setDescription('Include recent messages (last 50)')
                .setRequired(false)))
        .addSubcommand(subcommand =>
          subcommand.setName('archive')
            .setDescription('📦 Archive channel (lock and move to archive category)')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to archive')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for archiving')
                .setMaxLength(512)))
        .addSubcommand(subcommand =>
          subcommand.setName('category')
            .setDescription('📁 Move channel to different category')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to move')
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('category')
                .setDescription('Target category')
                .addChannelTypes(ChannelType.GuildCategory)
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('position')
            .setDescription('📍 Change channel position')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to move')
                .setRequired(true))
            .addIntegerOption(option =>
              option.setName('position')
                .setDescription('New position (0 = top)')
                .setMinValue(0)
                .setRequired(true))))

    // 📊 ANALYTICS GROUP
    .addSubcommandGroup(group =>
      group.setName('info')
        .setDescription('📊 Channel information and analytics')
        .addSubcommand(subcommand =>
          subcommand.setName('stats')
            .setDescription('📊 Detailed channel statistics')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to analyze (current if not specified)')))
        .addSubcommand(subcommand =>
          subcommand.setName('overview')
            .setDescription('🔍 Complete channel overview')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to analyze (current if not specified)')))
        .addSubcommand(subcommand =>
          subcommand.setName('history')
            .setDescription('📜 Channel modification history')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to check history for')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('compare')
            .setDescription('⚖️ Compare two channels')
            .addChannelOption(option =>
              option.setName('channel1')
                .setDescription('First channel to compare')
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('channel2')
                .setDescription('Second channel to compare')
                .setRequired(true)))),

  async execute(interaction) {
    try {
      // 🔐 PERMISSION VALIDATION
      if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
        return await interaction.reply({
          embeds: [createErrorEmbed('You need **Manage Channels** permission to use this command!')],
          ephemeral: true
        });
      }

      const subcommandGroup = interaction.options.getSubcommandGroup();
      const subcommand = interaction.options.getSubcommand();

      // 🎯 ROUTE TO APPROPRIATE HANDLER
      switch (subcommandGroup) {
        case 'settings':
          return await handleSettings(interaction, subcommand);
        case 'permissions':
          return await handlePermissions(interaction, subcommand);
        case 'manage':
          return await handleManagement(interaction, subcommand);
        case 'info':
          return await handleInfo(interaction, subcommand);
        default:
          return await interaction.reply({
            embeds: [createErrorEmbed('Unknown command group!')],
            ephemeral: true
          });
      }

    } catch (error) {
      console.error('🔥 Channel Command Error:', error);

      const errorEmbed = new EmbedBuilder()
        .setColor(COLORS.ERROR)
        .setTitle(`${EMOJIS.ERROR} Critical Error`)
        .setDescription('An unexpected error occurred. Please try again or contact support.')
        .addFields({
          name: '🐛 Error Details',
          value: `\`\`\`${error.message}\`\`\``,
          inline: false
        })
        .setTimestamp()
        .setFooter({ text: 'Enterprise Channel Management System' });

      return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }
};

// 📝 SETTINGS HANDLER
async function handleSettings(interaction, subcommand) {
  const targetChannel = interaction.options.getChannel('channel') || interaction.channel;

  switch (subcommand) {
    case 'slowmode':
      return await handleSlowmode(interaction, targetChannel);
    case 'topic':
      return await handleTopic(interaction, targetChannel);
    case 'rename':
      return await handleRename(interaction);
    case 'nsfw':
      return await handleNSFW(interaction, targetChannel);
  }
}

// ⏱️ SLOWMODE HANDLER
async function handleSlowmode(interaction, channel) {
  const duration = interaction.options.getString('duration');
  let seconds;

  if (duration === 'custom') {
    seconds = interaction.options.getInteger('custom_seconds');
    if (seconds === null) {
      return await interaction.reply({
        embeds: [createErrorEmbed('Please provide custom seconds when using custom duration!')],
        ephemeral: true
      });
    }
  } else {
    seconds = parseInt(duration);
  }

  const oldSlowmode = channel.rateLimitPerUser;
  await channel.setRateLimitPerUser(seconds, `Slowmode changed by ${interaction.user.tag}`);

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Slowmode Configuration Updated`)
    .setDescription(`${EMOJIS.SLOWMODE} **Channel:** ${channel}`)
    .addFields(
      { name: '⏮️ Previous Setting', value: oldSlowmode === 0 ? 'Disabled' : `${oldSlowmode} seconds`, inline: true },
      { name: '⏭️ New Setting', value: seconds === 0 ? 'Disabled' : `${seconds} seconds`, inline: true },
      { name: '👤 Modified By', value: interaction.user.tag, inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Academic Grade Channel Management' });

  if (seconds > 0) {
    embed.addFields({
      name: '📊 Impact Analysis',
      value: [
        `• Users must wait **${seconds}s** between messages`,
        `• Helps prevent spam and encourages quality discussion`,
        `• Moderators and users with Manage Messages bypass this limit`
      ].join('\n'),
      inline: false
    });
  }

  return await interaction.reply({ embeds: [embed] });
}

// 📋 TOPIC HANDLER
async function handleTopic(interaction, channel) {
  const template = interaction.options.getString('template');
  let topic;

  if (template === 'custom') {
    topic = interaction.options.getString('custom_topic');
    if (!topic) {
      return await interaction.reply({
        embeds: [createErrorEmbed('Please provide custom topic when using custom template!')],
        ephemeral: true
      });
    }
  } else {
    const templates = {
      'general': '💬 Welcome to general chat! Please keep discussions friendly and on-topic. Have fun! 🎉',
      'announcements': '📢 Important server announcements and updates will be posted here. Stay informed! 📰',
      'support': '❓ Need help? Ask your questions here and our community will assist you! 🤝',
      'gaming': '🎮 Gaming discussions, LFG posts, and game-related content. Let\'s play together! 🕹️',
      'study': '📚 Study together, share resources, and help each other learn. Knowledge is power! 🧠',
      'creative': '🎨 Share your creative works, get feedback, and inspire others with your art! ✨'
    };
    topic = templates[template];
  }

  const oldTopic = channel.topic || 'No topic set';
  await channel.setTopic(topic, `Topic changed by ${interaction.user.tag}`);

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Channel Topic Updated`)
    .setDescription(`${EMOJIS.TOPIC} **Channel:** ${channel}`)
    .addFields(
      { name: '📝 New Topic', value: topic.length > 1024 ? `${topic.substring(0, 1020)}...` : topic, inline: false },
      { name: '👤 Modified By', value: interaction.user.tag, inline: true },
      { name: '📊 Character Count', value: `${topic.length}/1024`, inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Professional Topic Management' });

  return await interaction.reply({ embeds: [embed] });
}

// ✏️ RENAME HANDLER
async function handleRename(interaction) {
  const channel = interaction.options.getChannel('channel');
  const newName = interaction.options.getString('name');
  const autoFormat = interaction.options.getBoolean('auto_format') ?? true;

  const oldName = channel.name;
  let formattedName = newName;

  if (autoFormat) {
    formattedName = newName
      .toLowerCase()
      .replace(/[^a-z0-9\-_]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 100);
  }

  await channel.setName(formattedName, `Channel renamed by ${interaction.user.tag}`);

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Channel Successfully Renamed`)
    .setDescription(`${EMOJIS.RENAME} Channel has been renamed with professional formatting`)
    .addFields(
      { name: '📝 Original Name', value: `\`${oldName}\``, inline: true },
      { name: '✨ New Name', value: `\`${formattedName}\``, inline: true },
      { name: '🔧 Auto-Formatted', value: autoFormat ? 'Yes' : 'No', inline: true },
      { name: '👤 Modified By', value: interaction.user.tag, inline: true },
      { name: '📊 Name Length', value: `${formattedName.length}/100 characters`, inline: true },
      { name: '🔗 Channel Link', value: `${channel}`, inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Enterprise Channel Naming System' });

  if (newName !== formattedName) {
    embed.addFields({
      name: '🔄 Formatting Applied',
      value: [
        '• Converted to lowercase',
        '• Replaced invalid characters with hyphens',
        '• Removed duplicate hyphens',
        '• Trimmed leading/trailing hyphens'
      ].join('\n'),
      inline: false
    });
  }

  return await interaction.reply({ embeds: [embed] });
}

// 🔞 NSFW HANDLER
async function handleNSFW(interaction, channel) {
  const enabled = interaction.options.getBoolean('enabled');

  if (channel.type !== ChannelType.GuildText) {
    return await interaction.reply({
      embeds: [createErrorEmbed('NSFW setting can only be applied to text channels!')],
      ephemeral: true
    });
  }

  const wasNSFW = channel.nsfw;
  await channel.setNSFW(enabled, `NSFW setting changed by ${interaction.user.tag}`);

  const embed = new EmbedBuilder()
    .setColor(enabled ? COLORS.WARNING : COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} NSFW Setting Updated`)
    .setDescription(`${enabled ? '🔞' : '✅'} **Channel:** ${channel}`)
    .addFields(
      { name: '⏮️ Previous Setting', value: wasNSFW ? '🔞 NSFW Enabled' : '✅ NSFW Disabled', inline: true },
      { name: '⏭️ New Setting', value: enabled ? '🔞 NSFW Enabled' : '✅ NSFW Disabled', inline: true },
      { name: '👤 Modified By', value: interaction.user.tag, inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Content Safety Management' });

  if (enabled) {
    embed.addFields({
      name: '⚠️ NSFW Channel Guidelines',
      value: [
        '• Content must comply with Discord ToS',
        '• Users must be 18+ to access this channel',
        '• Appropriate content warnings should be used',
        '• Server rules still apply'
      ].join('\n'),
      inline: false
    });
  }

  return await interaction.reply({ embeds: [embed] });
}

// 🛡️ PERMISSIONS HANDLER
async function handlePermissions(interaction, subcommand) {
  switch (subcommand) {
    case 'lock':
      return await handleLock(interaction);
    case 'unlock':
      return await handleUnlock(interaction);
    case 'view':
      return await handleViewPermissions(interaction);
    case 'sync':
      return await handleSyncPermissions(interaction);
  }
}

// 🔒 LOCK HANDLER
async function handleLock(interaction) {
  const channel = interaction.options.getChannel('channel') || interaction.channel;
  const role = interaction.options.getRole('role') || interaction.guild.roles.everyone;
  const reason = interaction.options.getString('reason') || 'Channel locked by moderator';

  await channel.permissionOverwrites.edit(role, {
    SendMessages: false,
    AddReactions: false,
    CreatePublicThreads: false,
    CreatePrivateThreads: false,
    SendMessagesInThreads: false
  }, { reason: `Locked by ${interaction.user.tag}: ${reason}` });

  const embed = new EmbedBuilder()
    .setColor(COLORS.WARNING)
    .setTitle(`${EMOJIS.LOCK} Channel Successfully Locked`)
    .setDescription(`${EMOJIS.SHIELD} Security measures have been applied`)
    .addFields(
      { name: '📝 Channel', value: `${channel}`, inline: true },
      { name: '🎭 Affected Role', value: `${role}`, inline: true },
      { name: '👤 Locked By', value: interaction.user.tag, inline: true },
      { name: '📋 Reason', value: reason, inline: false },
      { name: '🚫 Restricted Actions', value: [
        '• Send Messages',
        '• Add Reactions',
        '• Create Threads',
        '• Send Messages in Threads'
      ].join('\n'), inline: false }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Security System' });

  return await interaction.reply({ embeds: [embed] });
}

// 🔓 UNLOCK HANDLER
async function handleUnlock(interaction) {
  const channel = interaction.options.getChannel('channel') || interaction.channel;
  const role = interaction.options.getRole('role') || interaction.guild.roles.everyone;

  await channel.permissionOverwrites.edit(role, {
    SendMessages: null,
    AddReactions: null,
    CreatePublicThreads: null,
    CreatePrivateThreads: null,
    SendMessagesInThreads: null
  }, { reason: `Unlocked by ${interaction.user.tag}` });

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.UNLOCK} Channel Successfully Unlocked`)
    .setDescription(`${EMOJIS.SUCCESS} Permissions have been restored`)
    .addFields(
      { name: '📝 Channel', value: `${channel}`, inline: true },
      { name: '🎭 Affected Role', value: `${role}`, inline: true },
      { name: '👤 Unlocked By', value: interaction.user.tag, inline: true },
      { name: '✅ Restored Actions', value: [
        '• Send Messages',
        '• Add Reactions',
        '• Create Threads',
        '• Send Messages in Threads'
      ].join('\n'), inline: false }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Security System' });

  return await interaction.reply({ embeds: [embed] });
}

// 👁️ VIEW PERMISSIONS HANDLER
async function handleViewPermissions(interaction) {
  const channel = interaction.options.getChannel('channel') || interaction.channel;
  const overwrites = channel.permissionOverwrites.cache;

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.SHIELD} Channel Permissions Overview`)
    .setDescription(`${EMOJIS.CHANNEL} **Channel:** ${channel}`)
    .addFields(
      { name: '📊 Basic Information', value: [
        `**Type:** ${channel.type === ChannelType.GuildText ? 'Text Channel' : 'Voice Channel'}`,
        `**Category:** ${channel.parent?.name || 'None'}`,
        `**Position:** ${channel.position}`,
        `**Created:** <t:${Math.floor(channel.createdTimestamp / 1000)}:R>`
      ].join('\n'), inline: false }
    );

  if (overwrites.size > 0) {
    const permissionList = overwrites.map(overwrite => {
      const target = overwrite.type === OverwriteType.Role
        ? interaction.guild.roles.cache.get(overwrite.id)
        : interaction.guild.members.cache.get(overwrite.id);

      const allowed = overwrite.allow.toArray();
      const denied = overwrite.deny.toArray();

      let status = '';
      if (allowed.length > 0) status += `✅ ${allowed.length} allowed`;
      if (denied.length > 0) status += `${status ? ' | ' : ''}❌ ${denied.length} denied`;

      return `${overwrite.type === OverwriteType.Role ? '🎭' : '👤'} ${target?.name || target?.displayName || 'Unknown'} - ${status || 'No overrides'}`;
    }).join('\n');

    embed.addFields({
      name: '🛡️ Permission Overrides',
      value: permissionList.length > 1024 ? `${permissionList.substring(0, 1020)}...` : permissionList,
      inline: false
    });
  } else {
    embed.addFields({
      name: '🛡️ Permission Overrides',
      value: 'No custom permission overrides set',
      inline: false
    });
  }

  return await interaction.reply({ embeds: [embed] });
}

// 🔄 SYNC PERMISSIONS HANDLER
async function handleSyncPermissions(interaction) {
  const channel = interaction.options.getChannel('channel');

  if (!channel.parent) {
    return await interaction.reply({
      embeds: [createErrorEmbed('Channel must be in a category to sync permissions!')],
      ephemeral: true
    });
  }

  await channel.lockPermissions();

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Permissions Synchronized`)
    .setDescription(`${EMOJIS.GEAR} Channel permissions synced with category`)
    .addFields(
      { name: '📝 Channel', value: `${channel}`, inline: true },
      { name: '📁 Category', value: `${channel.parent}`, inline: true },
      { name: '👤 Synced By', value: interaction.user.tag, inline: true },
      { name: '🔄 Synchronization Details', value: [
        '• All permission overrides copied from category',
        '• Previous channel-specific overrides removed',
        '• Channel now inherits category permissions',
        '• Changes take effect immediately'
      ].join('\n'), inline: false }
    )
    .setTimestamp()
    .setFooter({ text: 'Permission Synchronization System' });

  return await interaction.reply({ embeds: [embed] });
}

// 📁 MANAGEMENT HANDLER
async function handleManagement(interaction, subcommand) {
  switch (subcommand) {
    case 'clone':
      return await handleClone(interaction);
    case 'archive':
      return await handleArchive(interaction);
    case 'category':
      return await handleCategory(interaction);
    case 'position':
      return await handlePosition(interaction);
  }
}

// 📄 CLONE HANDLER
async function handleClone(interaction) {
  const channel = interaction.options.getChannel('channel');
  const name = interaction.options.getString('name') || `${channel.name}-copy`;
  const includeMessages = interaction.options.getBoolean('include_messages') || false;

  await interaction.deferReply();

  const clonedChannel = await channel.clone({
    name: name,
    reason: `Channel cloned by ${interaction.user.tag}`
  });

  // Copy recent messages if requested
  if (includeMessages && channel.type === ChannelType.GuildText) {
    try {
      const messages = await channel.messages.fetch({ limit: 50 });
      const messageArray = Array.from(messages.values()).reverse();

      for (const message of messageArray.slice(0, 10)) { // Limit to 10 messages to avoid rate limits
        if (message.content) {
          await clonedChannel.send({
            content: `**${message.author.tag}** (${new Date(message.createdTimestamp).toLocaleString()}):\n${message.content}`
          });
        }
      }
    } catch (error) {
      console.error('Error copying messages:', error);
    }
  }

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Channel Successfully Cloned`)
    .setDescription(`${EMOJIS.CLONE} Professional channel duplication completed`)
    .addFields(
      { name: '📝 Original Channel', value: `${channel}`, inline: true },
      { name: '📄 Cloned Channel', value: `${clonedChannel}`, inline: true },
      { name: '👤 Cloned By', value: interaction.user.tag, inline: true },
      { name: '📊 Cloning Details', value: [
        `**Name:** ${clonedChannel.name}`,
        `**Type:** ${clonedChannel.type === ChannelType.GuildText ? 'Text Channel' : 'Voice Channel'}`,
        `**Category:** ${clonedChannel.parent?.name || 'None'}`,
        `**Messages Copied:** ${includeMessages ? 'Yes (last 10)' : 'No'}`
      ].join('\n'), inline: false }
    )
    .setTimestamp()
    .setFooter({ text: 'Enterprise Channel Cloning System' });

  return await interaction.editReply({ embeds: [embed] });
}

// 📦 ARCHIVE HANDLER
async function handleArchive(interaction) {
  const channel = interaction.options.getChannel('channel');
  const reason = interaction.options.getString('reason') || 'Channel archived by moderator';

  // Find or create archive category
  let archiveCategory = interaction.guild.channels.cache.find(c =>
    c.type === ChannelType.GuildCategory && c.name.toLowerCase().includes('archive')
  );

  if (!archiveCategory) {
    archiveCategory = await interaction.guild.channels.create({
      name: '📦 Archived Channels',
      type: ChannelType.GuildCategory,
      reason: 'Archive category created automatically'
    });
  }

  // Lock channel and move to archive
  await channel.permissionOverwrites.edit(interaction.guild.roles.everyone, {
    SendMessages: false,
    AddReactions: false
  });

  await channel.setParent(archiveCategory, { reason: `Archived by ${interaction.user.tag}` });
  await channel.setName(`archived-${channel.name}`, { reason: 'Channel archived' });

  const embed = new EmbedBuilder()
    .setColor(COLORS.WARNING)
    .setTitle(`${EMOJIS.ARCHIVE} Channel Successfully Archived`)
    .setDescription(`${EMOJIS.SUCCESS} Channel has been locked and moved to archive`)
    .addFields(
      { name: '📝 Channel', value: `${channel}`, inline: true },
      { name: '📁 Archive Category', value: `${archiveCategory}`, inline: true },
      { name: '👤 Archived By', value: interaction.user.tag, inline: true },
      { name: '📋 Reason', value: reason, inline: false },
      { name: '📦 Archive Actions', value: [
        '• Channel locked (no new messages)',
        '• Moved to archive category',
        '• Name prefixed with "archived-"',
        '• All content preserved'
      ].join('\n'), inline: false }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Archive System' });

  return await interaction.reply({ embeds: [embed] });
}

// 📁 CATEGORY HANDLER
async function handleCategory(interaction) {
  const channel = interaction.options.getChannel('channel');
  const category = interaction.options.getChannel('category');

  const oldCategory = channel.parent;
  await channel.setParent(category, { reason: `Category changed by ${interaction.user.tag}` });

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Channel Category Updated`)
    .setDescription(`${EMOJIS.CATEGORY} Channel successfully moved to new category`)
    .addFields(
      { name: '📝 Channel', value: `${channel}`, inline: true },
      { name: '📁 Previous Category', value: oldCategory?.name || 'None', inline: true },
      { name: '📁 New Category', value: category.name, inline: true },
      { name: '👤 Moved By', value: interaction.user.tag, inline: true },
      { name: '📊 Position', value: `${channel.position}`, inline: true },
      { name: '🔄 Permissions', value: 'Inherited from new category', inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Organization System' });

  return await interaction.reply({ embeds: [embed] });
}

// 📍 POSITION HANDLER
async function handlePosition(interaction) {
  const channel = interaction.options.getChannel('channel');
  const position = interaction.options.getInteger('position');

  const oldPosition = channel.position;
  await channel.setPosition(position, { reason: `Position changed by ${interaction.user.tag}` });

  const embed = new EmbedBuilder()
    .setColor(COLORS.SUCCESS)
    .setTitle(`${EMOJIS.SUCCESS} Channel Position Updated`)
    .setDescription(`${EMOJIS.GEAR} Channel position successfully modified`)
    .addFields(
      { name: '📝 Channel', value: `${channel}`, inline: true },
      { name: '📍 Previous Position', value: `${oldPosition}`, inline: true },
      { name: '📍 New Position', value: `${position}`, inline: true },
      { name: '👤 Modified By', value: interaction.user.tag, inline: true },
      { name: '📁 Category', value: channel.parent?.name || 'None', inline: true },
      { name: '📊 Total Channels', value: `${channel.guild.channels.cache.size}`, inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Positioning System' });

  return await interaction.reply({ embeds: [embed] });
}

// 📊 INFO HANDLER
async function handleInfo(interaction, subcommand) {
  switch (subcommand) {
    case 'stats':
      return await handleStats(interaction);
    case 'overview':
      return await handleOverview(interaction);
    case 'history':
      return await handleHistory(interaction);
    case 'compare':
      return await handleCompare(interaction);
  }
}

// 📊 STATS HANDLER
async function handleStats(interaction) {
  const channel = interaction.options.getChannel('channel') || interaction.channel;

  let messageCount = 0;
  let memberCount = 0;
  let lastActivity = null;

  if (channel.type === ChannelType.GuildText) {
    try {
      const messages = await channel.messages.fetch({ limit: 100 });
      messageCount = messages.size;
      lastActivity = messages.first()?.createdAt;
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  } else if (channel.type === ChannelType.GuildVoice) {
    memberCount = channel.members.size;
  }

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.STATS} Channel Statistics`)
    .setDescription(`${EMOJIS.CHANNEL} **${channel.name}** Analytics Dashboard`)
    .addFields(
      { name: '📊 Basic Information', value: [
        `**Type:** ${getChannelTypeString(channel.type)}`,
        `**Created:** <t:${Math.floor(channel.createdTimestamp / 1000)}:R>`,
        `**Category:** ${channel.parent?.name || 'None'}`,
        `**Position:** ${channel.position}`
      ].join('\n'), inline: true },
      { name: '📈 Activity Statistics', value: [
        `**Recent Messages:** ${messageCount}`,
        `**Current Members:** ${memberCount}`,
        `**Last Activity:** ${lastActivity ? `<t:${Math.floor(lastActivity.getTime() / 1000)}:R>` : 'Unknown'}`,
        `**NSFW:** ${channel.nsfw ? 'Yes' : 'No'}`
      ].join('\n'), inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Analytics System' });

  if (channel.topic) {
    embed.addFields({
      name: '📋 Channel Topic',
      value: channel.topic.length > 1024 ? `${channel.topic.substring(0, 1020)}...` : channel.topic,
      inline: false
    });
  }

  return await interaction.reply({ embeds: [embed] });
}

// 🔍 OVERVIEW HANDLER
async function handleOverview(interaction) {
  const channel = interaction.options.getChannel('channel') || interaction.channel;
  const overwrites = channel.permissionOverwrites.cache;

  const embed = new EmbedBuilder()
    .setColor(COLORS.ACADEMIC)
    .setTitle(`${EMOJIS.CROWN} Complete Channel Overview`)
    .setDescription(`${EMOJIS.CHANNEL} **${channel.name}** - Enterprise Analysis`)
    .addFields(
      { name: '🏛️ Channel Identity', value: [
        `**Name:** ${channel.name}`,
        `**ID:** ${channel.id}`,
        `**Type:** ${getChannelTypeString(channel.type)}`,
        `**Created:** <t:${Math.floor(channel.createdTimestamp / 1000)}:F>`
      ].join('\n'), inline: true },
      { name: '📁 Organization', value: [
        `**Category:** ${channel.parent?.name || 'None'}`,
        `**Position:** ${channel.position}`,
        `**Guild:** ${channel.guild.name}`,
        `**Region:** ${channel.guild.preferredLocale || 'Unknown'}`
      ].join('\n'), inline: true },
      { name: '⚙️ Configuration', value: [
        `**NSFW:** ${channel.nsfw ? 'Enabled' : 'Disabled'}`,
        `**Slowmode:** ${channel.rateLimitPerUser || 0}s`,
        `**Permissions:** ${overwrites.size} overrides`,
        `**Threads:** ${channel.threads?.cache.size || 0} active`
      ].join('\n'), inline: true }
    )
    .setTimestamp()
    .setFooter({ text: 'Enterprise Channel Overview System' });

  return await interaction.reply({ embeds: [embed] });
}

// 📜 HISTORY HANDLER (Placeholder)
async function handleHistory(interaction) {
  const channel = interaction.options.getChannel('channel');

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.STATS} Channel History`)
    .setDescription(`📜 **${channel.name}** Modification History`)
    .addFields({
      name: '🚧 Feature Coming Soon',
      value: 'Channel modification history tracking will be available in a future update. This feature will include:\n• Name changes\n• Permission modifications\n• Topic updates\n• Category moves',
      inline: false
    })
    .setTimestamp()
    .setFooter({ text: 'Channel History System - In Development' });

  return await interaction.reply({ embeds: [embed] });
}

// ⚖️ COMPARE HANDLER
async function handleCompare(interaction) {
  const channel1 = interaction.options.getChannel('channel1');
  const channel2 = interaction.options.getChannel('channel2');

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.STATS} Channel Comparison`)
    .setDescription(`⚖️ Comparing **${channel1.name}** vs **${channel2.name}**`)
    .addFields(
      { name: `📝 ${channel1.name}`, value: [
        `**Type:** ${getChannelTypeString(channel1.type)}`,
        `**Category:** ${channel1.parent?.name || 'None'}`,
        `**Position:** ${channel1.position}`,
        `**Created:** <t:${Math.floor(channel1.createdTimestamp / 1000)}:R>`
      ].join('\n'), inline: true },
      { name: `📝 ${channel2.name}`, value: [
        `**Type:** ${getChannelTypeString(channel2.type)}`,
        `**Category:** ${channel2.parent?.name || 'None'}`,
        `**Position:** ${channel2.position}`,
        `**Created:** <t:${Math.floor(channel2.createdTimestamp / 1000)}:R>`
      ].join('\n'), inline: true },
      { name: '📊 Comparison Summary', value: [
        `**Same Type:** ${channel1.type === channel2.type ? '✅' : '❌'}`,
        `**Same Category:** ${channel1.parent?.id === channel2.parent?.id ? '✅' : '❌'}`,
        `**Age Difference:** ${Math.abs(channel1.createdTimestamp - channel2.createdTimestamp) / (1000 * 60 * 60 * 24)} days`,
        `**Position Difference:** ${Math.abs(channel1.position - channel2.position)} positions`
      ].join('\n'), inline: false }
    )
    .setTimestamp()
    .setFooter({ text: 'Channel Comparison System' });

  return await interaction.reply({ embeds: [embed] });
}

// 🛠️ UTILITY FUNCTIONS
function createErrorEmbed(message) {
  return new EmbedBuilder()
    .setColor(COLORS.ERROR)
    .setTitle(`${EMOJIS.ERROR} Error`)
    .setDescription(message)
    .setTimestamp()
    .setFooter({ text: 'Enterprise Channel Management System' });
}

function getChannelTypeString(type) {
  const types = {
    [ChannelType.GuildText]: '📝 Text Channel',
    [ChannelType.GuildVoice]: '🎵 Voice Channel',
    [ChannelType.GuildCategory]: '📁 Category',
    [ChannelType.GuildAnnouncement]: '📢 Announcement Channel',
    [ChannelType.GuildForum]: '💬 Forum Channel',
    [ChannelType.GuildStageVoice]: '🎭 Stage Channel'
  };
  return types[type] || '❓ Unknown Channel';
}