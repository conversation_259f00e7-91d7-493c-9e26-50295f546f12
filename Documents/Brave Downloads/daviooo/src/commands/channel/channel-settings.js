const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder, ChannelType } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('channel-settings')
    .setDescription('🔧 Configure channel settings')
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels)
    .addSubcommand(subcommand =>
      subcommand
        .setName('slowmode')
        .setDescription('Set slowmode for a channel')
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Channel to modify')
            .addChannelTypes(ChannelType.GuildText)
            .setRequired(true))
        .addIntegerOption(option =>
          option.setName('seconds')
            .setDescription('Slowmode duration in seconds (0 to disable)')
            .setMinValue(0)
            .setMaxValue(21600)
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('topic')
        .setDescription('Set channel topic')
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Channel to modify')
            .addChannelTypes(ChannelType.GuildText)
            .setRequired(true))
        .addStringOption(option =>
          option.setName('topic')
            .setDescription('New channel topic')
            .setMaxLength(1024)
            .setRequired(true)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('name')
        .setDescription('Rename a channel')
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Channel to rename')
            .setRequired(true))
        .addStringOption(option =>
          option.setName('name')
            .setDescription('New channel name')
            .setMaxLength(100)
            .setRequired(true))),

  async execute(interaction) {
    const subcommand = interaction.options.getSubcommand();
    const channel = interaction.options.getChannel('channel');

    // Check permissions
    if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
      return await interaction.reply({
        content: '❌ You need **Manage Channels** permission to use this command!',
        ephemeral: true
      });
    }

    try {
      switch (subcommand) {
        case 'slowmode': {
          const seconds = interaction.options.getInteger('seconds');

          await channel.setRateLimitPerUser(seconds);

          const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('✅ Slowmode Updated')
            .setDescription(seconds === 0
              ? `Slowmode disabled for ${channel}`
              : `Slowmode set to **${seconds} seconds** for ${channel}`)
            .setTimestamp();

          await interaction.reply({ embeds: [embed] });
          break;
        }

        case 'topic': {
          const topic = interaction.options.getString('topic');

          await channel.setTopic(topic);

          const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('✅ Channel Topic Updated')
            .setDescription(`Topic for ${channel} updated to:\n\`\`\`${topic}\`\`\``)
            .setTimestamp();

          await interaction.reply({ embeds: [embed] });
          break;
        }

        case 'name': {
          const name = interaction.options.getString('name');
          const oldName = channel.name;

          await channel.setName(name);

          const embed = new EmbedBuilder()
            .setColor(0x00FF00)
            .setTitle('✅ Channel Renamed')
            .setDescription(`Channel **${oldName}** renamed to **${name}**`)
            .setTimestamp();

          await interaction.reply({ embeds: [embed] });
          break;
        }
      }
    } catch (error) {
      console.error('Channel settings error:', error);

      const embed = new EmbedBuilder()
        .setColor(0xFF0000)
        .setTitle('❌ Error')
        .setDescription('Failed to update channel settings. Please check my permissions.')
        .setTimestamp();

      await interaction.reply({ embeds: [embed], ephemeral: true });
    }
  }
};