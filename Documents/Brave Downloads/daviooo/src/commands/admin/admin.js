// 🔧 PROFESSIONAL ADMIN DASHBOARD - ACADEMIC EXCELLENCE
// Comprehensive bot administration with monitoring and management tools

const { <PERSON><PERSON><PERSON>ommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const logger = require('../../utils/logger');
const errorHandler = require('../../utils/errorHandler');
const performanceMonitor = require('../../utils/performanceMonitor');
const databaseManager = require('../../database/connection');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('admin')
    .setDescription('🔧 Advanced bot administration and monitoring dashboard')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
    .addSubcommandGroup(group =>
      group
        .setName('system')
        .setDescription('System monitoring and management')
        .addSubcommand(subcommand =>
          subcommand
            .setName('status')
            .setDescription('📊 View comprehensive system status and metrics')
        )
        .addSubcommand(subcommand =>
          subcommand
            .setName('performance')
            .setDescription('⚡ View detailed performance metrics and statistics')
        )
        .addSubcommand(subcommand =>
          subcommand
            .setName('errors')
            .setDescription('🚨 View error logs and statistics')
        )
        .addSubcommand(subcommand =>
          subcommand
            .setName('database')
            .setDescription('🗄️ Database health check and statistics')
        )
    )
    .addSubcommandGroup(group =>
      group
        .setName('maintenance')
        .setDescription('Bot maintenance and cleanup operations')
        .addSubcommand(subcommand =>
          subcommand
            .setName('cleanup')
            .setDescription('🧹 Perform database cleanup and maintenance')
        )
        .addSubcommand(subcommand =>
          subcommand
            .setName('restart')
            .setDescription('🔄 Restart bot systems (use with caution)')
        )
        .addSubcommand(subcommand =>
          subcommand
            .setName('logs')
            .setDescription('📋 View recent system logs')
        )
    )
    .addSubcommandGroup(group =>
      group
        .setName('config')
        .setDescription('Bot configuration and settings')
        .addSubcommand(subcommand =>
          subcommand
            .setName('view')
            .setDescription('👁️ View current bot configuration')
        )
        .addSubcommand(subcommand =>
          subcommand
            .setName('update')
            .setDescription('⚙️ Update bot configuration settings')
            .addStringOption(option =>
              option
                .setName('setting')
                .setDescription('Configuration setting to update')
                .setRequired(true)
                .addChoices(
                  { name: 'Log Level', value: 'log_level' },
                  { name: 'Performance Monitoring', value: 'performance_monitoring' },
                  { name: 'Error Reporting', value: 'error_reporting' },
                  { name: 'Auto Maintenance', value: 'auto_maintenance' }
                )
            )
            .addStringOption(option =>
              option
                .setName('value')
                .setDescription('New value for the setting')
                .setRequired(true)
            )
        )
    ),

  async execute(interaction) {
    try {
      const subcommandGroup = interaction.options.getSubcommandGroup();
      const subcommand = interaction.options.getSubcommand();

      // Record command execution
      performanceMonitor.recordCommand(`admin-${subcommandGroup}-${subcommand}`);
      logger.command(interaction.user.username, `admin ${subcommandGroup} ${subcommand}`, interaction.guild?.name);

      switch (subcommandGroup) {
        case 'system':
          await this.handleSystemCommands(interaction, subcommand);
          break;
        case 'maintenance':
          await this.handleMaintenanceCommands(interaction, subcommand);
          break;
        case 'config':
          await this.handleConfigCommands(interaction, subcommand);
          break;
        default:
          throw new Error('Unknown subcommand group');
      }

    } catch (error) {
      const errorResponse = errorHandler.handleCommandError(error, interaction, 'admin');
      await interaction.reply({ embeds: [errorResponse], ephemeral: true });
    }
  },

  async handleSystemCommands(interaction, subcommand) {
    switch (subcommand) {
      case 'status':
        await this.showSystemStatus(interaction);
        break;
      case 'performance':
        await this.showPerformanceMetrics(interaction);
        break;
      case 'errors':
        await this.showErrorStats(interaction);
        break;
      case 'database':
        await this.showDatabaseStatus(interaction);
        break;
    }
  },

  async handleMaintenanceCommands(interaction, subcommand) {
    switch (subcommand) {
      case 'cleanup':
        await this.performCleanup(interaction);
        break;
      case 'restart':
        await this.restartSystems(interaction);
        break;
      case 'logs':
        await this.showRecentLogs(interaction);
        break;
    }
  },

  async handleConfigCommands(interaction, subcommand) {
    switch (subcommand) {
      case 'view':
        await this.showConfiguration(interaction);
        break;
      case 'update':
        await this.updateConfiguration(interaction);
        break;
    }
  },

  async showSystemStatus(interaction) {
    const metrics = performanceMonitor.getMetrics();
    const dbHealth = await databaseManager.healthCheck();
    const errorStats = errorHandler.getErrorStats();

    const statusEmbed = new EmbedBuilder()
      .setTitle('🔧 System Status Dashboard')
      .setColor(dbHealth.healthy ? 0x00ff00 : 0xff0000)
      .addFields(
        {
          name: '🤖 Bot Status',
          value: `\`\`\`
Status: Online ✅
Uptime: ${performanceMonitor.formatUptime(metrics.uptime)}
Guilds: ${interaction.client.guilds.cache.size}
Users: ${interaction.client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0)}
\`\`\``,
          inline: true
        },
        {
          name: '💾 Memory Usage',
          value: `\`\`\`
Used: ${performanceMonitor.formatBytes(metrics.memory.used)}
Total: ${performanceMonitor.formatBytes(metrics.memory.total)}
Usage: ${metrics.memory.percentage.toFixed(1)}%
\`\`\``,
          inline: true
        },
        {
          name: '🗄️ Database',
          value: `\`\`\`
Status: ${dbHealth.healthy ? 'Connected ✅' : 'Error ❌'}
Host: ${dbHealth.stats?.host || 'Unknown'}
Collections: ${dbHealth.stats?.collections?.length || 0}
\`\`\``,
          inline: true
        },
        {
          name: '⚡ Performance',
          value: `\`\`\`
CPU: ${metrics.cpu.usage.toFixed(1)}%
Commands/min: ${metrics.commands.perMinute}
Total Commands: ${metrics.commands.total}
\`\`\``,
          inline: true
        },
        {
          name: '🚨 Errors',
          value: `\`\`\`
Total: ${errorStats.totalErrors}
Per Hour: ${metrics.errors.perHour}
Critical: ${errorStats.criticalErrors}
\`\`\``,
          inline: true
        },
        {
          name: '🖥️ System Info',
          value: `\`\`\`
Platform: ${metrics.system.platform}
Node.js: ${metrics.system.nodeVersion}
CPU Cores: ${metrics.system.cpuCount}
\`\`\``,
          inline: true
        }
      )
      .setTimestamp()
      .setFooter({ text: 'DAVIO Admin Dashboard' });

    const actionRow = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('refresh_status')
          .setLabel('🔄 Refresh')
          .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
          .setCustomId('detailed_report')
          .setLabel('📊 Detailed Report')
          .setStyle(ButtonStyle.Secondary)
      );

    await interaction.reply({ embeds: [statusEmbed], components: [actionRow], ephemeral: true });
  },

  async showPerformanceMetrics(interaction) {
    const report = performanceMonitor.getReport();

    const performanceEmbed = new EmbedBuilder()
      .setTitle('⚡ Performance Metrics')
      .setColor(report.summary.status === 'healthy' ? 0x00ff00 : 0xffaa00)
      .setDescription(`**Overall Status:** ${report.summary.status === 'healthy' ? '✅ Healthy' : '⚠️ Warning'}`)
      .addFields(
        {
          name: '📊 Current Metrics',
          value: `\`\`\`
Memory Usage: ${report.summary.memoryUsage}
CPU Usage: ${report.summary.cpuUsage}
Commands/min: ${report.summary.commandsPerMinute}
Errors/hour: ${report.summary.errorsPerHour}
\`\`\``,
          inline: false
        },
        {
          name: '⚠️ Active Alerts',
          value: report.alerts.length > 0 ? 
            report.alerts.map(alert => `• ${alert}`).join('\n') : 
            '✅ No active alerts',
          inline: false
        }
      )
      .setTimestamp()
      .setFooter({ text: 'Performance data updated every 30 seconds' });

    await interaction.reply({ embeds: [performanceEmbed], ephemeral: true });
  },

  async showErrorStats(interaction) {
    const errorStats = errorHandler.getErrorStats();

    const errorEmbed = new EmbedBuilder()
      .setTitle('🚨 Error Statistics')
      .setColor(errorStats.totalErrors > 0 ? 0xff0000 : 0x00ff00)
      .addFields(
        {
          name: '📈 Error Summary',
          value: `\`\`\`
Total Errors: ${errorStats.totalErrors}
Unique Types: ${errorStats.uniqueErrors}
Critical Errors: ${errorStats.criticalErrors}
\`\`\``,
          inline: false
        },
        {
          name: '🔝 Top Error Types',
          value: errorStats.topErrors.length > 0 ?
            errorStats.topErrors.slice(0, 5).map(err => `• ${err.type}: ${err.count}`).join('\n') :
            '✅ No errors recorded',
          inline: false
        }
      )
      .setTimestamp()
      .setFooter({ text: 'Error tracking helps improve bot stability' });

    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
  },

  async showDatabaseStatus(interaction) {
    const dbHealth = await databaseManager.healthCheck();
    const dbStats = databaseManager.getStats();

    const dbEmbed = new EmbedBuilder()
      .setTitle('🗄️ Database Status')
      .setColor(dbHealth.healthy ? 0x00ff00 : 0xff0000)
      .addFields(
        {
          name: '🔗 Connection Info',
          value: `\`\`\`
Status: ${dbHealth.healthy ? 'Connected ✅' : 'Error ❌'}
Database: ${dbStats.database || 'Unknown'}
Host: ${dbStats.host}:${dbStats.port}
\`\`\``,
          inline: false
        },
        {
          name: '📊 Statistics',
          value: `\`\`\`
Total Queries: ${dbStats.totalQueries}
Failed Queries: ${dbStats.failedQueries}
Collections: ${dbStats.collections.length}
Connection Time: ${dbStats.connectionTime}ms
\`\`\``,
          inline: false
        },
        {
          name: '📁 Collections',
          value: dbStats.collections.length > 0 ?
            dbStats.collections.map(col => `• ${col}`).join('\n') :
            'No collections found',
          inline: false
        }
      )
      .setTimestamp()
      .setFooter({ text: 'Database health is monitored continuously' });

    await interaction.reply({ embeds: [dbEmbed], ephemeral: true });
  },

  async performCleanup(interaction) {
    await interaction.deferReply({ ephemeral: true });

    try {
      await databaseManager.performMaintenance();
      
      const cleanupEmbed = new EmbedBuilder()
        .setTitle('🧹 Maintenance Complete')
        .setColor(0x00ff00)
        .setDescription('Database cleanup and maintenance operations completed successfully.')
        .addFields(
          {
            name: '✅ Completed Tasks',
            value: '• Old giveaway cleanup\n• Database optimization\n• Cache refresh\n• Log rotation',
            inline: false
          }
        )
        .setTimestamp();

      await interaction.editReply({ embeds: [cleanupEmbed] });
      
    } catch (error) {
      const errorResponse = errorHandler.handleCommandError(error, interaction, 'admin-cleanup');
      await interaction.editReply({ embeds: [errorResponse] });
    }
  }
};
