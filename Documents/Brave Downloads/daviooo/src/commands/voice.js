// 🎵 VOICE COMMANDS - PROFESSIONAL VOICE CHANNEL MANAGEMENT
// Advanced voice channel control and audio features

const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder, ChannelType, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

// 🎨 PROFESSIONAL COLORS & EMOJIS
const COLORS = {
  SUCCESS: '#00D166',
  ERROR: '#FF3838',
  WARNING: '#FFB02E',
  INFO: '#00B4D8',
  VOICE: '#7209B7'
};

const EMOJIS = {
  VOICE: '🎵',
  MUTE: '🔇',
  UNMUTE: '🔊',
  DEAFEN: '🔕',
  UNDEAFEN: '🔉',
  MOVE: '🔄',
  DISCONNECT: '📞',
  LOCK: '🔒',
  UNLOCK: '🔓',
  SETTINGS: '⚙️',
  CROWN: '👑',
  USERS: '👥',
  STATS: '📊',
  MUSIC: '🎶',
  MICROPHONE: '🎤',
  SPEAKER: '🔈'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('voice')
    .setDescription('🎵 Professional Voice Channel Management System')
    .setDefaultMemberPermissions(PermissionFlagsBits.MoveMembers)
    
    // 🎤 MEMBER CONTROL GROUP
    .addSubcommandGroup(group =>
      group.setName('member')
        .setDescription('🎤 Control voice channel members')
        .addSubcommand(subcommand =>
          subcommand.setName('mute')
            .setDescription('🔇 Mute a user in voice channel')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to mute')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for muting')
                .setMaxLength(500)))
        .addSubcommand(subcommand =>
          subcommand.setName('unmute')
            .setDescription('🔊 Unmute a user in voice channel')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to unmute')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('deafen')
            .setDescription('🔕 Deafen a user in voice channel')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to deafen')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for deafening')
                .setMaxLength(500)))
        .addSubcommand(subcommand =>
          subcommand.setName('undeafen')
            .setDescription('🔉 Undeafen a user in voice channel')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to undeafen')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('move')
            .setDescription('🔄 Move user to another voice channel')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to move')
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel to move to')
                .addChannelTypes(ChannelType.GuildVoice)
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('disconnect')
            .setDescription('📞 Disconnect user from voice channel')
            .addUserOption(option =>
              option.setName('user')
                .setDescription('User to disconnect')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('reason')
                .setDescription('Reason for disconnecting')
                .setMaxLength(500))))
    
    // 📢 CHANNEL CONTROL GROUP
    .addSubcommandGroup(group =>
      group.setName('channel')
        .setDescription('📢 Control voice channels')
        .addSubcommand(subcommand =>
          subcommand.setName('lock')
            .setDescription('🔒 Lock a voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel to lock (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('unlock')
            .setDescription('🔓 Unlock a voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel to unlock (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('limit')
            .setDescription('👥 Set user limit for voice channel')
            .addIntegerOption(option =>
              option.setName('limit')
                .setDescription('User limit (0 = unlimited)')
                .setMinValue(0)
                .setMaxValue(99)
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel to modify (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('rename')
            .setDescription('✏️ Rename a voice channel')
            .addStringOption(option =>
              option.setName('name')
                .setDescription('New channel name')
                .setRequired(true)
                .setMaxLength(100))
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel to rename (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('create')
            .setDescription('➕ Create a new voice channel')
            .addStringOption(option =>
              option.setName('name')
                .setDescription('Channel name')
                .setRequired(true)
                .setMaxLength(100))
            .addIntegerOption(option =>
              option.setName('limit')
                .setDescription('User limit (0 = unlimited)')
                .setMinValue(0)
                .setMaxValue(99))
            .addChannelOption(option =>
              option.setName('category')
                .setDescription('Category to create channel in')
                .addChannelTypes(ChannelType.GuildCategory)))
        .addSubcommand(subcommand =>
          subcommand.setName('delete')
            .setDescription('🗑️ Delete a voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel to delete')
                .addChannelTypes(ChannelType.GuildVoice)
                .setRequired(true))))
    
    // 👥 MASS ACTIONS GROUP
    .addSubcommandGroup(group =>
      group.setName('mass')
        .setDescription('👥 Mass voice actions')
        .addSubcommand(subcommand =>
          subcommand.setName('mute-all')
            .setDescription('🔇 Mute all users in voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('unmute-all')
            .setDescription('🔊 Unmute all users in voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('deafen-all')
            .setDescription('🔕 Deafen all users in voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('undeafen-all')
            .setDescription('🔉 Undeafen all users in voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('move-all')
            .setDescription('🔄 Move all users to another voice channel')
            .addChannelOption(option =>
              option.setName('to')
                .setDescription('Destination voice channel')
                .addChannelTypes(ChannelType.GuildVoice)
                .setRequired(true))
            .addChannelOption(option =>
              option.setName('from')
                .setDescription('Source voice channel (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice)))
        .addSubcommand(subcommand =>
          subcommand.setName('disconnect-all')
            .setDescription('📞 Disconnect all users from voice channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Voice channel (current if not specified)')
                .addChannelTypes(ChannelType.GuildVoice))))
    
    // 📊 INFO & STATS
    .addSubcommand(subcommand =>
      subcommand.setName('info')
        .setDescription('📊 Get voice channel information')
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Voice channel to get info about (current if not specified)')
            .addChannelTypes(ChannelType.GuildVoice)))
    .addSubcommand(subcommand =>
      subcommand.setName('list')
        .setDescription('📋 List all voice channels and their status')),

  async execute(interaction) {
    try {
      const subcommandGroup = interaction.options.getSubcommandGroup();
      const subcommand = interaction.options.getSubcommand();

      // Route to appropriate handler
      if (subcommandGroup === 'member') {
        return await handleMemberActions(interaction, subcommand);
      } else if (subcommandGroup === 'channel') {
        return await handleChannelActions(interaction, subcommand);
      } else if (subcommandGroup === 'mass') {
        return await handleMassActions(interaction, subcommand);
      } else if (subcommand === 'info') {
        return await handleInfo(interaction);
      } else if (subcommand === 'list') {
        return await handleList(interaction);
      }

    } catch (error) {
      console.error('Voice Command Error:', error);
      return interaction.reply({
        content: `${EMOJIS.ERROR} **Error:** An unexpected error occurred while processing your request.`,
        ephemeral: true
      });
    }
  }
};

// 🎤 MEMBER ACTIONS HANDLER
async function handleMemberActions(interaction, action) {
  const targetUser = interaction.options.getUser('user');
  const reason = interaction.options.getString('reason') || 'No reason provided';
  const channel = interaction.options.getChannel('channel');

  // Check permissions
  if (!interaction.member.permissions.has(PermissionFlagsBits.MuteMembers)) {
    return interaction.reply({
      embeds: [createErrorEmbed('You need **Mute Members** permission to use this command!')],
      ephemeral: true
    });
  }

  const targetMember = await interaction.guild.members.fetch(targetUser.id).catch(() => null);
  if (!targetMember) {
    return interaction.reply({
      embeds: [createErrorEmbed('User not found in this server!')],
      ephemeral: true
    });
  }

  // Check if user is in voice channel (except for move)
  if (action !== 'move' && !targetMember.voice.channel) {
    return interaction.reply({
      embeds: [createErrorEmbed(`${targetUser.tag} is not in a voice channel!`)],
      ephemeral: true
    });
  }

  try {
    let result;
    switch (action) {
      case 'mute':
        await targetMember.voice.setMute(true, reason);
        result = `${EMOJIS.MUTE} Successfully muted **${targetUser.tag}** in voice channel`;
        break;

      case 'unmute':
        await targetMember.voice.setMute(false, reason);
        result = `${EMOJIS.UNMUTE} Successfully unmuted **${targetUser.tag}** in voice channel`;
        break;

      case 'deafen':
        await targetMember.voice.setDeaf(true, reason);
        result = `${EMOJIS.DEAFEN} Successfully deafened **${targetUser.tag}** in voice channel`;
        break;

      case 'undeafen':
        await targetMember.voice.setDeaf(false, reason);
        result = `${EMOJIS.UNDEAFEN} Successfully undeafened **${targetUser.tag}** in voice channel`;
        break;

      case 'move':
        if (!targetMember.voice.channel) {
          return interaction.reply({
            embeds: [createErrorEmbed(`${targetUser.tag} is not in a voice channel!`)],
            ephemeral: true
          });
        }
        await targetMember.voice.setChannel(channel, reason);
        result = `${EMOJIS.MOVE} Successfully moved **${targetUser.tag}** to **${channel.name}**`;
        break;

      case 'disconnect':
        await targetMember.voice.disconnect(reason);
        result = `${EMOJIS.DISCONNECT} Successfully disconnected **${targetUser.tag}** from voice channel`;
        break;
    }

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.VOICE} Voice Action Completed`)
      .setDescription(result)
      .addFields(
        { name: 'Target', value: `${targetUser.tag}`, inline: true },
        { name: 'Action', value: action.charAt(0).toUpperCase() + action.slice(1), inline: true },
        { name: 'Reason', value: reason, inline: true }
      )
      .setTimestamp()
      .setFooter({ text: `Executed by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });

    return interaction.reply({ embeds: [embed] });

  } catch (error) {
    console.error('Member Action Error:', error);
    return interaction.reply({
      embeds: [createErrorEmbed('Failed to execute voice action. Check bot permissions!')],
      ephemeral: true
    });
  }
}

// 📢 CHANNEL ACTIONS HANDLER
async function handleChannelActions(interaction, action) {
  let targetChannel = interaction.options.getChannel('channel');

  // Use current voice channel if not specified
  if (!targetChannel && interaction.member.voice.channel) {
    targetChannel = interaction.member.voice.channel;
  }

  // Check permissions
  if (!interaction.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
    return interaction.reply({
      embeds: [createErrorEmbed('You need **Manage Channels** permission to use this command!')],
      ephemeral: true
    });
  }

  try {
    let result;
    switch (action) {
      case 'lock':
        if (!targetChannel) {
          return interaction.reply({
            embeds: [createErrorEmbed('Please specify a voice channel or join one!')],
            ephemeral: true
          });
        }
        await targetChannel.permissionOverwrites.edit(interaction.guild.roles.everyone, {
          Connect: false
        });
        result = `${EMOJIS.LOCK} Successfully locked **${targetChannel.name}**`;
        break;

      case 'unlock':
        if (!targetChannel) {
          return interaction.reply({
            embeds: [createErrorEmbed('Please specify a voice channel or join one!')],
            ephemeral: true
          });
        }
        await targetChannel.permissionOverwrites.edit(interaction.guild.roles.everyone, {
          Connect: null
        });
        result = `${EMOJIS.UNLOCK} Successfully unlocked **${targetChannel.name}**`;
        break;

      case 'limit':
        if (!targetChannel) {
          return interaction.reply({
            embeds: [createErrorEmbed('Please specify a voice channel or join one!')],
            ephemeral: true
          });
        }
        const limit = interaction.options.getInteger('limit');
        await targetChannel.setUserLimit(limit);
        result = `${EMOJIS.USERS} Set user limit for **${targetChannel.name}** to **${limit === 0 ? 'Unlimited' : limit}**`;
        break;

      case 'rename':
        if (!targetChannel) {
          return interaction.reply({
            embeds: [createErrorEmbed('Please specify a voice channel or join one!')],
            ephemeral: true
          });
        }
        const newName = interaction.options.getString('name');
        const oldName = targetChannel.name;
        await targetChannel.setName(newName);
        result = `✏️ Renamed voice channel from **${oldName}** to **${newName}**`;
        break;

      case 'create':
        const channelName = interaction.options.getString('name');
        const userLimit = interaction.options.getInteger('limit') || 0;
        const category = interaction.options.getChannel('category');

        const newChannel = await interaction.guild.channels.create({
          name: channelName,
          type: ChannelType.GuildVoice,
          userLimit: userLimit,
          parent: category?.id
        });

        result = `➕ Successfully created voice channel **${newChannel.name}**`;
        if (userLimit > 0) result += ` with limit of **${userLimit}** users`;
        if (category) result += ` in category **${category.name}**`;
        break;

      case 'delete':
        const channelToDelete = interaction.options.getChannel('channel');
        if (!channelToDelete) {
          return interaction.reply({
            embeds: [createErrorEmbed('Please specify a voice channel to delete!')],
            ephemeral: true
          });
        }

        const deletedName = channelToDelete.name;
        await channelToDelete.delete();
        result = `🗑️ Successfully deleted voice channel **${deletedName}**`;
        break;
    }

    const embed = new EmbedBuilder()
      .setColor(COLORS.SUCCESS)
      .setTitle(`${EMOJIS.SETTINGS} Channel Action Completed`)
      .setDescription(result)
      .setTimestamp()
      .setFooter({ text: `Executed by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });

    return interaction.reply({ embeds: [embed] });

  } catch (error) {
    console.error('Channel Action Error:', error);
    return interaction.reply({
      embeds: [createErrorEmbed('Failed to execute channel action. Check bot permissions!')],
      ephemeral: true
    });
  }
}

// 👥 MASS ACTIONS HANDLER
async function handleMassActions(interaction, action) {
  let targetChannel = interaction.options.getChannel('channel');
  const fromChannel = interaction.options.getChannel('from');
  const toChannel = interaction.options.getChannel('to');

  // Use current voice channel if not specified
  if (!targetChannel && interaction.member.voice.channel) {
    targetChannel = interaction.member.voice.channel;
  }

  if (action === 'move-all') {
    targetChannel = fromChannel || interaction.member.voice.channel;
  }

  // Check permissions
  if (!interaction.member.permissions.has(PermissionFlagsBits.MuteMembers)) {
    return interaction.reply({
      embeds: [createErrorEmbed('You need **Mute Members** permission to use mass actions!')],
      ephemeral: true
    });
  }

  if (!targetChannel) {
    return interaction.reply({
      embeds: [createErrorEmbed('Please specify a voice channel or join one!')],
      ephemeral: true
    });
  }

  const members = targetChannel.members;
  if (members.size === 0) {
    return interaction.reply({
      embeds: [createErrorEmbed(`No users found in **${targetChannel.name}**!`)],
      ephemeral: true
    });
  }

  await interaction.deferReply();

  try {
    let successCount = 0;
    let failCount = 0;
    let result;

    for (const [memberId, member] of members) {
      try {
        switch (action) {
          case 'mute-all':
            await member.voice.setMute(true, 'Mass mute action');
            break;
          case 'unmute-all':
            await member.voice.setMute(false, 'Mass unmute action');
            break;
          case 'deafen-all':
            await member.voice.setDeaf(true, 'Mass deafen action');
            break;
          case 'undeafen-all':
            await member.voice.setDeaf(false, 'Mass undeafen action');
            break;
          case 'move-all':
            await member.voice.setChannel(toChannel, 'Mass move action');
            break;
          case 'disconnect-all':
            await member.voice.disconnect('Mass disconnect action');
            break;
        }
        successCount++;
      } catch (error) {
        failCount++;
        console.error(`Failed to ${action} ${member.user.tag}:`, error);
      }
    }

    const actionName = action.replace('-all', '').replace('-', ' ');
    result = `${getActionEmoji(action)} Successfully ${actionName}ed **${successCount}** users`;
    if (failCount > 0) result += ` (${failCount} failed)`;
    if (action === 'move-all') result += ` to **${toChannel.name}**`;

    const embed = new EmbedBuilder()
      .setColor(successCount > failCount ? COLORS.SUCCESS : COLORS.WARNING)
      .setTitle(`${EMOJIS.USERS} Mass Action Completed`)
      .setDescription(result)
      .addFields(
        { name: 'Channel', value: targetChannel.name, inline: true },
        { name: 'Action', value: actionName.charAt(0).toUpperCase() + actionName.slice(1), inline: true },
        { name: 'Results', value: `✅ ${successCount} | ❌ ${failCount}`, inline: true }
      )
      .setTimestamp()
      .setFooter({ text: `Executed by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL() });

    return interaction.editReply({ embeds: [embed] });

  } catch (error) {
    console.error('Mass Action Error:', error);
    return interaction.editReply({
      embeds: [createErrorEmbed('Failed to execute mass action. Check bot permissions!')],
    });
  }
}

// 📊 INFO HANDLER
async function handleInfo(interaction) {
  let targetChannel = interaction.options.getChannel('channel');

  // Use current voice channel if not specified
  if (!targetChannel && interaction.member.voice.channel) {
    targetChannel = interaction.member.voice.channel;
  }

  if (!targetChannel) {
    return interaction.reply({
      embeds: [createErrorEmbed('Please specify a voice channel or join one!')],
      ephemeral: true
    });
  }

  const members = targetChannel.members;
  const memberList = members.size > 0
    ? members.map(member => `${member.voice.mute ? '🔇' : '🎤'} ${member.voice.deaf ? '🔕' : '🔉'} ${member.displayName}`).join('\n')
    : 'No users in channel';

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.VOICE} Voice Channel Information`)
    .setDescription(`**${targetChannel.name}**`)
    .addFields(
      { name: '👥 Users', value: `${members.size}${targetChannel.userLimit > 0 ? `/${targetChannel.userLimit}` : ''}`, inline: true },
      { name: '🔒 Status', value: targetChannel.permissionsFor(interaction.guild.roles.everyone).has(PermissionFlagsBits.Connect) ? 'Unlocked' : 'Locked', inline: true },
      { name: '📍 Category', value: targetChannel.parent?.name || 'None', inline: true },
      { name: '🎤 Members', value: memberList.length > 1024 ? `${memberList.substring(0, 1020)}...` : memberList }
    )
    .setTimestamp()
    .setFooter({ text: `Channel ID: ${targetChannel.id}` });

  return interaction.reply({ embeds: [embed] });
}

// 📋 LIST HANDLER
async function handleList(interaction) {
  const voiceChannels = interaction.guild.channels.cache.filter(channel => channel.type === ChannelType.GuildVoice);

  if (voiceChannels.size === 0) {
    return interaction.reply({
      embeds: [createErrorEmbed('No voice channels found in this server!')],
      ephemeral: true
    });
  }

  const channelList = voiceChannels.map(channel => {
    const memberCount = channel.members.size;
    const limit = channel.userLimit > 0 ? `/${channel.userLimit}` : '';
    const status = channel.permissionsFor(interaction.guild.roles.everyone).has(PermissionFlagsBits.Connect) ? '🔓' : '🔒';
    const category = channel.parent ? `[${channel.parent.name}] ` : '';

    return `${status} ${category}**${channel.name}** - ${memberCount}${limit} users`;
  }).join('\n');

  const embed = new EmbedBuilder()
    .setColor(COLORS.INFO)
    .setTitle(`${EMOJIS.VOICE} Voice Channels Overview`)
    .setDescription(channelList.length > 4096 ? `${channelList.substring(0, 4090)}...` : channelList)
    .addFields(
      { name: '📊 Statistics', value: `Total Channels: ${voiceChannels.size}\nTotal Users: ${voiceChannels.reduce((acc, ch) => acc + ch.members.size, 0)}`, inline: true }
    )
    .setTimestamp()
    .setFooter({ text: `🔓 = Unlocked | 🔒 = Locked` });

  return interaction.reply({ embeds: [embed] });
}

// 🛠️ UTILITY FUNCTIONS
function createErrorEmbed(message) {
  return new EmbedBuilder()
    .setColor(COLORS.ERROR)
    .setTitle(`${EMOJIS.ERROR} Error`)
    .setDescription(message)
    .setTimestamp();
}

function getActionEmoji(action) {
  const emojiMap = {
    'mute-all': EMOJIS.MUTE,
    'unmute-all': EMOJIS.UNMUTE,
    'deafen-all': EMOJIS.DEAFEN,
    'undeafen-all': EMOJIS.UNDEAFEN,
    'move-all': EMOJIS.MOVE,
    'disconnect-all': EMOJIS.DISCONNECT
  };
  return emojiMap[action] || EMOJIS.VOICE;
}
