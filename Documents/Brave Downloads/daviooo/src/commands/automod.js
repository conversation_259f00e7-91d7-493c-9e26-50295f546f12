// 🛡️ ENTERPRISE-GRADE AUTOMOD SYSTEM - PROFESSIONAL SECURITY FRAMEWORK
// Advanced Discord server protection with intelligent filtering and raid protection

const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const AutoMod = require('../database/models/automod');

// 🎨 PROFESSIONAL COLORS & EMOJIS
const COLORS = {
  SUCCESS: '#00D166',
  ERROR: '#FF3838',
  WARNING: '#FFB02E',
  INFO: '#00B4D8',
  SECURITY: '#7209B7'
};

const EMOJIS = {
  SHIELD: '🛡️',
  CHECK: '✅',
  CROSS: '❌',
  WARNING: '⚠️',
  SETTINGS: '⚙️',
  STATS: '📊',
  LOCK: '🔒',
  FILTER: '🚫',
  RADAR: '📡',
  FIRE: '🔥',
  LIGHTNING: '⚡',
  CROWN: '👑',
  HAMMER: '🔨',
  EYES: '👀'
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName('automod')
    .setDescription('🛡️ Enterprise-Grade Server Protection System')
    .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
    
    // 🚫 CONTENT FILTERS GROUP
    .addSubcommandGroup(group =>
      group.setName('filters')
        .setDescription('🚫 Content filtering and message protection')
        .addSubcommand(subcommand =>
          subcommand.setName('invites')
            .setDescription('🚫 Configure Discord invite link protection')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when invite detected')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ Delete Message', value: 'delete' },
                  { name: '⚠️ Warn User', value: 'warn' },
                  { name: '⏰ Timeout User', value: 'timeout' },
                  { name: '👢 Kick User', value: 'kick' },
                  { name: '🔨 Ban User', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('links')
            .setDescription('🔗 Configure external link protection')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when link detected')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ Delete Message', value: 'delete' },
                  { name: '⚠️ Warn User', value: 'warn' },
                  { name: '⏰ Timeout User', value: 'timeout' },
                  { name: '👢 Kick User', value: 'kick' },
                  { name: '🔨 Ban User', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('spam')
            .setDescription('📢 Configure spam protection')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when spam detected')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ Delete Messages', value: 'delete' },
                  { name: '⚠️ Warn User', value: 'warn' },
                  { name: '⏰ Timeout User', value: 'timeout' },
                  { name: '👢 Kick User', value: 'kick' },
                  { name: '🔨 Ban User', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                ))
            .addIntegerOption(option =>
              option.setName('limit')
                .setDescription('Messages per time window (default: 5)')
                .setMinValue(2)
                .setMaxValue(20))
            .addIntegerOption(option =>
              option.setName('window')
                .setDescription('Time window in seconds (default: 10)')
                .setMinValue(5)
                .setMaxValue(60)))
        .addSubcommand(subcommand =>
          subcommand.setName('caps')
            .setDescription('🔠 Configure excessive caps protection')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when caps detected')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ Delete Message', value: 'delete' },
                  { name: '⚠️ Warn User', value: 'warn' },
                  { name: '⏰ Timeout User', value: 'timeout' },
                  { name: '👢 Kick User', value: 'kick' },
                  { name: '🔨 Ban User', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                ))
            .addIntegerOption(option =>
              option.setName('threshold')
                .setDescription('Caps percentage threshold (default: 70%)')
                .setMinValue(50)
                .setMaxValue(95)))
        .addSubcommand(subcommand =>
          subcommand.setName('mentions')
            .setDescription('📢 Configure mention spam protection')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when mention spam detected')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ Delete Message', value: 'delete' },
                  { name: '⚠️ Warn User', value: 'warn' },
                  { name: '⏰ Timeout User', value: 'timeout' },
                  { name: '👢 Kick User', value: 'kick' },
                  { name: '🔨 Ban User', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                ))
            .addIntegerOption(option =>
              option.setName('limit')
                .setDescription('Max mentions per message (default: 5)')
                .setMinValue(2)
                .setMaxValue(15)))
        .addSubcommand(subcommand =>
          subcommand.setName('scam')
            .setDescription('🎣 Configure scam link protection')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when scam detected')
                .setRequired(true)
                .addChoices(
                  { name: '🗑️ Delete Message', value: 'delete' },
                  { name: '⚠️ Warn User', value: 'warn' },
                  { name: '⏰ Timeout User', value: 'timeout' },
                  { name: '👢 Kick User', value: 'kick' },
                  { name: '🔨 Ban User', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                ))))
    
    // 🔒 RAID PROTECTION GROUP
    .addSubcommandGroup(group =>
      group.setName('raid')
        .setDescription('🔒 Advanced raid protection and mass action detection')
        .addSubcommand(subcommand =>
          subcommand.setName('protection')
            .setDescription('🔒 Configure raid protection settings')
            .addStringOption(option =>
              option.setName('action')
                .setDescription('What to do when raid detected')
                .setRequired(true)
                .addChoices(
                  { name: '🔒 Server Lockdown', value: 'lockdown' },
                  { name: '👢 Kick Raiders', value: 'kick' },
                  { name: '🔨 Ban Raiders', value: 'ban' },
                  { name: '❌ Disable', value: 'disable' }
                ))
            .addIntegerOption(option =>
              option.setName('threshold')
                .setDescription('Join threshold for raid detection (default: 10)')
                .setMinValue(5)
                .setMaxValue(50))
            .addIntegerOption(option =>
              option.setName('window')
                .setDescription('Time window in seconds (default: 30)')
                .setMinValue(10)
                .setMaxValue(300))))
    
    // ⚙️ SETTINGS GROUP
    .addSubcommandGroup(group =>
      group.setName('settings')
        .setDescription('⚙️ Configure automod settings and permissions')
        .addSubcommand(subcommand =>
          subcommand.setName('logs')
            .setDescription('📝 Set automod log channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel for automod logs')
                .setRequired(true)))
        .addSubcommand(subcommand =>
          subcommand.setName('immune-role')
            .setDescription('👑 Add/remove immune role')
            .addRoleOption(option =>
              option.setName('role')
                .setDescription('Role to make immune to automod')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('action')
                .setDescription('Add or remove immunity')
                .setRequired(true)
                .addChoices(
                  { name: '➕ Add Immunity', value: 'add' },
                  { name: '➖ Remove Immunity', value: 'remove' }
                )))
        .addSubcommand(subcommand =>
          subcommand.setName('immune-channel')
            .setDescription('🔓 Add/remove immune channel')
            .addChannelOption(option =>
              option.setName('channel')
                .setDescription('Channel to make immune to automod')
                .setRequired(true))
            .addStringOption(option =>
              option.setName('action')
                .setDescription('Add or remove immunity')
                .setRequired(true)
                .addChoices(
                  { name: '➕ Add Immunity', value: 'add' },
                  { name: '➖ Remove Immunity', value: 'remove' }
                ))))
    
    // 📊 INFO & STATS
    .addSubcommand(subcommand =>
      subcommand.setName('status')
        .setDescription('📊 View automod status and statistics'))
    .addSubcommand(subcommand =>
      subcommand.setName('reset')
        .setDescription('🔄 Reset all automod settings')
        .addStringOption(option =>
          option.setName('confirm')
            .setDescription('Type "CONFIRM" to reset all settings')
            .setRequired(true))),

  async execute(interaction) {
    try {
      const subcommandGroup = interaction.options.getSubcommandGroup();
      const subcommand = interaction.options.getSubcommand();
      
      // Get or create automod settings
      let automod = await AutoMod.findOne({ guildId: interaction.guild.id });
      if (!automod) {
        automod = new AutoMod({ guildId: interaction.guild.id });
        await automod.save();
      }

      // Route to appropriate handler
      if (subcommandGroup === 'filters') {
        return await handleFilters(interaction, automod, subcommand);
      } else if (subcommandGroup === 'raid') {
        return await handleRaid(interaction, automod, subcommand);
      } else if (subcommandGroup === 'settings') {
        return await handleSettings(interaction, automod, subcommand);
      } else if (subcommand === 'status') {
        return await handleStatus(interaction, automod);
      } else if (subcommand === 'reset') {
        return await handleReset(interaction, automod);
      }

    } catch (error) {
      console.error('Automod Error:', error);
      return interaction.reply({
        content: `${EMOJIS.CROSS} **Error:** An unexpected error occurred while processing your request.`,
        ephemeral: true
      });
    }
  }
};

// 🚫 CONTENT FILTERS HANDLER
async function handleFilters(interaction, automod, filterType) {
  const action = interaction.options.getString('action');

  if (action === 'disable') {
    automod[getFilterKey(filterType)].enabled = false;
    await automod.save();

    return interaction.reply({
      embeds: [createSuccessEmbed(`${EMOJIS.CROSS} **${getFilterName(filterType)} Disabled**`,
        `${getFilterName(filterType)} protection has been disabled.`)],
      ephemeral: true
    });
  }

  // Enable filter with action
  const filterKey = getFilterKey(filterType);
  automod[filterKey].enabled = true;
  automod[filterKey].action = action;

  // Handle additional options
  if (filterType === 'spam') {
    const limit = interaction.options.getInteger('limit');
    const window = interaction.options.getInteger('window');
    if (limit) automod[filterKey].messageLimit = limit;
    if (window) automod[filterKey].timeWindow = window;
  } else if (filterType === 'caps') {
    const threshold = interaction.options.getInteger('threshold');
    if (threshold) automod[filterKey].threshold = threshold;
  } else if (filterType === 'mentions') {
    const limit = interaction.options.getInteger('limit');
    if (limit) automod[filterKey].limit = limit;
  }

  await automod.save();

  const embed = createSuccessEmbed(
    `${EMOJIS.SHIELD} **${getFilterName(filterType)} Configured**`,
    `${getFilterName(filterType)} protection is now **enabled** with **${getActionName(action)}** action.`
  );

  if (filterType === 'spam') {
    embed.addFields({
      name: `${EMOJIS.SETTINGS} Settings`,
      value: `• **Message Limit:** ${automod[filterKey].messageLimit}\n• **Time Window:** ${automod[filterKey].timeWindow}s`,
      inline: true
    });
  }

  return interaction.reply({ embeds: [embed], ephemeral: true });
}

// 🔒 RAID PROTECTION HANDLER
async function handleRaid(interaction, automod, subcommand) {
  if (subcommand === 'protection') {
    const action = interaction.options.getString('action');

    if (action === 'disable') {
      automod.raidProtection.enabled = false;
      await automod.save();

      return interaction.reply({
        embeds: [createSuccessEmbed(`${EMOJIS.CROSS} **Raid Protection Disabled**`,
          'Raid protection has been disabled.')],
        ephemeral: true
      });
    }

    automod.raidProtection.enabled = true;
    automod.raidProtection.action = action;

    const threshold = interaction.options.getInteger('threshold');
    const window = interaction.options.getInteger('window');
    if (threshold) automod.raidProtection.joinThreshold = threshold;
    if (window) automod.raidProtection.timeWindow = window;

    await automod.save();

    const embed = createSuccessEmbed(
      `${EMOJIS.LOCK} **Raid Protection Configured**`,
      `Raid protection is now **enabled** with **${getActionName(action)}** action.`
    );

    embed.addFields({
      name: `${EMOJIS.SETTINGS} Settings`,
      value: `• **Join Threshold:** ${automod.raidProtection.joinThreshold}\n• **Time Window:** ${automod.raidProtection.timeWindow}s`,
      inline: true
    });

    return interaction.reply({ embeds: [embed], ephemeral: true });
  }
}

// ⚙️ SETTINGS HANDLER
async function handleSettings(interaction, automod, setting) {
  if (setting === 'logs') {
    const channel = interaction.options.getChannel('channel');
    automod.logChannel = channel.id;
    await automod.save();

    return interaction.reply({
      embeds: [createSuccessEmbed(`${EMOJIS.SETTINGS} **Log Channel Set**`,
        `Automod logs will now be sent to ${channel}.`)],
      ephemeral: true
    });
  }

  if (setting === 'immune-role') {
    const role = interaction.options.getRole('role');
    const action = interaction.options.getString('action');

    if (action === 'add') {
      if (!automod.immuneRoles.includes(role.id)) {
        automod.immuneRoles.push(role.id);
        await automod.save();

        return interaction.reply({
          embeds: [createSuccessEmbed(`${EMOJIS.CROWN} **Role Immunity Added**`,
            `${role} is now immune to automod actions.`)],
          ephemeral: true
        });
      } else {
        return interaction.reply({
          content: `${EMOJIS.WARNING} **${role.name}** is already immune to automod.`,
          ephemeral: true
        });
      }
    } else {
      automod.immuneRoles = automod.immuneRoles.filter(id => id !== role.id);
      await automod.save();

      return interaction.reply({
        embeds: [createSuccessEmbed(`${EMOJIS.HAMMER} **Role Immunity Removed**`,
          `${role} is no longer immune to automod actions.`)],
        ephemeral: true
      });
    }
  }

  if (setting === 'immune-channel') {
    const channel = interaction.options.getChannel('channel');
    const action = interaction.options.getString('action');

    if (action === 'add') {
      if (!automod.immuneChannels.includes(channel.id)) {
        automod.immuneChannels.push(channel.id);
        await automod.save();

        return interaction.reply({
          embeds: [createSuccessEmbed(`${EMOJIS.CROWN} **Channel Immunity Added**`,
            `${channel} is now immune to automod actions.`)],
          ephemeral: true
        });
      } else {
        return interaction.reply({
          content: `${EMOJIS.WARNING} **${channel.name}** is already immune to automod.`,
          ephemeral: true
        });
      }
    } else {
      automod.immuneChannels = automod.immuneChannels.filter(id => id !== channel.id);
      await automod.save();

      return interaction.reply({
        embeds: [createSuccessEmbed(`${EMOJIS.HAMMER} **Channel Immunity Removed**`,
          `${channel} is no longer immune to automod actions.`)],
        ephemeral: true
      });
    }
  }
}

// 📊 STATUS HANDLER
async function handleStatus(interaction, automod) {
  const embed = new EmbedBuilder()
    .setTitle(`${EMOJIS.SHIELD} **Automod Status Dashboard**`)
    .setDescription(`${EMOJIS.RADAR} **Server Protection Overview**`)
    .setColor(COLORS.SECURITY)
    .setThumbnail(interaction.guild.iconURL())
    .setTimestamp();

  // Content Filters Status
  const filtersStatus = [
    `${getStatusIcon(automod.antiInvites.enabled)} **Invite Protection:** ${automod.antiInvites.enabled ? `${getActionName(automod.antiInvites.action)}` : 'Disabled'}`,
    `${getStatusIcon(automod.antiLinks.enabled)} **Link Protection:** ${automod.antiLinks.enabled ? `${getActionName(automod.antiLinks.action)}` : 'Disabled'}`,
    `${getStatusIcon(automod.antiSpam.enabled)} **Spam Protection:** ${automod.antiSpam.enabled ? `${getActionName(automod.antiSpam.action)}` : 'Disabled'}`,
    `${getStatusIcon(automod.antiCaps.enabled)} **Caps Protection:** ${automod.antiCaps.enabled ? `${getActionName(automod.antiCaps.action)}` : 'Disabled'}`,
    `${getStatusIcon(automod.mentionSpam.enabled)} **Mention Spam:** ${automod.mentionSpam.enabled ? `${getActionName(automod.mentionSpam.action)}` : 'Disabled'}`,
    `${getStatusIcon(automod.antiScam.enabled)} **Scam Protection:** ${automod.antiScam.enabled ? `${getActionName(automod.antiScam.action)}` : 'Disabled'}`
  ].join('\n');

  embed.addFields({
    name: `${EMOJIS.FILTER} **Content Filters**`,
    value: filtersStatus,
    inline: false
  });

  // Raid Protection Status
  embed.addFields({
    name: `${EMOJIS.LOCK} **Raid Protection**`,
    value: `${getStatusIcon(automod.raidProtection.enabled)} ${automod.raidProtection.enabled ?
      `**Enabled** - ${getActionName(automod.raidProtection.action)}\n• Threshold: ${automod.raidProtection.joinThreshold} joins\n• Window: ${automod.raidProtection.timeWindow}s` :
      '**Disabled**'}`,
    inline: true
  });

  // Statistics
  embed.addFields({
    name: `${EMOJIS.STATS} **Statistics**`,
    value: `• Messages Deleted: **${automod.stats.messagesDeleted}**\n• Users Warned: **${automod.stats.usersWarned}**\n• Users Timed Out: **${automod.stats.usersTimedOut}**\n• Users Kicked: **${automod.stats.usersKicked}**\n• Users Banned: **${automod.stats.usersBanned}**\n• Raids Blocked: **${automod.stats.raidsBlocked}**`,
    inline: true
  });

  // Settings
  const settingsValue = [
    `• **Log Channel:** ${automod.logChannel ? `<#${automod.logChannel}>` : 'Not Set'}`,
    `• **Immune Roles:** ${automod.immuneRoles.length}`,
    `• **Immune Channels:** ${automod.immuneChannels.length}`
  ].join('\n');

  embed.addFields({
    name: `${EMOJIS.SETTINGS} **Configuration**`,
    value: settingsValue,
    inline: false
  });

  return interaction.reply({ embeds: [embed], ephemeral: true });
}

// 🔄 RESET HANDLER
async function handleReset(interaction, automod) {
  const confirm = interaction.options.getString('confirm');

  if (confirm !== 'CONFIRM') {
    return interaction.reply({
      content: `${EMOJIS.WARNING} **Invalid confirmation.** Type \`CONFIRM\` to reset all automod settings.`,
      ephemeral: true
    });
  }

  // Reset all settings
  await AutoMod.findOneAndDelete({ guildId: interaction.guild.id });

  const embed = createSuccessEmbed(
    `${EMOJIS.CHECK} **Automod Reset Complete**`,
    'All automod settings have been reset to default values.'
  );

  return interaction.reply({ embeds: [embed], ephemeral: true });
}

// 🛠️ UTILITY FUNCTIONS
function getFilterKey(filterType) {
  const mapping = {
    'invites': 'antiInvites',
    'links': 'antiLinks',
    'spam': 'antiSpam',
    'caps': 'antiCaps',
    'mentions': 'mentionSpam',
    'scam': 'antiScam'
  };
  return mapping[filterType];
}

function getFilterName(filterType) {
  const mapping = {
    'invites': 'Invite Protection',
    'links': 'Link Protection',
    'spam': 'Spam Protection',
    'caps': 'Caps Protection',
    'mentions': 'Mention Spam Protection',
    'scam': 'Scam Protection'
  };
  return mapping[filterType];
}

function getActionName(action) {
  const mapping = {
    'delete': 'Delete Message',
    'warn': 'Warn User',
    'timeout': 'Timeout User',
    'kick': 'Kick User',
    'ban': 'Ban User',
    'lockdown': 'Server Lockdown'
  };
  return mapping[action];
}

function getStatusIcon(enabled) {
  return enabled ? EMOJIS.CHECK : EMOJIS.CROSS;
}

function createSuccessEmbed(title, description) {
  return new EmbedBuilder()
    .setTitle(title)
    .setDescription(description)
    .setColor(COLORS.SUCCESS)
    .setTimestamp();
}
