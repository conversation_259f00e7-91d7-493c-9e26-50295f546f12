const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonStyle } = require("discord.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("mindread")
    .setDescription("I will read your mind!")
    .addNumberOption((option) =>
      option
        .setName("number")
        .setDescription("Think of a number between 1 and 10")
        .setMaxValue(10)
        .setMinValue(1)
        .setRequired(true)
    ),
  async execute(interaction) {
    const userNumber = interaction.options.getNumber("number");

    // Initial response
    await interaction.reply("🔮 Scanning your brain waves... 🧠");

    setTimeout(async () => {
      await interaction.editReply("🔢 Calculating probabilities... ✍️");
    }, 1500);

    setTimeout(async () => {
      let guessedNumber = userNumber;
      let isWrongGuess = Math.random() < 0.4; // 40% chance of a wrong/funny response
      let responseText = `I read your mind... Your number is **${userN<PERSON>ber}**! 🤯`;
      let embedColor = "Green";
      let button = null;

      if (isWrongGuess) {
        const funnyResponses = [
          "Wait... why is your mind just full of memes? 🤔",
          "I see... nothing? Do you even have thoughts? 😵",
          "Oh no! Your mind is too powerful, I can't read it! 🚀",
          "Your number is... uh... potato? 🥔",
          `Your number is **${Math.floor(Math.random() * 10) + 1}**! (I hope I got it right 😬)`,
        ];
        responseText = funnyResponses[Math.floor(Math.random() * funnyResponses.length)];
        embedColor = "Red";

        if (responseText.includes("Your number is **")) {
          button = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId("reveal")
              .setLabel("Reveal the truth")
              .setStyle(ButtonStyle.Primary)
          );
        }
      }

      const embed = new EmbedBuilder()
        .setTitle("🧠 Mind Reading Results!")
        .setColor(embedColor)
        .setDescription(responseText);

      const reply = await interaction.editReply({
        embeds: [embed],
        components: button ? [button] : [],
      });

      if (button) {
        const filter = (i) => i.customId === "reveal" && i.user.id === interaction.user.id;
        const collector = reply.createMessageComponentCollector({ filter, time: 15000 });

        collector.on("collect", async (i) => {
          embed.setColor("Green");
          embed.setDescription(`Okay, okay... I was just messing with you! Your real number is **${userNumber}**! 😆`);
          await i.update({ embeds: [embed], components: [] });
        });

        collector.on("end", async () => {
          await interaction.editReply({ components: [] }).catch(() => {});
        });
      }
    }, 4000);
  }
};