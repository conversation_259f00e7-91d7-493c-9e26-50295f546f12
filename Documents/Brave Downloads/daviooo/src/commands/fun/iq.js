const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle
} = require('discord.js');

module.exports = {
  cooldown: '10',
  data: new SlashCommandBuilder()
    .setName('iq')
    .setDescription("Generates and provides the user's IQ with advanced visualization 🧠")
    .addUserOption(option =>
      option
        .setName('user')
        .setDescription('Check the IQ of a user')
        .setRequired(false)
    )
    .addBooleanOption(option =>
      option
        .setName('public')
        .setDescription('Make the result visible to everyone (default: true)')
        .setRequired(false)
    ),

  async execute(interaction) {
    // Optionen auslesen
    const user = interaction.options.getUser('user') || interaction.user;
    const isPublic = interaction.options.getBoolean('public') ?? true;

    // Denk-Indikator anzeigen
    await interaction.deferReply({ ephemeral: !isPublic });

    try {
      // Seed-basierte, konsistente IQ-Berechnung anhand der User-ID
      const seed = parseInt(user.id.slice(-8), 10);
      const rng = mulberry32(seed);
      const minIQ = 20;
      const maxIQ = 200;
      const randomIQ = Math.floor(rng() * (maxIQ - minIQ + 1)) + minIQ;

      // Erstelle Text-basiertes Gauge
      const textGauge = createTextIQGauge(randomIQ);

      // Zusätzliche Berechnungen
      const iqCategory = getIQCategory(randomIQ);
      const iqPercentile = getIQPercentile(randomIQ);
      const emoji = getEmojiBasedOnIQ(randomIQ);
      const message = generateIQMessage(randomIQ, user.username);

      // Embed mit Text-Gauge
      const embed = new EmbedBuilder()
        .setTitle(`${emoji} ${user.username}'s IQ Assessment`)
        .setDescription(message)
        .setColor(getColorBasedOnIQ(randomIQ))
        .setThumbnail(user.displayAvatarURL({ dynamic: true }))
        .addFields(
          {
            name: '📊 IQ Score',
            value: `**${randomIQ}** (${iqCategory})`,
            inline: true,
          },
          {
            name: '🏆 Percentile',
            value: `${iqPercentile}% of people score lower`,
            inline: true,
          },
          {
            name: '📈 Visual Gauge',
            value: textGauge,
            inline: false,
          }
        )
        .setFooter({
          text: isPublic 
            ? `Requested by ${interaction.user.username} • Results are for entertainment only!`
            : 'Results are for entertainment only!',
          iconURL: interaction.user.displayAvatarURL({ dynamic: true }),
        })
        .setTimestamp();

      // Neue Aktionszeile mit zwei Link-Buttons
      const row = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setLabel('Invite Davio')
            .setStyle(ButtonStyle.Link)
            .setURL('https://discord.com/oauth2/authorize?client_id=1296137985082789919'),
          new ButtonBuilder()
            .setLabel('Learn More About IQ')
            .setStyle(ButtonStyle.Link)
            .setURL('https://youtu.be/nAnBU8qxh7I')
        );

      await interaction.editReply({
        embeds: [embed],
        components: [row]
      });
    } catch (error) {
      console.error('Error executing IQ command:', error);
      await interaction.editReply({ 
        content: 'There was an error processing your IQ test! Please try again later.',
        ephemeral: true 
      });
    }
  },
};

// ----------------------
// Utility Functions
// ----------------------

// Seedbare Zufallszahlengenerator
function mulberry32(seed) {
  return function() {
    let t = seed += 0x6D2B79F5;
    t = Math.imul(t ^ (t >>> 15), t | 1);
    t ^= t + Math.imul(t ^ (t >>> 7), t | 61);
    return ((t ^ (t >>> 14)) >>> 0) / 4294967296;
  };
}

function getColorBasedOnIQ(iq) {
  const colors = {
    exceptional: '#FFD700',
    verySuperior: '#9370DB',
    superior: '#1E90FF',
    highAverage: '#32CD32',
    average: '#FFFF00',
    lowAverage: '#FFA500',
    borderline: '#FF4500',
    low: '#A9A9A9'
  };

  if (iq >= 160) return colors.exceptional;
  if (iq >= 140) return colors.verySuperior;
  if (iq >= 120) return colors.superior;
  if (iq >= 110) return colors.highAverage;
  if (iq >= 90) return colors.average;
  if (iq >= 70) return colors.lowAverage;
  if (iq >= 50) return colors.borderline;
  return colors.low;
}

function getEmojiBasedOnIQ(iq) {
  if (iq >= 160) return '🌟';
  if (iq >= 140) return '🧠';
  if (iq >= 120) return '🎓';
  if (iq >= 110) return '💡';
  if (iq >= 90) return '📚';
  if (iq >= 70) return '🤔';
  if (iq >= 50) return '📝';
  return '🔍';
}

function getIQCategory(iq) {
  if (iq >= 160) return 'Exceptional';
  if (iq >= 140) return 'Very Superior';
  if (iq >= 120) return 'Superior';
  if (iq >= 110) return 'High Average';
  if (iq >= 90) return 'Average';
  if (iq >= 70) return 'Low Average';
  if (iq >= 50) return 'Borderline';
  return 'Extremely Low';
}

function getIQPercentile(iq) {
  const z = (iq - 100) / 15;
  const percentile = Math.round(normalCDF(z) * 100);
  return Math.min(Math.max(percentile, 1), 99);
}

function normalCDF(x) {
  const t = 1 / (1 + 0.2316419 * Math.abs(x));
  const d = 0.3989423 * Math.exp(-x * x / 2);
  let prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
  if (x > 0) prob = 1 - prob;
  return prob;
}

function createTextIQGauge(iq) {
  // Erstelle eine schöne Text-basierte Gauge
  const gaugeLength = 20;
  const minIQ = 20;
  const maxIQ = 200;

  // Berechne Position auf der Gauge
  const position = Math.round(((iq - minIQ) / (maxIQ - minIQ)) * gaugeLength);

  // Erstelle die Gauge mit verschiedenen Farb-Emojis
  let gauge = '';
  for (let i = 0; i <= gaugeLength; i++) {
    if (i === position) {
      gauge += '🔴'; // Nadel-Position
    } else if (i < 4) {
      gauge += '🟥'; // Niedrig (20-50)
    } else if (i < 8) {
      gauge += '🟧'; // Unter Durchschnitt (50-90)
    } else if (i < 12) {
      gauge += '🟨'; // Durchschnitt (90-110)
    } else if (i < 16) {
      gauge += '🟩'; // Überdurchschnitt (110-130)
    } else if (i < 18) {
      gauge += '🟦'; // Hoch (130-150)
    } else {
      gauge += '🟪'; // Außergewöhnlich (150+)
    }
  }

  // Füge Skala hinzu
  const scale = `\`\`\`
20    50    90   110   130   150   200
│     │     │     │     │     │     │
${gauge}
\`\`\``;

  return scale + `\n**Your IQ: ${iq}** 🎯\n*Powered by Davio*`;
}

function generateIQMessage(iq, username) {
  if (iq >= 160) {
    return `Wow! **${username}** has an exceptional IQ of **${iq}**!`;
  } else if (iq >= 140) {
    return `Incredible! **${username}** has a very superior IQ of **${iq}**!`;
  } else if (iq >= 120) {
    return `Impressive! **${username}** has a superior IQ of **${iq}**!`;
  } else if (iq >= 110) {
    return `Great job! **${username}** has a high average IQ of **${iq}**!`;
  } else if (iq >= 90) {
    return `**${username}** has an average IQ of **${iq}**.`;
  } else if (iq >= 70) {
    return `**${username}** has a low average IQ of **${iq}**.`;
  } else if (iq >= 50) {
    return `**${username}** has a borderline IQ of **${iq}**.`;
  } else {
    return `**${username}** has an IQ of **${iq}**.`;
  }
}