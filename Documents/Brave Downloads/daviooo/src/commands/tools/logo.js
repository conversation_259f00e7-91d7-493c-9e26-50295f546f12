const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('logo')
    .setDescription('Generate a custom ASCII art logo')
    .addStringOption(option =>
      option.setName('text')
        .setDescription('Text to display on the logo (max 10 characters)')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('style')
        .setDescription('Logo style')
        .setRequired(true)
        .addChoices(
          { name: 'Block', value: 'block' },
          { name: 'Banner', value: 'banner' },
          { name: 'Digital', value: 'digital' },
          { name: 'Bubble', value: 'bubble' },
          { name: 'Shadow', value: 'shadow' },
          { name: 'Double', value: 'double' }
        )),

  async execute(interaction) {
    await interaction.deferReply();

    try {
      // Get options
      const text = interaction.options.getString('text');
      const style = interaction.options.getString('style');

      // Validate text length
      if (text.length > 10) {
        return await interaction.editReply({
          content: '❌ Text must be 10 characters or less for ASCII art generation!',
          ephemeral: true
        });
      }

      // Generate ASCII art logo
      const asciiLogo = this.generateASCIILogo(text, style);
      const styleInfo = this.getStyleInfo(style);

      const embed = new EmbedBuilder()
        .setTitle('🎨 Your ASCII Art Logo')
        .setDescription(`Here's your **${this.capitalizeFirstLetter(style)}** style logo!`)
        .addFields(
          { name: '📝 Text', value: `\`${text}\``, inline: true },
          { name: '🎭 Style', value: this.capitalizeFirstLetter(style), inline: true },
          { name: '✨ ASCII Logo', value: `\`\`\`\n${asciiLogo}\n\`\`\``, inline: false }
        )
        .setColor(styleInfo.color)
        .setTimestamp()
        .setFooter({
          text: `Requested by ${interaction.user.tag} • Powered by Davio`,
          iconURL: interaction.user.displayAvatarURL({ dynamic: true })
        });

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      console.error('Error generating ASCII logo:', error);
      await interaction.editReply({
        content: `❌ Failed to generate logo: ${error.message || 'Unknown error'}`,
        ephemeral: true
      });
    }
  },

  // ASCII Art Generation Methods
  generateASCIILogo(text, style) {
    const upperText = text.toUpperCase();

    switch (style) {
      case 'block':
        return this.generateBlockStyle(upperText);
      case 'banner':
        return this.generateBannerStyle(upperText);
      case 'digital':
        return this.generateDigitalStyle(upperText);
      case 'bubble':
        return this.generateBubbleStyle(upperText);
      case 'shadow':
        return this.generateShadowStyle(upperText);
      case 'double':
        return this.generateDoubleStyle(upperText);
      default:
        return this.generateBlockStyle(upperText);
    }
  },

  generateBlockStyle(text) {
    const chars = {
      'A': ['██████', '██  ██', '██████', '██  ██', '██  ██'],
      'B': ['██████', '██  ██', '██████', '██  ██', '██████'],
      'C': ['██████', '██    ', '██    ', '██    ', '██████'],
      'D': ['██████', '██  ██', '██  ██', '██  ██', '██████'],
      'E': ['██████', '██    ', '██████', '██    ', '██████'],
      'F': ['██████', '██    ', '██████', '██    ', '██    '],
      'G': ['██████', '██    ', '██ ███', '██  ██', '██████'],
      'H': ['██  ██', '██  ██', '██████', '██  ██', '██  ██'],
      'I': ['██████', '  ██  ', '  ██  ', '  ██  ', '██████'],
      'J': ['██████', '    ██', '    ██', '██  ██', '██████'],
      'K': ['██  ██', '██ ██ ', '████  ', '██ ██ ', '██  ██'],
      'L': ['██    ', '██    ', '██    ', '██    ', '██████'],
      'M': ['██  ██', '██████', '██████', '██  ██', '██  ██'],
      'N': ['██  ██', '██████', '██████', '██████', '██  ██'],
      'O': ['██████', '██  ██', '██  ██', '██  ██', '██████'],
      'P': ['██████', '██  ██', '██████', '██    ', '██    '],
      'Q': ['██████', '██  ██', '██ ███', '██████', '    ██'],
      'R': ['██████', '██  ██', '██████', '██ ██ ', '██  ██'],
      'S': ['██████', '██    ', '██████', '    ██', '██████'],
      'T': ['██████', '  ██  ', '  ██  ', '  ██  ', '  ██  '],
      'U': ['██  ██', '██  ██', '██  ██', '██  ██', '██████'],
      'V': ['██  ██', '██  ██', '██  ██', ' ████ ', '  ██  '],
      'W': ['██  ██', '██  ██', '██████', '██████', '██  ██'],
      'X': ['██  ██', ' ████ ', '  ██  ', ' ████ ', '██  ██'],
      'Y': ['██  ██', '██  ██', ' ████ ', '  ██  ', '  ██  '],
      'Z': ['██████', '   ██ ', '  ██  ', ' ██   ', '██████'],
      ' ': ['      ', '      ', '      ', '      ', '      '],
      '0': ['██████', '██  ██', '██  ██', '██  ██', '██████'],
      '1': ['  ██  ', ' ███  ', '  ██  ', '  ██  ', '██████'],
      '2': ['██████', '    ██', '██████', '██    ', '██████'],
      '3': ['██████', '    ██', '██████', '    ██', '██████'],
      '4': ['██  ██', '██  ██', '██████', '    ██', '    ██'],
      '5': ['██████', '██    ', '██████', '    ██', '██████'],
      '6': ['██████', '██    ', '██████', '██  ██', '██████'],
      '7': ['██████', '    ██', '   ██ ', '  ██  ', ' ██   '],
      '8': ['██████', '██  ██', '██████', '██  ██', '██████'],
      '9': ['██████', '██  ██', '██████', '    ██', '██████']
    };

    return this.buildASCII(text, chars);
  },
  
  async applyStyle(ctx, style, size) {
    switch (style) {
      case 'minimalist':
        // Clean background, no additional effects
        break;
        
      case 'gradient':
        const gradient = ctx.createLinearGradient(0, 0, size, size);
        gradient.addColorStop(0, '#3498db');
        gradient.addColorStop(1, '#9b59b6');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, size, size);
        break;
        
      case 'neon':
        // Add glow effect
        ctx.shadowColor = '#00ffff';
        ctx.shadowBlur = size / 10;
        ctx.fillStyle = '#111111';
        ctx.fillRect(0, 0, size, size);
        ctx.shadowBlur = 0;
        break;
        
      case 'retro':
        // Add retro patterns
        ctx.fillStyle = '#f39c12';
        
        // Create stripe pattern
        for (let i = 0; i < size; i += size / 20) {
          ctx.fillRect(0, i, size, size / 40);
        }
        break;
        
      case 'gaming':
        // Dark background with tech lines
        ctx.fillStyle = '#1e1e1e';
        ctx.fillRect(0, 0, size, size);
        
        // Add tech lines
        ctx.strokeStyle = '#33ff33';
        ctx.lineWidth = size / 100;
        
        for (let i = 0; i < 10; i++) {
          const pos = Math.random() * size;
          ctx.beginPath();
          ctx.moveTo(0, pos);
          ctx.lineTo(size, pos);
          ctx.stroke();
        }
        break;
        
      case 'tech':
        // Create circuit-like pattern
        ctx.fillStyle = '#0a192f';
        ctx.fillRect(0, 0, size, size);
        
        ctx.strokeStyle = '#64ffda';
        ctx.lineWidth = size / 200;
        
        // Draw circuit patterns
        for (let i = 0; i < 5; i++) {
          const startX = Math.random() * size;
          const startY = Math.random() * size;
          
          ctx.beginPath();
          ctx.moveTo(startX, startY);
          
          // Create random path
          let x = startX;
          let y = startY;
          
          for (let j = 0; j < 5; j++) {
            const direction = Math.floor(Math.random() * 4);
            const length = Math.random() * (size / 5) + (size / 10);
            
            switch (direction) {
              case 0: x += length; break; // right
              case 1: x -= length; break; // left
              case 2: y += length; break; // down
              case 3: y -= length; break; // up
            }
            
            ctx.lineTo(x, y);
          }
          
          ctx.stroke();
        }
        break;
        
      case 'abstract':
        // Create random shapes
        for (let i = 0; i < 10; i++) {
          const hue = Math.random() * 360;
          ctx.fillStyle = `hsla(${hue}, 70%, 60%, 0.5)`;
          
          // Draw random shape
          ctx.beginPath();
          ctx.moveTo(Math.random() * size, Math.random() * size);
          
          for (let j = 0; j < 5; j++) {
            ctx.lineTo(Math.random() * size, Math.random() * size);
          }
          
          ctx.closePath();
          ctx.fill();
        }
        break;
        
      case 'elegant':
        // Subtle gradient
        const elegantGradient = ctx.createLinearGradient(0, 0, size, size);
        elegantGradient.addColorStop(0, '#2c3e50');
        elegantGradient.addColorStop(1, '#4a6572');
        ctx.fillStyle = elegantGradient;
        ctx.fillRect(0, 0, size, size);
        
        // Add subtle patterns
        ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
        for (let i = 0; i < size; i += size / 20) {
          ctx.fillRect(0, i, size, 1);
        }
        break;
    }
  },
  
  async drawText(ctx, options) {
    const { text, font, color, size, style, shadow } = options;
    
    // Set font based on style and font option
    let fontSize = size / (text.length > 10 ? 10 : 5);
    let fontFamily = this.getFontFamily(font);
    
    ctx.font = `bold ${fontSize}px ${fontFamily}`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Apply shadow if needed
    if (shadow) {
      ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
      ctx.shadowBlur = size / 30;
      ctx.shadowOffsetX = size / 100;
      ctx.shadowOffsetY = size / 100;
    }
    
    // Style-specific text effects
    switch (style) {
      case 'neon':
        // Neon glow effect
        ctx.shadowColor = color;
        ctx.shadowBlur = size / 15;
        ctx.fillStyle = '#ffffff';
        ctx.fillText(text, size / 2, size / 2);
        
        // Double glow for more intensity
        ctx.shadowBlur = size / 30;
        ctx.fillText(text, size / 2, size / 2);
        break;
        
      case 'retro':
        // 3D text effect
        ctx.fillStyle = '#ffffff';
        const offset = size / 50;
        
        // Draw multiple layers for 3D effect
        for (let i = 7; i > 0; i--) {
          ctx.fillStyle = i % 2 === 0 ? '#e74c3c' : '#f1c40f';
          ctx.fillText(text, size / 2, size / 2 + (i * offset));
        }
        
        // Final front layer
        ctx.fillStyle = '#ffffff';
        ctx.fillText(text, size / 2, size / 2);
        break;
        
      case 'gaming':
        // Glitch effect
        const glitchOffset = size / 100;
        
        // Red channel
        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
        ctx.fillText(text, size / 2 + glitchOffset, size / 2);
        
        // Green channel
        ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
        ctx.fillText(text, size / 2, size / 2);
        
        // Blue channel
        ctx.fillStyle = 'rgba(0, 0, 255, 0.8)';
        ctx.fillText(text, size / 2 - glitchOffset, size / 2);
        break;
        
      case 'tech':
        // Main text
        ctx.fillStyle = '#64ffda';
        ctx.fillText(text, size / 2, size / 2);
        
        // Add scanning line effect
        ctx.fillStyle = 'rgba(100, 255, 218, 0.3)';
        ctx.fillRect(0, size / 2, size, size / 50);
        break;
        
      case 'elegant':
        // Gold gradient text
        const textGradient = ctx.createLinearGradient(
          size / 2 - ctx.measureText(text).width / 2,
          size / 2 - fontSize / 2,
          size / 2 + ctx.measureText(text).width / 2,
          size / 2 + fontSize / 2
        );
        
        textGradient.addColorStop(0, '#f1c40f');
        textGradient.addColorStop(0.5, '#ffffff');
        textGradient.addColorStop(1, '#f39c12');
        
        ctx.fillStyle = textGradient;
        ctx.fillText(text, size / 2, size / 2);
        break;
        
      default:
        // Default text rendering
        ctx.fillStyle = color;
        ctx.fillText(text, size / 2, size / 2);
    }
    
    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
  },
  
  async addAnimationEffects(ctx, style, size) {
    // Note: In a static image, we can only simulate animation effects
    // For actual animation, you'd need to export as GIF/video
    
    switch (style) {
      case 'neon':
        // Add "pulse" effect circles
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.beginPath();
        ctx.arc(size / 2, size / 2, size * 0.4, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.beginPath();
        ctx.arc(size / 2, size / 2, size * 0.6, 0, Math.PI * 2);
        ctx.fill();
        break;
        
      case 'tech':
        // Add "scanning" effects
        ctx.fillStyle = 'rgba(100, 255, 218, 0.1)';
        for (let i = 0; i < size; i += size / 10) {
          ctx.fillRect(0, i, size, size / 50);
        }
        break;
        
      case 'gaming':
        // Add simulated "glitch" particles
        for (let i = 0; i < 50; i++) {
          const x = Math.random() * size;
          const y = Math.random() * size;
          const width = Math.random() * (size / 20) + 1;
          const height = Math.random() * (size / 100) + 1;
          
          ctx.fillStyle = `rgba(${Math.random() * 100 + 155}, ${Math.random() * 100 + 155}, ${Math.random() * 155 + 100}, 0.8)`;
          ctx.fillRect(x, y, width, height);
        }
        break;
    }
  },
  
  async addFinalTouches(ctx, style, size, color) {
    switch (style) {
      case 'minimalist':
        // Add subtle accent line
        ctx.strokeStyle = color;
        ctx.lineWidth = size / 100;
        ctx.beginPath();
        ctx.moveTo(size * 0.3, size * 0.8);
        ctx.lineTo(size * 0.7, size * 0.8);
        ctx.stroke();
        break;
        
      case 'elegant':
        // Add decorative corner elements
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = size / 200;
        const cornerSize = size / 10;
        
        // Top-left corner
        ctx.beginPath();
        ctx.moveTo(cornerSize, cornerSize / 2);
        ctx.lineTo(cornerSize / 2, cornerSize / 2);
        ctx.lineTo(cornerSize / 2, cornerSize);
        ctx.stroke();
        
        // Top-right corner
        ctx.beginPath();
        ctx.moveTo(size - cornerSize, cornerSize / 2);
        ctx.lineTo(size - cornerSize / 2, cornerSize / 2);
        ctx.lineTo(size - cornerSize / 2, cornerSize);
        ctx.stroke();
        
        // Bottom-left corner
        ctx.beginPath();
        ctx.moveTo(cornerSize / 2, size - cornerSize);
        ctx.lineTo(cornerSize / 2, size - cornerSize / 2);
        ctx.lineTo(cornerSize, size - cornerSize / 2);
        ctx.stroke();
        
        // Bottom-right corner
        ctx.beginPath();
        ctx.moveTo(size - cornerSize, size - cornerSize / 2);
        ctx.lineTo(size - cornerSize / 2, size - cornerSize / 2);
        ctx.lineTo(size - cornerSize / 2, size - cornerSize);
        ctx.stroke();
        break;
        
      case 'tech':
        // Add border frame
        ctx.strokeStyle = '#64ffda';
        ctx.lineWidth = size / 100;
        ctx.strokeRect(size / 20, size / 20, size * 0.9, size * 0.9);
        
        // Add corner markers
        const markerSize = size / 30;
        
        // Top-left
        ctx.fillStyle = '#64ffda';
        ctx.fillRect(size / 20, size / 20, markerSize, markerSize);
        
        // Top-right
        ctx.fillRect(size * 0.9 + size / 20 - markerSize, size / 20, markerSize, markerSize);
        
        // Bottom-left
        ctx.fillRect(size / 20, size * 0.9 + size / 20 - markerSize, markerSize, markerSize);
        
        // Bottom-right
        ctx.fillRect(size * 0.9 + size / 20 - markerSize, size * 0.9 + size / 20 - markerSize, markerSize, markerSize);
        break;
    }
  },
  
  // Helper methods for default values based on style
  getDefaultColor(style) {
    const colors = {
      minimalist: '#2c3e50',
      gradient: '#3498db',
      neon: '#00ffff',
      retro: '#f39c12',
      gaming: '#33ff33',
      tech: '#64ffda',
      abstract: '#e74c3c',
      elegant: '#f1c40f'
    };
    
    return colors[style] || '#3498db';
  },
  
  getDefaultBackground(style) {
    const backgrounds = {
      minimalist: '#ffffff',
      gradient: '#2c3e50',
      neon: '#111111',
      retro: '#333333',
      gaming: '#1e1e1e',
      tech: '#0a192f',
      abstract: '#ecf0f1',
      elegant: '#2c3e50'
    };
    
    return backgrounds[style] || '#ffffff';
  },
  
  getDefaultShape(style) {
    const shapes = {
      minimalist: 'circle',
      gradient: 'rounded',
      neon: 'circle',
      retro: 'square',
      gaming: 'hexagon',
      tech: 'square',
      abstract: 'circle',
      elegant: 'shield'
    };
    
    return shapes[style] || 'circle';
  },
  
  getDefaultFont(style) {
    const fonts = {
      minimalist: 'sans-serif',
      gradient: 'sans-serif',
      neon: 'monospace',
      retro: 'monospace',
      gaming: 'fantasy',
      tech: 'monospace',
      abstract: 'sans-serif',
      elegant: 'serif'
    };
    
    return fonts[style] || 'sans-serif';
  },
  
  getDefaultShadow(style) {
    const shadows = {
      minimalist: false,
      gradient: true,
      neon: true,
      retro: true,
      gaming: true,
      tech: true,
      abstract: false,
      elegant: true
    };
    
    return shadows[style] || false;
  },
  
  getFontFamily(font) {
    const fonts = {
      'sans-serif': 'Arial, Helvetica, sans-serif',
      'serif': 'Georgia, Times New Roman, serif',
      'monospace': 'Courier New, monospace',
      'fantasy': 'Impact, fantasy',
      'cursive': 'Comic Sans MS, cursive'
    };
    
    return fonts[font] || 'Arial, sans-serif';
  },
  
  capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  },
  
  isValidHex(color) {
    return /^#([0-9A-F]{3}){1,2}$/i.test(color);
  }
};