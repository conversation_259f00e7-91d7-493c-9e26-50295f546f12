// 📊 PROFESSIONAL PERFORMANCE MONITOR - ACADEMIC EXCELLENCE
// Advanced system monitoring with comprehensive metrics and alerts

const logger = require('./logger');
const os = require('os');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      memory: {
        used: 0,
        total: 0,
        percentage: 0,
        history: []
      },
      cpu: {
        usage: 0,
        history: []
      },
      commands: {
        total: 0,
        perMinute: 0,
        history: []
      },
      uptime: 0,
      errors: {
        total: 0,
        perHour: 0,
        history: []
      }
    };
    
    this.alerts = {
      highMemory: 80, // Alert at 80% memory usage
      highCpu: 70,    // Alert at 70% CPU usage
      highErrors: 10  // Alert at 10 errors per hour
    };
    
    this.startTime = Date.now();
    this.lastCpuUsage = process.cpuUsage();
    this.commandCount = 0;
    this.errorCount = 0;
    
    this.startMonitoring();
  }

  startMonitoring() {
    // Monitor every 30 seconds
    setInterval(() => {
      this.updateMetrics();
      this.checkAlerts();
    }, 30000);

    // Log summary every 5 minutes
    setInterval(() => {
      this.logSummary();
    }, 300000);

    logger.info('Performance monitoring started');
  }

  updateMetrics() {
    // Memory metrics
    const memUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const usedMemory = memUsage.heapUsed;
    
    this.metrics.memory.used = usedMemory;
    this.metrics.memory.total = totalMemory;
    this.metrics.memory.percentage = (usedMemory / totalMemory) * 100;
    this.metrics.memory.history.push({
      timestamp: Date.now(),
      value: this.metrics.memory.percentage
    });

    // Keep only last 100 entries
    if (this.metrics.memory.history.length > 100) {
      this.metrics.memory.history.shift();
    }

    // CPU metrics
    const currentCpuUsage = process.cpuUsage(this.lastCpuUsage);
    const cpuPercent = (currentCpuUsage.user + currentCpuUsage.system) / 1000000 * 100;
    
    this.metrics.cpu.usage = cpuPercent;
    this.metrics.cpu.history.push({
      timestamp: Date.now(),
      value: cpuPercent
    });

    if (this.metrics.cpu.history.length > 100) {
      this.metrics.cpu.history.shift();
    }

    this.lastCpuUsage = process.cpuUsage();

    // Uptime
    this.metrics.uptime = Date.now() - this.startTime;

    // Commands per minute
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const recentCommands = this.metrics.commands.history.filter(
      cmd => cmd.timestamp > oneMinuteAgo
    );
    this.metrics.commands.perMinute = recentCommands.length;

    // Errors per hour
    const oneHourAgo = now - 3600000;
    const recentErrors = this.metrics.errors.history.filter(
      err => err.timestamp > oneHourAgo
    );
    this.metrics.errors.perHour = recentErrors.length;
  }

  checkAlerts() {
    const { memory, cpu, errors } = this.metrics;

    // Memory alert
    if (memory.percentage > this.alerts.highMemory) {
      logger.warning(`High memory usage: ${memory.percentage.toFixed(1)}%`);
    }

    // CPU alert
    if (cpu.usage > this.alerts.highCpu) {
      logger.warning(`High CPU usage: ${cpu.usage.toFixed(1)}%`);
    }

    // Error rate alert
    if (errors.perHour > this.alerts.highErrors) {
      logger.warning(`High error rate: ${errors.perHour} errors/hour`);
    }
  }

  logSummary() {
    const { memory, cpu, commands, uptime, errors } = this.metrics;
    
    logger.separator();
    logger.system('📊 PERFORMANCE SUMMARY', 'info');
    logger.system(`💾 Memory: ${this.formatBytes(memory.used)} / ${this.formatBytes(memory.total)} (${memory.percentage.toFixed(1)}%)`);
    logger.system(`🖥️  CPU: ${cpu.usage.toFixed(1)}%`);
    logger.system(`⚡ Commands: ${commands.total} total, ${commands.perMinute}/min`);
    logger.system(`⏱️  Uptime: ${this.formatUptime(uptime)}`);
    logger.system(`❌ Errors: ${errors.total} total, ${errors.perHour}/hour`);
    logger.separator();
  }

  // Track command execution
  recordCommand(commandName) {
    this.commandCount++;
    this.metrics.commands.total++;
    this.metrics.commands.history.push({
      timestamp: Date.now(),
      command: commandName
    });

    // Keep only last 1000 commands
    if (this.metrics.commands.history.length > 1000) {
      this.metrics.commands.history.shift();
    }
  }

  // Track errors
  recordError(error) {
    this.errorCount++;
    this.metrics.errors.total++;
    this.metrics.errors.history.push({
      timestamp: Date.now(),
      error: error.message || 'Unknown error'
    });

    // Keep only last 500 errors
    if (this.metrics.errors.history.length > 500) {
      this.metrics.errors.history.shift();
    }
  }

  // Get current metrics
  getMetrics() {
    this.updateMetrics();
    return {
      ...this.metrics,
      system: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
        loadAverage: os.loadavg()
      }
    };
  }

  // Get performance report
  getReport() {
    const metrics = this.getMetrics();
    
    return {
      summary: {
        status: this.getHealthStatus(),
        uptime: this.formatUptime(metrics.uptime),
        memoryUsage: `${metrics.memory.percentage.toFixed(1)}%`,
        cpuUsage: `${metrics.cpu.usage.toFixed(1)}%`,
        commandsPerMinute: metrics.commands.perMinute,
        errorsPerHour: metrics.errors.perHour
      },
      details: metrics,
      alerts: this.getActiveAlerts()
    };
  }

  getHealthStatus() {
    const { memory, cpu, errors } = this.metrics;
    
    if (memory.percentage > this.alerts.highMemory || 
        cpu.usage > this.alerts.highCpu || 
        errors.perHour > this.alerts.highErrors) {
      return 'warning';
    }
    
    return 'healthy';
  }

  getActiveAlerts() {
    const alerts = [];
    const { memory, cpu, errors } = this.metrics;

    if (memory.percentage > this.alerts.highMemory) {
      alerts.push(`High memory usage: ${memory.percentage.toFixed(1)}%`);
    }

    if (cpu.usage > this.alerts.highCpu) {
      alerts.push(`High CPU usage: ${cpu.usage.toFixed(1)}%`);
    }

    if (errors.perHour > this.alerts.highErrors) {
      alerts.push(`High error rate: ${errors.perHour} errors/hour`);
    }

    return alerts;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
}

module.exports = new PerformanceMonitor();
