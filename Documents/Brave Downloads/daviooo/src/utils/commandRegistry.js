const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

// 🎨 ENTERPRISE COMMAND REGISTRY SYSTEM
class CommandRegistry {
  constructor() {
    this.commands = new Map();
    this.categories = new Map();
    this.commandStats = new Map();
    this.loadStartTime = Date.now();
    this.totalCommands = 0;
    this.failedCommands = 0;
  }

  // 🎨 LOAD ALL COMMANDS WITH BEAUTIFUL LOGGING
  async loadCommands(commandsPath) {
    console.log(chalk.cyan('\n🔍 Scanning command categories...'));
    
    try {
      await this.scanDirectory(commandsPath);
      this.displayLoadingSummary();
      this.generateCommandReport();
      return this.commands;
    } catch (error) {
      console.log(chalk.red(`❌ Failed to load commands: ${error.message}`));
      throw error;
    }
  }

  // 🎨 SCAN DIRECTORY RECURSIVELY
  async scanDirectory(dir, category = null) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        const categoryName = item;
        if (!this.categories.has(categoryName)) {
          this.categories.set(categoryName, {
            name: categoryName,
            commands: [],
            loadTime: Date.now()
          });
        }
        
        await this.scanDirectory(itemPath, categoryName);
      } else if (item.endsWith('.js') && item !== 'index.js') {
        await this.loadCommand(itemPath, category);
      }
    }
  }

  // 🎨 LOAD INDIVIDUAL COMMAND
  async loadCommand(filePath, category) {
    try {
      const command = require(filePath);
      
      if (command.data && command.execute) {
        const commandName = command.data.name;
        
        // Store command with metadata
        this.commands.set(commandName, {
          ...command,
          category: category || 'uncategorized',
          filePath: filePath,
          loadTime: Date.now(),
          executionCount: 0,
          lastExecuted: null,
          averageResponseTime: 0
        });

        // Add to category
        if (category && this.categories.has(category)) {
          this.categories.get(category).commands.push(commandName);
        }

        // Initialize stats
        this.commandStats.set(commandName, {
          executions: 0,
          errors: 0,
          totalResponseTime: 0,
          lastUsed: null
        });

        console.log(chalk.green(`✅ Loaded command: ${commandName} (${category || 'uncategorized'})`));
        this.totalCommands++;
      } else {
        console.log(chalk.yellow(`⚠️ Invalid command structure: ${path.basename(filePath)}`));
        this.failedCommands++;
      }
    } catch (error) {
      console.log(chalk.red(`❌ Failed to load ${path.basename(filePath)}: ${error.message}`));
      this.failedCommands++;
    }
  }

  // 🎨 DISPLAY LOADING SUMMARY
  displayLoadingSummary() {
    const loadTime = Date.now() - this.loadStartTime;
    
    console.log('\n╔══════════════════════════════════════════════════════════════╗');
    console.log('║                    📋 COMMAND LOADING SUMMARY               ║');
    console.log('╠══════════════════════════════════════════════════════════════╣');
    console.log(`║ ✅ Successfully Loaded: ${this.totalCommands.toString().padEnd(35)} ║`);
    console.log(`║ ❌ Failed to Load: ${this.failedCommands.toString().padEnd(39)} ║`);
    console.log(`║ 📂 Categories Found: ${this.categories.size.toString().padEnd(37)} ║`);
    console.log(`║ ⏱️ Load Time: ${loadTime}ms${' '.repeat(42 - loadTime.toString().length)} ║`);
    console.log(`║ 🎯 Success Rate: ${((this.totalCommands / (this.totalCommands + this.failedCommands)) * 100).toFixed(1)}%${' '.repeat(37)} ║`);
    console.log('╚══════════════════════════════════════════════════════════════╝\n');
  }

  // 🎨 GENERATE DETAILED COMMAND REPORT
  generateCommandReport() {
    console.log(chalk.cyan('🌐 Registering commands globally...'));
    
    // Display commands by category
    for (const [categoryName, categoryData] of this.categories) {
      if (categoryData.commands.length > 0) {
        // Find a representative command for this category
        const representativeCommand = categoryData.commands[0];
        console.log(chalk.green(`✅ Loaded command: ${representativeCommand} (${categoryName})`));
      }
    }

    console.log(chalk.green('\n✅ All commands registered successfully!'));
  }

  // 🎨 TRACK COMMAND EXECUTION
  trackExecution(commandName, responseTime, success = true) {
    const command = this.commands.get(commandName);
    const stats = this.commandStats.get(commandName);
    
    if (command && stats) {
      // Update command metadata
      command.executionCount++;
      command.lastExecuted = Date.now();
      
      // Update stats
      stats.executions++;
      stats.lastUsed = Date.now();
      
      if (success) {
        stats.totalResponseTime += responseTime;
        command.averageResponseTime = stats.totalResponseTime / stats.executions;
      } else {
        stats.errors++;
      }
    }
  }

  // 🎨 GET COMMAND STATISTICS
  getCommandStats(commandName) {
    const command = this.commands.get(commandName);
    const stats = this.commandStats.get(commandName);
    
    if (command && stats) {
      return {
        name: commandName,
        category: command.category,
        executions: stats.executions,
        errors: stats.errors,
        successRate: stats.executions > 0 ? ((stats.executions - stats.errors) / stats.executions * 100).toFixed(2) : 0,
        averageResponseTime: command.averageResponseTime.toFixed(2),
        lastUsed: stats.lastUsed ? new Date(stats.lastUsed).toLocaleString() : 'Never'
      };
    }
    
    return null;
  }

  // 🎨 GET CATEGORY STATISTICS
  getCategoryStats() {
    const categoryStats = new Map();
    
    for (const [categoryName, categoryData] of this.categories) {
      let totalExecutions = 0;
      let totalErrors = 0;
      let totalResponseTime = 0;
      
      for (const commandName of categoryData.commands) {
        const stats = this.commandStats.get(commandName);
        if (stats) {
          totalExecutions += stats.executions;
          totalErrors += stats.errors;
          totalResponseTime += this.commands.get(commandName)?.averageResponseTime || 0;
        }
      }
      
      categoryStats.set(categoryName, {
        commandCount: categoryData.commands.length,
        totalExecutions,
        totalErrors,
        successRate: totalExecutions > 0 ? ((totalExecutions - totalErrors) / totalExecutions * 100).toFixed(2) : 0,
        averageResponseTime: categoryData.commands.length > 0 ? (totalResponseTime / categoryData.commands.length).toFixed(2) : 0
      });
    }
    
    return categoryStats;
  }

  // 🎨 GET MOST POPULAR COMMANDS
  getMostPopularCommands(limit = 10) {
    const commandArray = Array.from(this.commandStats.entries())
      .map(([name, stats]) => ({ name, executions: stats.executions }))
      .sort((a, b) => b.executions - a.executions)
      .slice(0, limit);
    
    return commandArray;
  }

  // 🎨 GET SYSTEM OVERVIEW
  getSystemOverview() {
    const totalExecutions = Array.from(this.commandStats.values())
      .reduce((sum, stats) => sum + stats.executions, 0);
    
    const totalErrors = Array.from(this.commandStats.values())
      .reduce((sum, stats) => sum + stats.errors, 0);
    
    return {
      totalCommands: this.totalCommands,
      totalCategories: this.categories.size,
      totalExecutions,
      totalErrors,
      overallSuccessRate: totalExecutions > 0 ? ((totalExecutions - totalErrors) / totalExecutions * 100).toFixed(2) : 0,
      loadTime: Date.now() - this.loadStartTime
    };
  }

  // 🎨 DISPLAY ANALYTICS REPORT
  displayAnalyticsReport() {
    const overview = this.getSystemOverview();
    const popularCommands = this.getMostPopularCommands(5);
    
    console.log('\n╔══════════════════════════════════════════════════════════════╗');
    console.log('║                    📊 COMMAND ANALYTICS REPORT              ║');
    console.log('╠══════════════════════════════════════════════════════════════╣');
    console.log(`║ 📋 Total Commands: ${overview.totalCommands.toString().padEnd(39)} ║`);
    console.log(`║ 📂 Categories: ${overview.totalCategories.toString().padEnd(43)} ║`);
    console.log(`║ ⚡ Total Executions: ${overview.totalExecutions.toString().padEnd(37)} ║`);
    console.log(`║ 🎯 Success Rate: ${overview.overallSuccessRate}%${' '.repeat(37)} ║`);
    console.log('╠══════════════════════════════════════════════════════════════╣');
    console.log('║                    🏆 MOST POPULAR COMMANDS                  ║');
    
    popularCommands.forEach((cmd, index) => {
      const rank = (index + 1).toString().padStart(2);
      const name = cmd.name.padEnd(20);
      const executions = cmd.executions.toString().padEnd(10);
      console.log(`║ ${rank}. ${name} - ${executions} executions${' '.repeat(15)} ║`);
    });
    
    console.log('╚══════════════════════════════════════════════════════════════╝\n');
  }
}

module.exports = new CommandRegistry();
