// Professional Console Logger with Beautiful UI
// Inspired by modern CLI tools with colored boxes and timestamps

const chalk = require('chalk');

class ConsoleLogger {
  constructor() {
    this.startTime = Date.now();
    this.commandCount = 0;
    this.eventCount = 0;
    this.contextMenuCount = 0;
  }

  /**
   * Get formatted timestamp
   */
  getTimestamp() {
    const now = new Date();
    const date = now.toISOString().split('T')[0];
    const time = now.toTimeString().split(' ')[0];
    return chalk.gray(`[${date} ${time}]`);
  }

  /**
   * Create a beautiful box with content
   */
  createBox(title, items, color = 'cyan') {
    const maxLength = Math.max(title.length, ...items.map(item => item.length)) + 4;
    const border = '─'.repeat(maxLength);
    
    console.log(chalk[color](`┌${border}┐`));
    console.log(chalk[color](`│ ${title.toUpperCase().padEnd(maxLength - 2)} │`));
    console.log(chalk[color](`├${border}┤`));
    
    items.forEach(item => {
      const checkmark = chalk.green('✓');
      const content = `${checkmark} │ ${item}`;
      console.log(chalk[color](`│ Loaded: ${content.padEnd(maxLength - 2)} │`));
    });
    
    console.log(chalk[color](`└${border}┘`));
    console.log('');
  }

  /**
   * Create event status box
   */
  createEventBox(events) {
    const title = 'EVENT STATUS';
    const maxLength = Math.max(title.length, ...events.map(e => e.name.length)) + 10;
    const border = '─'.repeat(maxLength);
    
    console.log(chalk.yellow(`┌${border}┐`));
    console.log(chalk.yellow(`│ ${title.padEnd(maxLength - 2)} │`));
    console.log(chalk.yellow(`├${border}┤`));
    
    events.forEach(event => {
      const status = event.loaded ? chalk.green('✓') : chalk.red('✗');
      const content = `${status} │ ${event.name}`;
      console.log(chalk.yellow(`│ Loaded: ${content.padEnd(maxLength - 2)} │`));
    });
    
    console.log(chalk.yellow(`└${border}┘`));
    console.log('');
  }

  /**
   * Log info message with timestamp and styling
   */
  info(message) {
    console.log(`${this.getTimestamp()} ${chalk.blue('[INFO]')} ${message}`);
  }

  /**
   * Log success message
   */
  success(message) {
    console.log(`${this.getTimestamp()} ${chalk.green('[SUCCESS]')} ${message}`);
  }

  /**
   * Log warning message
   */
  warn(message) {
    console.log(`${this.getTimestamp()} ${chalk.yellow('[WARN]')} ${message}`);
  }

  /**
   * Log error message
   */
  error(message) {
    console.log(`${this.getTimestamp()} ${chalk.red('[ERROR]')} ${message}`);
  }

  /**
   * Log debug message
   */
  debug(message) {
    console.log(`${this.getTimestamp()} ${chalk.magenta('[DEBUG]')} ${message}`);
  }

  /**
   * Log command loading
   */
  command(category, subcommand) {
    this.commandCount++;
    const icon = chalk.green('✅');
    console.log(`${icon} Loaded subcommand: ${chalk.cyan(`/${category} ${subcommand}`)}`);
  }

  /**
   * Log category creation
   */
  category(name, count) {
    const icon = chalk.blue('🚀');
    console.log(`${icon} Created category command: ${chalk.cyan(`/${name}`)} with ${chalk.yellow(count)} subcommands`);
  }

  /**
   * Log context menu creation
   */
  contextMenu(name, type) {
    this.contextMenuCount++;
    const icon = chalk.green('✅');
    const typeColor = type === 'User' ? chalk.blue(type) : chalk.magenta(type);
    console.log(`${icon} Created context menu: ${chalk.cyan(name)} (${typeColor})`);
  }

  /**
   * Log event loading
   */
  event(name) {
    this.eventCount++;
    const icon = chalk.green('✅');
    console.log(`${icon} Loaded event: ${chalk.cyan(name)}`);
  }

  /**
   * Log skipped command
   */
  skip(filename, reason) {
    const icon = chalk.yellow('⚠️');
    console.log(`${icon} Skipping ${chalk.yellow(filename)}: ${reason}`);
  }

  /**
   * Log command execution
   */
  execute(command, time) {
    const icon = chalk.green('✅');
    console.log(`${icon} Executed ${chalk.cyan(command)} in ${chalk.yellow(time + 'ms')}`);
  }

  /**
   * Create startup summary
   */
  summary() {
    console.log('');
    console.log(chalk.cyan('═'.repeat(60)));
    console.log(chalk.cyan.bold('🎉 DAVIO ✨ STARTUP SUMMARY'));
    console.log(chalk.cyan('═'.repeat(60)));
    console.log(`${chalk.green('📊')} Commands Loaded: ${chalk.yellow(this.commandCount)}`);
    console.log(`${chalk.green('🎯')} Context Menus: ${chalk.yellow(this.contextMenuCount)}`);
    console.log(`${chalk.green('⚡')} Events Loaded: ${chalk.yellow(this.eventCount)}`);
    console.log(`${chalk.green('⏱️')} Startup Time: ${chalk.yellow((Date.now() - this.startTime) + 'ms')}`);
    console.log(chalk.cyan('═'.repeat(60)));
    console.log('');
  }

  /**
   * Create beautiful separator
   */
  separator(text = '') {
    console.log('');
    if (text) {
      console.log(chalk.cyan(`── ${text} ${'─'.repeat(50 - text.length)}`));
    } else {
      console.log(chalk.gray('─'.repeat(60)));
    }
    console.log('');
  }

  /**
   * Create progress bar
   */
  progress(current, total, label = 'Progress') {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * 20);
    const empty = 20 - filled;

    const bar = chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
    process.stdout.write(`\r${label}: [${bar}] ${percentage}% (${current}/${total})`);

    if (current === total) {
      console.log(''); // New line when complete
    }
  }

  /**
   * Create animated loading indicator
   */
  loading(text, duration = 1000) {
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let i = 0;

    const interval = setInterval(() => {
      process.stdout.write(`\r${chalk.cyan(frames[i])} ${text}`);
      i = (i + 1) % frames.length;
    }, 80);

    setTimeout(() => {
      clearInterval(interval);
      process.stdout.write(`\r${chalk.green('✅')} ${text}\n`);
    }, duration);

    return interval;
  }

  /**
   * Show compact loading summary
   */
  loadingSummary(items, title) {
    console.log(chalk.cyan(`\n── ${title.toUpperCase()} ────────────────────────────────────\n`));

    items.forEach((item, index) => {
      const icon = chalk.green('✅');
      const name = chalk.cyan(item.name);
      const count = item.count ? chalk.yellow(`(${item.count})`) : '';
      console.log(`${icon} ${name} ${count}`);

      // Update counters for summary
      if (title.includes('COMMANDS')) {
        this.commandCount += item.count ? parseInt(item.count.split(' ')[0]) : 1;
      } else if (title.includes('CONTEXT')) {
        this.contextMenuCount++;
      } else if (title.includes('EVENTS')) {
        this.eventCount++;
      }
    });

    console.log('');
  }

  /**
   * Clear console and show header
   */
  clear() {
    console.clear();
    this.showHeader();
  }

  /**
   * Show beautiful header
   */
  showHeader() {
    console.log('');
    console.log(chalk.cyan.bold('  ____              _         ✨ ____        _   '));
    console.log(chalk.cyan.bold(' |  _ \\  __ ___   _(_) ___     | __ )  ___ | |_ '));
    console.log(chalk.cyan.bold(' | | | |/ _` \\ \\ / / |/ _ \\    |  _ \\ / _ \\| __|'));
    console.log(chalk.cyan.bold(' | |_| | (_| |\\ V /| | (_) |   | |_) | (_) | |_ '));
    console.log(chalk.cyan.bold(' |____/ \\__,_| \\_/ |_|\\___/    |____/ \\___/ \\__|'));
    console.log('');
    console.log(chalk.yellow.bold('              🚀 Professional Discord Bot Framework 🚀'));
    console.log(chalk.gray('                    Starting up with style...'));
    console.log('');
  }
}

// Create singleton instance
const logger = new ConsoleLogger();

module.exports = logger;
