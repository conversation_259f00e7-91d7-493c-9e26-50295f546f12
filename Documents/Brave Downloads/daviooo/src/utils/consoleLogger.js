// Professional Console Logger with Beautiful UI
// Inspired by modern CLI tools with colored boxes and timestamps

const chalk = require('chalk');
const figlet = require('figlet');

class ConsoleLogger {
  constructor() {
    this.startTime = Date.now();
    this.commandCount = 0;
    this.eventCount = 0;
    this.contextMenuCount = 0;
  }

  /**
   * Get formatted timestamp
   */
  getTimestamp() {
    const now = new Date();
    const date = now.toISOString().split('T')[0];
    const time = now.toTimeString().split(' ')[0];
    return chalk.gray(`[${date} ${time}]`);
  }

  /**
   * Create a beautiful box with content (exactly like in your image)
   */
  createBox(title, items, color = 'green') {
    const boxWidth = 50;
    const border = '─'.repeat(boxWidth);

    console.log(chalk[color](`┌${border}┐`));
    console.log(chalk[color](`│ ${title.toUpperCase().padEnd(boxWidth - 2)} │`));
    console.log(chalk[color](`├${border}┤`));

    items.forEach(item => {
      const checkmark = chalk.green('✓');
      const content = `Loaded: ${checkmark} │ ${item}`;
      const padding = boxWidth - content.length - 2;
      console.log(chalk[color](`│ ${content}${' '.repeat(Math.max(0, padding))} │`));
    });

    console.log(chalk[color](`└${border}┘`));
    console.log('');
  }

  /**
   * Create event status box
   */
  createEventBox(events) {
    const title = 'EVENT STATUS';
    const maxLength = Math.max(title.length, ...events.map(e => e.name.length)) + 10;
    const border = '─'.repeat(maxLength);
    
    console.log(chalk.yellow(`┌${border}┐`));
    console.log(chalk.yellow(`│ ${title.padEnd(maxLength - 2)} │`));
    console.log(chalk.yellow(`├${border}┤`));
    
    events.forEach(event => {
      const status = event.loaded ? chalk.green('✓') : chalk.red('✗');
      const content = `${status} │ ${event.name}`;
      console.log(chalk.yellow(`│ Loaded: ${content.padEnd(maxLength - 2)} │`));
    });
    
    console.log(chalk.yellow(`└${border}┘`));
    console.log('');
  }

  /**
   * Log info message with timestamp and styling
   */
  info(message) {
    console.log(`${this.getTimestamp()} ${chalk.blue('[INFO]')} ${message}`);
  }

  /**
   * Log success message
   */
  success(message) {
    console.log(`${this.getTimestamp()} ${chalk.green('[SUCCESS]')} ${message}`);
  }

  /**
   * Log warning message
   */
  warn(message) {
    console.log(`${this.getTimestamp()} ${chalk.yellow('[WARN]')} ${message}`);
  }

  /**
   * Log error message
   */
  error(message) {
    console.log(`${this.getTimestamp()} ${chalk.red('[ERROR]')} ${message}`);
  }

  /**
   * Log debug message
   */
  debug(message) {
    console.log(`${this.getTimestamp()} ${chalk.magenta('[DEBUG]')} ${message}`);
  }

  /**
   * Log command loading
   */
  command(category, subcommand) {
    this.commandCount++;
    const icon = chalk.green('✅');
    console.log(`${icon} Loaded subcommand: ${chalk.cyan(`/${category} ${subcommand}`)}`);
  }

  /**
   * Log category creation
   */
  category(name, count) {
    const icon = chalk.blue('🚀');
    console.log(`${icon} Created category command: ${chalk.cyan(`/${name}`)} with ${chalk.yellow(count)} subcommands`);
  }

  /**
   * Log context menu creation
   */
  contextMenu(name, type) {
    this.contextMenuCount++;
    const icon = chalk.green('✅');
    const typeColor = type === 'User' ? chalk.blue(type) : chalk.magenta(type);
    console.log(`${icon} Created context menu: ${chalk.cyan(name)} (${typeColor})`);
  }

  /**
   * Log event loading
   */
  event(name) {
    this.eventCount++;
    const icon = chalk.green('✅');
    console.log(`${icon} Loaded event: ${chalk.cyan(name)}`);
  }

  /**
   * Log skipped command
   */
  skip(filename, reason) {
    const icon = chalk.yellow('⚠️');
    console.log(`${icon} Skipping ${chalk.yellow(filename)}: ${reason}`);
  }

  /**
   * Log command execution
   */
  execute(command, time) {
    const icon = chalk.green('✅');
    console.log(`${icon} Executed ${chalk.cyan(command)} in ${chalk.yellow(time + 'ms')}`);
  }

  /**
   * Create beautiful startup summary
   */
  summary() {
    const startupTime = Date.now() - this.startTime;

    console.log('');
    console.log(chalk.magenta('╔' + '═'.repeat(58) + '╗'));
    console.log(chalk.magenta('║') + chalk.cyan.bold('                🎉 DAVIO ✨ READY TO SERVE 🎉               ') + chalk.magenta('║'));
    console.log(chalk.magenta('╠' + '═'.repeat(58) + '╣'));
    console.log(chalk.magenta('║') + `  ${chalk.green('📊 Commands:')} ${chalk.yellow(this.commandCount.toString().padEnd(8))} ${chalk.green('🎯 Context Menus:')} ${chalk.yellow(this.contextMenuCount.toString().padEnd(8))}  ` + chalk.magenta('║'));
    console.log(chalk.magenta('║') + `  ${chalk.green('⚡ Events:')} ${chalk.yellow(this.eventCount.toString().padEnd(10))} ${chalk.green('⏱️  Startup:')} ${chalk.yellow((startupTime + 'ms').padEnd(12))}  ` + chalk.magenta('║'));
    console.log(chalk.magenta('║') + chalk.green('                                                          ') + chalk.magenta('║'));
    console.log(chalk.magenta('║') + chalk.cyan.bold('                    🚀 BOT IS ONLINE! 🚀                    ') + chalk.magenta('║'));
    console.log(chalk.magenta('╚' + '═'.repeat(58) + '╝'));
    console.log('');
  }

  /**
   * Create beautiful separator
   */
  separator(text = '') {
    console.log('');
    if (text) {
      console.log(chalk.cyan(`── ${text} ${'─'.repeat(50 - text.length)}`));
    } else {
      console.log(chalk.gray('─'.repeat(60)));
    }
    console.log('');
  }

  /**
   * Create progress bar
   */
  progress(current, total, label = 'Progress') {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * 20);
    const empty = 20 - filled;

    const bar = chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
    process.stdout.write(`\r${label}: [${bar}] ${percentage}% (${current}/${total})`);

    if (current === total) {
      console.log(''); // New line when complete
    }
  }

  /**
   * Create animated loading indicator
   */
  loading(text, duration = 1000) {
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let i = 0;

    const interval = setInterval(() => {
      process.stdout.write(`\r${chalk.cyan(frames[i])} ${text}`);
      i = (i + 1) % frames.length;
    }, 80);

    setTimeout(() => {
      clearInterval(interval);
      process.stdout.write(`\r${chalk.green('✅')} ${text}\n`);
    }, duration);

    return interval;
  }

  /**
   * Show beautiful box summary like in the image
   */
  showBox(items, title, color = 'green') {
    const boxItems = items.map(item => {
      if (typeof item === 'string') {
        return item;
      }
      return item.count ? `${item.name} (${item.count})` : item.name;
    });

    this.createBox(title, boxItems, color);

    // Update counters for summary
    if (title.includes('SLASH COMMANDS')) {
      items.forEach(item => {
        this.commandCount += item.count ? parseInt(item.count.split(' ')[0]) : 1;
      });
    } else if (title.includes('CONTEXT MENUS')) {
      this.contextMenuCount += items.length;
    } else if (title.includes('EVENT STATUS')) {
      this.eventCount += items.length;
    }
  }

  /**
   * Clear console and show header
   */
  clear() {
    console.clear();
    this.showHeader();
  }

  /**
   * Show beautiful header
   */
  showHeader() {
    try {
      console.log('');
      const davioArt = figlet.textSync('DAVIO', {
        font: 'Standard',
        horizontalLayout: 'default',
        verticalLayout: 'default'
      });
      console.log(chalk.cyan.bold(davioArt));
      console.log(chalk.magenta.bold('         ✨ Professional Discord Bot Framework ✨'));
      console.log(chalk.gray('              Starting up with style...'));
      console.log('');
    } catch (error) {
      // Fallback if figlet fails
      console.log('');
      console.log(chalk.cyan.bold(' ____    _    __     _____ ___'));
      console.log(chalk.cyan.bold('|  _ \\  / \\   \\ \\   / /_ _/ _ \\'));
      console.log(chalk.cyan.bold('| | | |/ _ \\   \\ \\ / / | | | | |'));
      console.log(chalk.cyan.bold('| |_| / ___ \\   \\ V /  | | |_| |'));
      console.log(chalk.cyan.bold('|____/_/   \\_\\   \\_/  |___\\___/'));
      console.log('');
      console.log(chalk.magenta.bold('         ✨ DAVIO - Professional Discord Bot ✨'));
      console.log(chalk.gray('              Starting up with style...'));
      console.log('');
    }
  }
}

// Create singleton instance
const logger = new ConsoleLogger();

module.exports = logger;
