// 🎨 PROFESSIONAL CONSOLE LOGGER - ACADEMIC EXCELLENCE
// Clean, beautiful console output for production-grade Discord bot

const chalk = require('chalk');

class Logger {
  constructor() {
    this.startTime = Date.now();
    this.logCounts = {
      info: 0,
      success: 0,
      warning: 0,
      error: 0,
      commands: 0
    };
  }

  // 🎨 BEAUTIFUL HEADER
  header() {
    console.clear();
    console.log(chalk.cyan.bold('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.cyan.bold('║') + chalk.white.bold('                    🤖 DAVIO BOT SYSTEM                     ') + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('║') + chalk.gray('                Professional Discord Bot v3.0                ') + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
  }

  // ✅ SUCCESS MESSAGES
  success(message, details = null) {
    this.logCounts.success++;
    const timestamp = this.getTimestamp();
    console.log(chalk.green('✅') + chalk.white(` [${timestamp}] `) + chalk.green.bold(message));
    if (details) console.log(chalk.gray(`   └─ ${details}`));
  }

  // ℹ️ INFO MESSAGES
  info(message, details = null) {
    this.logCounts.info++;
    const timestamp = this.getTimestamp();
    console.log(chalk.blue('ℹ️') + chalk.white(` [${timestamp}] `) + chalk.white(message));
    if (details) console.log(chalk.gray(`   └─ ${details}`));
  }

  // ⚠️ WARNING MESSAGES
  warning(message, details = null) {
    this.logCounts.warning++;
    const timestamp = this.getTimestamp();
    console.log(chalk.yellow('⚠️') + chalk.white(` [${timestamp}] `) + chalk.yellow.bold(message));
    if (details) console.log(chalk.gray(`   └─ ${details}`));
  }

  // ❌ ERROR MESSAGES
  error(message, details = null) {
    this.logCounts.error++;
    const timestamp = this.getTimestamp();
    console.log(chalk.red('❌') + chalk.white(` [${timestamp}] `) + chalk.red.bold(message));
    if (details) console.log(chalk.gray(`   └─ ${details}`));
  }

  // 🚀 STARTUP MESSAGES
  startup(message) {
    const timestamp = this.getTimestamp();
    console.log(chalk.magenta('🚀') + chalk.white(` [${timestamp}] `) + chalk.magenta.bold(message));
  }

  // 📊 COMMAND EXECUTION
  command(user, command, guild = null) {
    this.logCounts.commands++;
    const timestamp = this.getTimestamp();
    const guildInfo = guild ? chalk.gray(` in ${guild}`) : '';
    console.log(chalk.cyan('⚡') + chalk.white(` [${timestamp}] `) +
                chalk.cyan(`Command: `) + chalk.white.bold(command) +
                chalk.gray(` by ${user}`) + guildInfo);
  }

  // 🔧 SYSTEM STATUS
  system(message, status = 'info') {
    const timestamp = this.getTimestamp();
    const emoji = status === 'success' ? '🟢' : status === 'warning' ? '🟡' : '🔵';
    console.log(emoji + chalk.white(` [${timestamp}] `) + chalk.white(message));
  }

  // 🕐 TIMESTAMP
  getTimestamp() {
    return new Date().toLocaleTimeString('de-DE', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  // ⏱️ UPTIME
  getUptime() {
    const uptime = Date.now() - this.startTime;
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
    return `${hours}h ${minutes}m ${seconds}s`;
  }

  // 🎨 READY MESSAGE
  ready(botName, guildCount, userCount) {
    console.log('');
    console.log(chalk.green.bold('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.green.bold('║') + chalk.white.bold('                    🎉 BOT READY & ONLINE                    ') + chalk.green.bold('║'));
    console.log(chalk.green.bold('╠══════════════════════════════════════════════════════════════╣'));
    console.log(chalk.green.bold('║') + chalk.white(` 🤖 Bot: ${botName}`.padEnd(60)) + chalk.green.bold('║'));
    console.log(chalk.green.bold('║') + chalk.white(` 🏰 Servers: ${guildCount}`.padEnd(60)) + chalk.green.bold('║'));
    console.log(chalk.green.bold('║') + chalk.white(` 👥 Users: ${userCount}`.padEnd(60)) + chalk.green.bold('║'));
    console.log(chalk.green.bold('║') + chalk.white(` 🕐 Started: ${new Date().toLocaleString('de-DE')}`.padEnd(60)) + chalk.green.bold('║'));
    console.log(chalk.green.bold('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
  }

  // 📈 STATISTICS SUMMARY
  stats() {
    const uptime = this.getUptime();
    console.log('');
    console.log(chalk.cyan.bold('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.cyan.bold('║') + chalk.white.bold('                      📊 BOT STATISTICS                      ') + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('╠══════════════════════════════════════════════════════════════╣'));
    console.log(chalk.cyan.bold('║') + chalk.white(` ⏱️  Uptime: ${uptime}`.padEnd(60)) + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('║') + chalk.white(` ⚡ Commands: ${this.logCounts.commands}`.padEnd(60)) + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('║') + chalk.green(` ✅ Success: ${this.logCounts.success}`.padEnd(60)) + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('║') + chalk.blue(` ℹ️  Info: ${this.logCounts.info}`.padEnd(60)) + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('║') + chalk.yellow(` ⚠️  Warnings: ${this.logCounts.warning}`.padEnd(60)) + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('║') + chalk.red(` ❌ Errors: ${this.logCounts.error}`.padEnd(60)) + chalk.cyan.bold('║'));
    console.log(chalk.cyan.bold('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
  }

  // 🎯 SEPARATOR
  separator() {
    console.log(chalk.gray('─'.repeat(64)));
  }

  // Legacy compatibility
  logInfo(msg) { this.info(msg); }
  logError(msg, err) { this.error(msg, err ? err.message : null); }
}

module.exports = new Logger();