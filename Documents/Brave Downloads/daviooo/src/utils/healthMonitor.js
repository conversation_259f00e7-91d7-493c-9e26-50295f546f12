const chalk = require('chalk');

// 🎨 ENTERPRISE HEALTH MONITORING SYSTEM
class HealthMonitor {
  constructor() {
    this.metrics = {
      startTime: Date.now(),
      commandsExecuted: 0,
      errorsEncountered: 0,
      memoryPeaks: [],
      responseTimeHistory: [],
      lastHealthCheck: Date.now()
    };
    
    this.thresholds = {
      memoryWarning: 500, // MB
      memoryCritical: 1000, // MB
      responseTimeWarning: 5000, // ms
      responseTimeCritical: 10000, // ms
      errorRateWarning: 0.05, // 5%
      errorRateCritical: 0.1 // 10%
    };

    this.startMonitoring();
  }

  // 🎨 START CONTINUOUS MONITORING
  startMonitoring() {
    // Health check every 2 minutes
    setInterval(() => {
      this.performHealthCheck();
    }, 2 * 60 * 1000);

    // Memory monitoring every 30 seconds
    setInterval(() => {
      this.monitorMemory();
    }, 30 * 1000);

    // Silent health monitor initialization
  }

  // 🎨 COMPREHENSIVE HEALTH CHECK
  async performHealthCheck() {
    const currentTime = Date.now();
    const uptime = currentTime - this.metrics.startTime;
    const memoryUsage = process.memoryUsage();
    const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    
    // Calculate error rate
    const errorRate = this.metrics.commandsExecuted > 0 ? 
      this.metrics.errorsEncountered / this.metrics.commandsExecuted : 0;

    // Calculate average response time
    const avgResponseTime = this.metrics.responseTimeHistory.length > 0 ?
      this.metrics.responseTimeHistory.reduce((a, b) => a + b, 0) / this.metrics.responseTimeHistory.length : 0;

    // Determine health status
    let healthStatus = 'EXCELLENT';
    let statusColor = chalk.green;
    let statusEmoji = '💚';

    if (memoryMB > this.thresholds.memoryCritical || 
        errorRate > this.thresholds.errorRateCritical ||
        avgResponseTime > this.thresholds.responseTimeCritical) {
      healthStatus = 'CRITICAL';
      statusColor = chalk.red;
      statusEmoji = '🔴';
    } else if (memoryMB > this.thresholds.memoryWarning || 
               errorRate > this.thresholds.errorRateWarning ||
               avgResponseTime > this.thresholds.responseTimeWarning) {
      healthStatus = 'WARNING';
      statusColor = chalk.yellow;
      statusEmoji = '🟡';
    }

    // Beautiful health report
    console.log('\n╔══════════════════════════════════════════════════════════════╗');
    console.log('║                    🏥 HEALTH MONITOR REPORT                 ║');
    console.log('╠══════════════════════════════════════════════════════════════╣');
    console.log(`║ ${statusEmoji} Status: ${statusColor(healthStatus.padEnd(47))} ║`);
    console.log(`║ 💾 Memory: ${memoryMB}MB${' '.repeat(47 - memoryMB.toString().length)} ║`);
    console.log(`║ ⚡ Commands: ${this.metrics.commandsExecuted.toString().padEnd(44)} ║`);
    console.log(`║ 🚨 Errors: ${this.metrics.errorsEncountered.toString().padEnd(46)} ║`);
    console.log(`║ 📊 Error Rate: ${(errorRate * 100).toFixed(2)}%${' '.repeat(40)} ║`);
    console.log(`║ ⏱️ Avg Response: ${avgResponseTime.toFixed(0)}ms${' '.repeat(37)} ║`);
    console.log(`║ 🕐 Uptime: ${this.formatUptime(uptime).padEnd(45)} ║`);
    console.log('╚══════════════════════════════════════════════════════════════╝\n');

    this.metrics.lastHealthCheck = currentTime;

    // Alert if critical
    if (healthStatus === 'CRITICAL') {
      this.sendCriticalAlert(memoryMB, errorRate, avgResponseTime);
    }
  }

  // 🎨 MEMORY MONITORING
  monitorMemory() {
    const memoryUsage = process.memoryUsage();
    const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    
    this.metrics.memoryPeaks.push({
      timestamp: Date.now(),
      memory: memoryMB
    });

    // Keep only last 100 readings
    if (this.metrics.memoryPeaks.length > 100) {
      this.metrics.memoryPeaks.shift();
    }

    // Silent memory monitoring - no spam alerts
  }

  // 🎨 TRACK COMMAND EXECUTION
  trackCommand(success = true, responseTime = 0) {
    this.metrics.commandsExecuted++;
    
    if (!success) {
      this.metrics.errorsEncountered++;
    }

    if (responseTime > 0) {
      this.metrics.responseTimeHistory.push(responseTime);
      
      // Keep only last 50 response times
      if (this.metrics.responseTimeHistory.length > 50) {
        this.metrics.responseTimeHistory.shift();
      }
    }
  }

  // 🎨 SEND CRITICAL ALERTS
  sendCriticalAlert(memory, errorRate, responseTime) {
    console.log(chalk.red.bold('\n🚨 CRITICAL SYSTEM ALERT 🚨'));
    console.log(chalk.red('System performance has degraded beyond acceptable thresholds:'));
    
    if (memory > this.thresholds.memoryCritical) {
      console.log(chalk.red(`  • Memory usage: ${memory}MB (Critical: ${this.thresholds.memoryCritical}MB)`));
    }
    
    if (errorRate > this.thresholds.errorRateCritical) {
      console.log(chalk.red(`  • Error rate: ${(errorRate * 100).toFixed(2)}% (Critical: ${this.thresholds.errorRateCritical * 100}%)`));
    }
    
    if (responseTime > this.thresholds.responseTimeCritical) {
      console.log(chalk.red(`  • Response time: ${responseTime.toFixed(0)}ms (Critical: ${this.thresholds.responseTimeCritical}ms)`));
    }
    
    console.log(chalk.red('Immediate attention required!\n'));
  }

  // 🎨 FORMAT UPTIME
  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  // 🎨 GET CURRENT METRICS
  getMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.metrics.startTime,
      currentMemory: Math.round(process.memoryUsage().heapUsed / 1024 / 1024)
    };
  }

  // 🎨 RESET METRICS
  resetMetrics() {
    this.metrics = {
      startTime: Date.now(),
      commandsExecuted: 0,
      errorsEncountered: 0,
      memoryPeaks: [],
      responseTimeHistory: [],
      lastHealthCheck: Date.now()
    };
    
    console.log(chalk.green('🔄 Health Monitor metrics reset'));
  }
}

module.exports = new HealthMonitor();
