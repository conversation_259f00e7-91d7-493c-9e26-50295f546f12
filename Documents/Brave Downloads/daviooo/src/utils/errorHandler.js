// 🚨 PROFESSIONAL ERROR HANDLER - ACADEMIC EXCELLENCE
// Comprehensive error handling with logging, reporting, and recovery

const logger = require('./logger');
const performanceMonitor = require('./performanceMonitor');

class ErrorHandler {
  constructor() {
    this.errorCounts = new Map();
    this.criticalErrors = [];
    this.setupGlobalHandlers();
  }

  setupGlobalHandlers() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.handleCriticalError('Uncaught Exception', error);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.handleCriticalError('Unhandled Promise Rejection', reason);
    });

    // Handle warnings
    process.on('warning', (warning) => {
      logger.warning(`Node.js Warning: ${warning.message}`, warning.stack);
    });

    logger.success('Global error handlers initialized');
  }

  handleCriticalError(type, error) {
    const errorInfo = {
      type,
      message: error.message || error,
      stack: error.stack || 'No stack trace available',
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      uptime: process.uptime()
    };

    this.criticalErrors.push(errorInfo);
    
    logger.error(`CRITICAL ERROR - ${type}`, error.message || error);
    logger.error('Stack trace:', error.stack || 'No stack trace');
    
    // Record in performance monitor
    performanceMonitor.recordError(error);

    // Keep only last 50 critical errors
    if (this.criticalErrors.length > 50) {
      this.criticalErrors.shift();
    }

    // For critical errors, we might want to restart or alert admins
    if (type === 'Uncaught Exception') {
      logger.error('Process will exit due to uncaught exception');
      process.exit(1);
    }
  }

  // Handle Discord.js errors
  handleDiscordError(error, context = {}) {
    const errorKey = `${error.name || 'Unknown'}_${error.code || 'NoCode'}`;
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);

    // Log based on error severity
    if (this.isCriticalDiscordError(error)) {
      logger.error(`Discord Critical Error: ${error.message}`, error.stack);
    } else if (this.isWarningDiscordError(error)) {
      logger.warning(`Discord Warning: ${error.message}`);
    } else {
      logger.info(`Discord Info: ${error.message}`);
    }

    // Record in performance monitor
    performanceMonitor.recordError(error);

    return this.getErrorResponse(error, context);
  }

  // Handle command errors
  handleCommandError(error, interaction, commandName) {
    const errorInfo = {
      command: commandName,
      user: interaction.user.tag,
      guild: interaction.guild?.name || 'DM',
      error: error.message,
      timestamp: new Date().toISOString()
    };

    logger.error(`Command Error [${commandName}]: ${error.message}`, error.stack);
    
    // Record in performance monitor
    performanceMonitor.recordError(error);

    // Track command-specific errors
    const commandErrorKey = `command_${commandName}`;
    const count = this.errorCounts.get(commandErrorKey) || 0;
    this.errorCounts.set(commandErrorKey, count + 1);

    return this.getCommandErrorResponse(error, commandName);
  }

  // Handle database errors
  handleDatabaseError(error, operation = 'unknown') {
    logger.error(`Database Error [${operation}]: ${error.message}`, error.stack);
    
    // Record in performance monitor
    performanceMonitor.recordError(error);

    // Track database-specific errors
    const dbErrorKey = `database_${operation}`;
    const count = this.errorCounts.get(dbErrorKey) || 0;
    this.errorCounts.set(dbErrorKey, count + 1);

    return {
      success: false,
      error: 'Database operation failed',
      details: this.isDevelopment() ? error.message : 'Internal server error'
    };
  }

  // Check if Discord error is critical
  isCriticalDiscordError(error) {
    const criticalCodes = [
      50001, // Missing Access
      50013, // Missing Permissions
      50035, // Invalid Form Body
      40001, // Unauthorized
      40002, // Account verification required
      40003  // Rate limited
    ];

    return criticalCodes.includes(error.code) || 
           error.message.includes('TOKEN') ||
           error.message.includes('UNAUTHORIZED');
  }

  // Check if Discord error is warning level
  isWarningDiscordError(error) {
    const warningCodes = [
      10003, // Unknown Channel
      10004, // Unknown Guild
      10008, // Unknown Message
      10011, // Unknown Role
      10013, // Unknown User
      50006  // Cannot send empty message
    ];

    return warningCodes.includes(error.code);
  }

  // Get appropriate error response for Discord errors
  getErrorResponse(error, context) {
    switch (error.code) {
      case 50013:
        return {
          title: '❌ Missing Permissions',
          description: 'I don\'t have the required permissions to perform this action.',
          color: 0xff0000
        };
      
      case 50001:
        return {
          title: '❌ Missing Access',
          description: 'I don\'t have access to the specified resource.',
          color: 0xff0000
        };
      
      case 10003:
        return {
          title: '❌ Channel Not Found',
          description: 'The specified channel could not be found.',
          color: 0xff0000
        };
      
      case 10013:
        return {
          title: '❌ User Not Found',
          description: 'The specified user could not be found.',
          color: 0xff0000
        };
      
      default:
        return {
          title: '❌ Error',
          description: 'An unexpected error occurred. Please try again later.',
          color: 0xff0000,
          fields: this.isDevelopment() ? [
            {
              name: 'Error Details',
              value: `\`\`\`${error.message}\`\`\``,
              inline: false
            }
          ] : []
        };
    }
  }

  // Get appropriate error response for command errors
  getCommandErrorResponse(error, commandName) {
    return {
      title: '❌ Command Error',
      description: `An error occurred while executing the \`${commandName}\` command.`,
      color: 0xff0000,
      fields: [
        {
          name: 'What happened?',
          value: 'The command encountered an unexpected error and could not complete.',
          inline: false
        },
        {
          name: 'What can you do?',
          value: '• Try running the command again\n• Check if you have the required permissions\n• Contact an administrator if the problem persists',
          inline: false
        }
      ],
      footer: {
        text: `Error ID: ${Date.now().toString(36)}`
      },
      timestamp: new Date().toISOString()
    };
  }

  // Get error statistics
  getErrorStats() {
    const totalErrors = Array.from(this.errorCounts.values()).reduce((a, b) => a + b, 0);
    const topErrors = Array.from(this.errorCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    return {
      totalErrors,
      uniqueErrors: this.errorCounts.size,
      criticalErrors: this.criticalErrors.length,
      topErrors: topErrors.map(([type, count]) => ({ type, count })),
      recentCritical: this.criticalErrors.slice(-5)
    };
  }

  // Reset error statistics
  resetStats() {
    this.errorCounts.clear();
    this.criticalErrors = [];
    logger.info('Error statistics reset');
  }

  // Check if running in development mode
  isDevelopment() {
    return process.env.NODE_ENV === 'development';
  }

  // Generate error report
  generateReport() {
    const stats = this.getErrorStats();
    const performance = performanceMonitor.getMetrics();

    return {
      timestamp: new Date().toISOString(),
      uptime: performance.uptime,
      errorStats: stats,
      systemHealth: {
        memoryUsage: performance.memory.percentage,
        cpuUsage: performance.cpu.usage,
        totalCommands: performance.commands.total
      }
    };
  }
}

module.exports = new ErrorHandler();
