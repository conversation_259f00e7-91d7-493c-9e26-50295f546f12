// 🔒 PROFESSIONAL SECURITY MANAGER - ACADEMIC EXCELLENCE
// Advanced security features with rate limiting, permission checks, and threat detection

const logger = require('./logger');

class SecurityManager {
  constructor() {
    this.rateLimits = new Map();
    this.suspiciousActivity = new Map();
    this.blockedUsers = new Set();
    this.adminUsers = new Set();
    this.trustedGuilds = new Set();
    
    // Rate limit configurations
    this.limits = {
      commands: { max: 10, window: 60000 }, // 10 commands per minute
      messages: { max: 20, window: 60000 }, // 20 messages per minute
      reactions: { max: 30, window: 60000 }  // 30 reactions per minute
    };

    this.setupSecurityMonitoring();
  }

  setupSecurityMonitoring() {
    // Clean up old rate limit entries every 5 minutes
    setInterval(() => {
      this.cleanupRateLimits();
    }, 300000);

    // Reset suspicious activity tracking every hour
    setInterval(() => {
      this.suspiciousActivity.clear();
    }, 3600000);

    logger.success('Security monitoring initialized');
  }

  // Check if user is rate limited
  checkRateLimit(userId, type = 'commands') {
    const key = `${userId}_${type}`;
    const now = Date.now();
    const limit = this.limits[type];

    if (!this.rateLimits.has(key)) {
      this.rateLimits.set(key, []);
    }

    const userLimits = this.rateLimits.get(key);
    
    // Remove old entries outside the time window
    const validEntries = userLimits.filter(timestamp => now - timestamp < limit.window);
    this.rateLimits.set(key, validEntries);

    // Check if user exceeds rate limit
    if (validEntries.length >= limit.max) {
      this.recordSuspiciousActivity(userId, `Rate limit exceeded for ${type}`);
      logger.warning(`Rate limit exceeded: ${userId} for ${type}`);
      return false;
    }

    // Add current timestamp
    validEntries.push(now);
    return true;
  }

  // Record suspicious activity
  recordSuspiciousActivity(userId, reason) {
    if (!this.suspiciousActivity.has(userId)) {
      this.suspiciousActivity.set(userId, []);
    }

    const activities = this.suspiciousActivity.get(userId);
    activities.push({
      reason,
      timestamp: Date.now()
    });

    // Auto-block users with too many suspicious activities
    if (activities.length >= 5) {
      this.blockUser(userId, 'Automatic block due to suspicious activity');
    }
  }

  // Check user permissions
  checkPermissions(interaction, requiredPermissions = []) {
    const { user, member, guild } = interaction;

    // Check if user is blocked
    if (this.blockedUsers.has(user.id)) {
      logger.warning(`Blocked user attempted command: ${user.tag}`);
      return {
        allowed: false,
        reason: 'User is blocked from using bot commands'
      };
    }

    // Check if guild is trusted (if applicable)
    if (guild && !this.trustedGuilds.has(guild.id) && requiredPermissions.includes('TRUSTED_GUILD')) {
      return {
        allowed: false,
        reason: 'Command not available in this server'
      };
    }

    // Check Discord permissions
    if (member && requiredPermissions.length > 0) {
      const hasPermissions = requiredPermissions.every(permission => 
        member.permissions.has(permission)
      );

      if (!hasPermissions) {
        return {
          allowed: false,
          reason: 'Insufficient permissions'
        };
      }
    }

    // Check admin status for admin commands
    if (requiredPermissions.includes('BOT_ADMIN') && !this.isAdmin(user.id)) {
      return {
        allowed: false,
        reason: 'Bot administrator access required'
      };
    }

    return { allowed: true };
  }

  // Validate command input
  validateInput(input, type = 'general') {
    const validations = {
      general: {
        maxLength: 2000,
        forbiddenPatterns: [
          /discord\.gg\/[a-zA-Z0-9]+/gi, // Discord invite links
          /https?:\/\/[^\s]+/gi,         // Suspicious URLs
          /@everyone|@here/gi            // Mass mentions
        ]
      },
      username: {
        maxLength: 32,
        forbiddenPatterns: [
          /[<>@#&!]/g,                   // Special characters
          /discord/gi,                   // Discord impersonation
          /admin|mod|owner/gi            // Role impersonation
        ]
      },
      message: {
        maxLength: 2000,
        forbiddenPatterns: [
          /(.)\1{10,}/g,                 // Spam characters
          /[^\x00-\x7F]{20,}/g          // Excessive unicode
        ]
      }
    };

    const validation = validations[type] || validations.general;

    // Check length
    if (input.length > validation.maxLength) {
      return {
        valid: false,
        reason: `Input too long (max ${validation.maxLength} characters)`
      };
    }

    // Check forbidden patterns
    for (const pattern of validation.forbiddenPatterns) {
      if (pattern.test(input)) {
        return {
          valid: false,
          reason: 'Input contains forbidden content'
        };
      }
    }

    return { valid: true };
  }

  // Block user
  blockUser(userId, reason = 'Manual block') {
    this.blockedUsers.add(userId);
    logger.warning(`User blocked: ${userId} - ${reason}`);
    
    // Could also save to database for persistence
    this.saveBlockedUser(userId, reason);
  }

  // Unblock user
  unblockUser(userId) {
    this.blockedUsers.delete(userId);
    logger.info(`User unblocked: ${userId}`);
    
    // Remove from database
    this.removeBlockedUser(userId);
  }

  // Add admin user
  addAdmin(userId) {
    this.adminUsers.add(userId);
    logger.info(`Admin added: ${userId}`);
  }

  // Remove admin user
  removeAdmin(userId) {
    this.adminUsers.delete(userId);
    logger.info(`Admin removed: ${userId}`);
  }

  // Check if user is admin
  isAdmin(userId) {
    return this.adminUsers.has(userId);
  }

  // Add trusted guild
  addTrustedGuild(guildId) {
    this.trustedGuilds.add(guildId);
    logger.info(`Trusted guild added: ${guildId}`);
  }

  // Remove trusted guild
  removeTrustedGuild(guildId) {
    this.trustedGuilds.delete(guildId);
    logger.info(`Trusted guild removed: ${guildId}`);
  }

  // Clean up old rate limit entries
  cleanupRateLimits() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, timestamps] of this.rateLimits.entries()) {
      const validTimestamps = timestamps.filter(timestamp => 
        now - timestamp < Math.max(...Object.values(this.limits).map(l => l.window))
      );

      if (validTimestamps.length === 0) {
        this.rateLimits.delete(key);
        cleaned++;
      } else {
        this.rateLimits.set(key, validTimestamps);
      }
    }

    if (cleaned > 0) {
      logger.info(`Cleaned up ${cleaned} old rate limit entries`);
    }
  }

  // Get security statistics
  getSecurityStats() {
    return {
      blockedUsers: this.blockedUsers.size,
      adminUsers: this.adminUsers.size,
      trustedGuilds: this.trustedGuilds.size,
      activeRateLimits: this.rateLimits.size,
      suspiciousActivities: this.suspiciousActivity.size,
      rateLimitConfig: this.limits
    };
  }

  // Generate security report
  generateSecurityReport() {
    const stats = this.getSecurityStats();
    const recentSuspicious = Array.from(this.suspiciousActivity.entries())
      .map(([userId, activities]) => ({
        userId,
        activityCount: activities.length,
        lastActivity: Math.max(...activities.map(a => a.timestamp))
      }))
      .sort((a, b) => b.lastActivity - a.lastActivity)
      .slice(0, 10);

    return {
      timestamp: new Date().toISOString(),
      statistics: stats,
      recentSuspiciousActivity: recentSuspicious,
      blockedUsersList: Array.from(this.blockedUsers),
      adminUsersList: Array.from(this.adminUsers)
    };
  }

  // Save blocked user to database (placeholder)
  async saveBlockedUser(userId, reason) {
    // Implementation would save to database
    // For now, just log it
    logger.info(`Saving blocked user to database: ${userId}`);
  }

  // Remove blocked user from database (placeholder)
  async removeBlockedUser(userId) {
    // Implementation would remove from database
    // For now, just log it
    logger.info(`Removing blocked user from database: ${userId}`);
  }

  // Load security data from database (placeholder)
  async loadSecurityData() {
    // Implementation would load from database
    // For now, just log it
    logger.info('Loading security data from database');
  }
}

module.exports = new SecurityManager();
