// Professional Slash Command Service
const { REST, Routes } = require('discord.js');
const Logger = require('../utils/Logger');

class SlashCommandService {
  constructor(token, clientId) {
    this.rest = new REST({ version: '10' }).setToken(token);
    this.clientId = clientId;
  }

  async deployCommands(commands) {
    try {
      const commandData = this.prepareCommandData(commands);
      
      Logger.info(`Deploying ${commandData.length} slash commands...`);
      
      await this.rest.put(
        Routes.applicationCommands(this.clientId),
        { body: commandData }
      );

      Logger.success(`Successfully deployed ${commandData.length} slash commands`);
      return true;
    } catch (error) {
      Logger.error(`Slash command deployment failed: ${error.message}`);
      return false;
    }
  }

  prepareCommandData(commands) {
    return Array.from(commands.values()).map(command => {
      const data = command.data.toJSON();
      
      // Validate and clean command data
      if (data.description && data.description.length > 100) {
        data.description = data.description.substring(0, 97) + '...';
      }
      
      // Validate options
      if (data.options) {
        data.options.forEach(option => {
          if (option.description && option.description.length > 100) {
            option.description = option.description.substring(0, 97) + '...';
          }
        });
      }
      
      return data;
    });
  }

  async deleteAllCommands() {
    try {
      await this.rest.put(
        Routes.applicationCommands(this.clientId),
        { body: [] }
      );
      Logger.success('All slash commands deleted');
    } catch (error) {
      Logger.error(`Failed to delete commands: ${error.message}`);
    }
  }
}

module.exports = SlashCommandService;