// Professional Monitoring Service
const Logger = require('../utils/Logger');

class MonitoringService {
  constructor(client) {
    this.client = client;
    this.interval = null;
    this.startTime = Date.now();
  }

  start() {
    // Monitor every 30 minutes
    this.interval = setInterval(() => {
      this.logSystemStats();
    }, 1800000);
    
    Logger.info('System monitoring started');
  }

  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      Logger.info('System monitoring stopped');
    }
  }

  logSystemStats() {
    const memory = (process.memoryUsage().heapUsed / 1024 / 1024).toFixed(1);
    const uptime = ((Date.now() - this.startTime) / 3600000).toFixed(1);
    const ping = this.client.ws.ping;
    
    Logger.system(memory, uptime, ping);
  }

  getStats() {
    const memory = process.memoryUsage();
    const uptime = process.uptime();
    
    return {
      memory: {
        used: Math.round(memory.heapUsed / 1024 / 1024),
        total: Math.round(memory.heapTotal / 1024 / 1024),
        external: Math.round(memory.external / 1024 / 1024)
      },
      uptime: Math.round(uptime),
      ping: this.client.ws.ping,
      guilds: this.client.guilds.cache.size,
      users: this.client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0),
      commands: this.client.commands?.size || 0
    };
  }
}

module.exports = MonitoringService;