// Description: Initializes the Discord client, loads commands/events, connects to MongoDB
require('dotenv').config();

// Check if all required environment variables are set
if (!process.env.TOKEN || !process.env.CLIENT_ID || !process.env.MONGO_URI) {
  console.error('❌ .env must contain TOKEN, CLIENT_ID and MONGO_URI');
  process.exit(1);
}

const {
  Client,
  Collection,
  GatewayIntentBits,
  Partials,
  Events,
  REST,
  Routes
} = require('discord.js');
const PerfectSubcommandHandler = require('./handlers/subcommandHandler');
const PerfectContextMenuHandler = require('./handlers/contextMenuHandler');
const path                 = require('path');
const { connectDB }        = require('./utils/database');
const { loadEvents }       = require('./loaders/eventLoader');
const { handleAutomod }    = require('./handlers/automodHandler');
const { scheduleGiveaways }= require('./handlers/giveawayHandler');
const { banner }           = require('./utils/banner');
const { logError, logInfo }= require('./utils/logger');

// Display ASCII banner
banner('Davio Bot');

// Create the client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers
  ],
  partials: [Partials.Message, Partials.Channel, Partials.Reaction]
});

// Initialize Perfect Professional Subcommand Handler
const subcommandHandler = new PerfectSubcommandHandler(client);
subcommandHandler.loadCommands(path.join(__dirname, 'commands'));

// Initialize Perfect Context Menu Handler
const contextMenuHandler = new PerfectContextMenuHandler(client);
contextMenuHandler.loadContextMenus(path.join(__dirname, 'commands'));

// Store commands in client for easy access
client.commands = subcommandHandler.getAllCommands();

// Load Events
loadEvents(client);

// Handle slash command interactions
client.on(Events.InteractionCreate, async interaction => {
  if (interaction.isChatInputCommand()) {
    const command = subcommandHandler.getCommand(interaction.commandName);
    if (!command) {
      return interaction.reply({
        content: '❌ Command not found!',
        ephemeral: true
      });
    }

    try {
      await command.execute(interaction);
    } catch (error) {
      logError('Error executing command:', error);

      const errorMessage = {
        content: '❌ An error occurred while executing this command!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  // Handle context menu interactions
  else if (interaction.isContextMenuCommand()) {
    const contextMenu = contextMenuHandler.getContextMenu(interaction.commandName);
    if (!contextMenu) {
      return interaction.reply({
        content: '❌ Context menu command not found!',
        ephemeral: true
      });
    }

    try {
      await contextMenu.execute(interaction);
      console.log(`✅ Executed context menu: ${interaction.commandName}`);
    } catch (error) {
      logError('Error executing context menu:', error);

      const errorMessage = {
        content: '❌ An error occurred while executing this context menu!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }
});

// Automod Handler
client.on(Events.MessageCreate, message => handleAutomod(message, client));

// Connect to MongoDB
connectDB()
  .then(() => logInfo('🚀 Connected to MongoDB'))
  .catch(err => logError('❌ MongoDB connection failed', err));

// On ready event: deploy commands & schedule giveaways
client.once(Events.ClientReady, async () => {
  logInfo(`✅ Logged in as ${client.user.tag}`);

  // Register Slash Commands and Context Menus
  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  try {
    logInfo('🌐 Registering commands with Perfect Handlers...');

    // First, clear all existing commands to avoid conflicts
    logInfo('🧹 Clearing existing commands...');
    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: [] }
    );
    logInfo('✅ Existing commands cleared');

    // Get slash commands
    const allSlashCommands = subcommandHandler.getCommandsJSON();
    logInfo(`📋 Found ${allSlashCommands.length} slash commands available`);

    // Get context menus
    const allContextMenus = contextMenuHandler.getContextMenusJSON();
    logInfo(`🎯 Found ${allContextMenus.length} context menus available`);

    // DEVELOPMENT BOT LIMIT: Only register the most important 5 commands
    const prioritySlashCommands = allSlashCommands.filter(cmd =>
      ['bot', 'moderation', 'economy'].includes(cmd.name)
    ).slice(0, 3); // Top 3 slash commands

    const priorityContextMenus = allContextMenus.slice(0, 2); // Top 2 context menus

    // Combine priority commands (max 5 for development bot)
    const allCommands = [...prioritySlashCommands, ...priorityContextMenus];
    logInfo(`🚀 Priority commands selected: ${allCommands.length}/5 (Development Bot Limit)`);
    logInfo(`   • Priority Slash: ${prioritySlashCommands.map(c => c.name).join(', ')}`);
    logInfo(`   • Priority Context: ${priorityContextMenus.map(c => c.name).join(', ')}`);

    // Log command statistics
    const stats = subcommandHandler.getCommandStats();
    if (Object.keys(stats).length > 0) {
      logInfo('📊 Command usage statistics available');
    }

    // Register all commands
    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: allCommands }
    );

    logInfo('✅ Priority commands registered successfully');
    logInfo(`   • Slash Commands: ${prioritySlashCommands.length}`);
    logInfo(`   • Context Menus: ${priorityContextMenus.length}`);
  } catch (error) {
    logError('❌ Failed to register commands', error);
  }

  // Schedule Giveaways
  scheduleGiveaways(client);
});

// Graceful shutdown & Exception Handling
process.on('SIGINT', () => {
  logInfo('SIGINT received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('SIGTERM', () => {
  logInfo('SIGTERM received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('uncaughtException', (err) => {
  logError('UNCAUGHT EXCEPTION', err);
  process.exit(1);
});

// Login the bot
client.login(process.env.TOKEN)
  .catch(err => logError('❌ Login failed', err));