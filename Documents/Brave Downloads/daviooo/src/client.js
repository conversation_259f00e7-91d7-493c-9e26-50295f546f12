// Description: Initializes the Discord client, loads commands/events, connects to MongoDB
require('dotenv').config();

// Check if all required environment variables are set
if (!process.env.TOKEN || !process.env.CLIENT_ID || !process.env.MONGO_URI) {
  console.error('❌ .env must contain TOKEN, CLIENT_ID and MONGO_URI');
  process.exit(1);
}

const {
  Client,
  Collection,
  GatewayIntentBits,
  Partials,
  Events,
  REST,
  Routes
} = require('discord.js');
const PerfectSubcommandHandler = require('./handlers/subcommandHandler');
const PerfectContextMenuHandler = require('./handlers/contextMenuHandler');
const logger = require('./utils/consoleLogger');
const path                 = require('path');
const { connectDB }        = require('./utils/database');
const { loadEvents }       = require('./loaders/eventLoader');
const { handleAutomod }    = require('./handlers/automodHandler');
const { scheduleGiveaways }= require('./handlers/giveawayHandler');
const GiveawayButtons = require('./modules/giveaway/GiveawayButtons');
// Banner replaced with logger header
// Logger is imported above

// Display beautiful header
logger.clear();

// Create the client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers
  ],
  partials: [Partials.Message, Partials.Channel, Partials.Reaction]
});

// Initialize Perfect Professional Subcommand Handler
const subcommandHandler = new PerfectSubcommandHandler(client);
subcommandHandler.loadCommands(path.join(__dirname, 'commands'));

// Initialize Perfect Context Menu Handler
const contextMenuHandler = new PerfectContextMenuHandler(client);
contextMenuHandler.loadContextMenus(path.join(__dirname, 'commands'));

// Store commands in client for easy access
client.commands = subcommandHandler.getAllCommands();

// Load Events
loadEvents(client);

// Handle slash command interactions
client.on(Events.InteractionCreate, async interaction => {
  if (interaction.isChatInputCommand()) {
    const command = subcommandHandler.getCommand(interaction.commandName);
    if (!command) {
      return interaction.reply({
        content: '❌ Command not found!',
        ephemeral: true
      });
    }

    try {
      await command.execute(interaction);
    } catch (error) {
      logger.error(`Error executing command: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this command!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  // Handle context menu interactions
  else if (interaction.isContextMenuCommand()) {
    const contextMenu = contextMenuHandler.getContextMenu(interaction.commandName);
    if (!contextMenu) {
      return interaction.reply({
        content: '❌ Context menu command not found!',
        ephemeral: true
      });
    }

    try {
      await contextMenu.execute(interaction);
      logger.execute(`Context Menu: ${interaction.commandName}`, 0);
    } catch (error) {
      logger.error(`Error executing context menu: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this context menu!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }
});

// Automod Handler
client.on(Events.MessageCreate, message => handleAutomod(message, client));

// Connect to MongoDB
connectDB()
  .then(() => logger.success('Connected to MongoDB'))
  .catch(err => logger.error(`MongoDB connection failed: ${err.message}`));

// On ready event: deploy commands & schedule giveaways
client.once(Events.ClientReady, async () => {
  logger.success(`Logged in as ${client.user.tag}`);
  logger.separator('COMMAND REGISTRATION');

  // Register Slash Commands and Context Menus
  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  try {
    logger.info('Registering commands with Perfect Handlers...');

    // First, clear all existing commands to avoid conflicts
    logger.info('Clearing existing commands...');
    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: [] }
    );
    logger.success('Existing commands cleared');

    // Get slash commands
    const allSlashCommands = subcommandHandler.getCommandsJSON();
    logger.info(`Found ${allSlashCommands.length} slash commands available`);

    // Get context menus
    const allContextMenus = contextMenuHandler.getContextMenusJSON();
    logger.info(`Found ${allContextMenus.length} context menus available`);

    // DEVELOPMENT BOT: Select only the most important commands (5 total limit)
    const prioritySlashCommands = allSlashCommands.filter(cmd =>
      ['moderation', 'bot'].includes(cmd.name)
    ).slice(0, 2); // Top 2 slash commands

    const priorityContextMenus = allContextMenus.filter(menu =>
      menu.name.includes('userinfo') || menu.name.includes('ban') || menu.name.includes('report')
    ).slice(0, 3); // Top 3 context menus

    // Combine priority commands (max 5 for development bot)
    const allCommands = [...prioritySlashCommands, ...priorityContextMenus];
    logger.info(`Selected priority commands: ${allCommands.length}/5 (Development Bot)`);
    logger.info(`Priority Slash: ${prioritySlashCommands.map(c => c.name).join(', ')}`);
    logger.info(`Priority Context: ${priorityContextMenus.map(c => c.name).join(', ')}`);

    // Register all commands
    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: allCommands }
    );

    logger.success('Priority commands registered successfully');
    logger.info(`Slash Commands: ${prioritySlashCommands.length}`);
    logger.info(`Context Menus: ${priorityContextMenus.length}`);

    // Show beautiful summary
    logger.summary();
  } catch (error) {
    logger.error(`Failed to register commands: ${error.message}`);
  }

  // Schedule Giveaways
  scheduleGiveaways(client);
});

// Graceful shutdown & Exception Handling
process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('uncaughtException', (err) => {
  logger.error(`UNCAUGHT EXCEPTION: ${err.message}`);
  process.exit(1);
});

// Login the bot
client.login(process.env.TOKEN)
  .catch(err => logger.error(`Login failed: ${err.message}`));