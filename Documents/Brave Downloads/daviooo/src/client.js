// Description: Initializes the Discord client, loads commands/events, connects to MongoDB
require('dotenv').config();

// Check if all required environment variables are set
if (!process.env.TOKEN || !process.env.CLIENT_ID || !process.env.MONGO_URI) {
  console.error('❌ .env must contain TOKEN, CLIENT_ID and MONGO_URI');
  process.exit(1);
}

const {
  Client,
  Collection,
  GatewayIntentBits,
  Partials,
  Events,
  REST,
  Routes
} = require('discord.js');
const SubcommandHandler    = require('./handlers/subcommandHandler');
const path                 = require('path');
const { connectDB }        = require('./utils/database');
const { loadEvents }       = require('./loaders/eventLoader');
const { handleAutomod }    = require('./handlers/automodHandler');
const { scheduleGiveaways }= require('./handlers/giveawayHandler');
const { banner }           = require('./utils/banner');
const { logError, logInfo }= require('./utils/logger');

// Display ASCII banner
banner('Davio Bot');

// Create the client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers
  ],
  partials: [Partials.Message, Partials.Channel, Partials.Reaction]
});

// Initialize Professional Subcommand Handler
const subcommandHandler = new SubcommandHandler();
subcommandHandler.loadCommands(path.join(__dirname, 'commands'));

// Store commands in client for easy access
client.commands = subcommandHandler.getAllCommands();

// Load Events
loadEvents(client);

// Handle slash command interactions
client.on(Events.InteractionCreate, async interaction => {
  if (!interaction.isChatInputCommand()) return;

  const command = subcommandHandler.getCommand(interaction.commandName);
  if (!command) {
    return interaction.reply({
      content: '❌ Command not found!',
      ephemeral: true
    });
  }

  try {
    await command.execute(interaction);
  } catch (error) {
    logError('Error executing command:', error);

    const errorMessage = {
      content: '❌ An error occurred while executing this command!',
      ephemeral: true
    };

    if (interaction.replied || interaction.deferred) {
      await interaction.followUp(errorMessage);
    } else {
      await interaction.reply(errorMessage);
    }
  }
});

// Automod Handler
client.on(Events.MessageCreate, message => handleAutomod(message, client));

// Connect to MongoDB
connectDB()
  .then(() => logInfo('🚀 Connected to MongoDB'))
  .catch(err => logError('❌ MongoDB connection failed', err));

// On ready event: deploy commands & schedule giveaways
client.once(Events.ClientReady, async () => {
  logInfo(`✅ Logged in as ${client.user.tag}`);

  // Register Slash Commands using Professional Subcommand Handler
  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  try {
    logInfo('🌐 Registering slash commands with Professional Subcommand Handler...');

    const commands = subcommandHandler.getCommandsJSON();
    logInfo(`📋 Found ${commands.length} category commands to register`);

    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: commands }
    );

    logInfo('✅ Slash commands registered successfully');
  } catch (error) {
    logError('❌ Failed to register slash commands', error);
  }

  // Schedule Giveaways
  scheduleGiveaways(client);
});

// Graceful shutdown & Exception Handling
process.on('SIGINT', () => {
  logInfo('SIGINT received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('SIGTERM', () => {
  logInfo('SIGTERM received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('uncaughtException', (err) => {
  logError('UNCAUGHT EXCEPTION', err);
  process.exit(1);
});

// Login the bot
client.login(process.env.TOKEN)
  .catch(err => logError('❌ Login failed', err));