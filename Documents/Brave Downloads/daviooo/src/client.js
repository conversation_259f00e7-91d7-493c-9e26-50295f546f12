// 🎯 PROFESSIONAL DISCORD BOT - ENTERPRISE GRADE
// Created by: Professional Developer
// Purpose: Production-Ready Discord Bot System
// Version: 4.0 - Optimized & Clean

require('dotenv').config();
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

// Environment validation
if (!process.env.TOKEN || !process.env.CLIENT_ID) {
  console.error(chalk.red('❌ Missing required environment variables: TOKEN and CLIENT_ID'));
  process.exit(1);
}

const {
  Client,
  Collection,
  GatewayIntentBits,
  Partials,
  Events,
  REST,
  Routes
} = require('discord.js');

// Professional Logger System
class Logger {
  static getTimestamp() {
    return new Date().toLocaleTimeString('de-DE');
  }

  static info(message) {
    console.log(chalk.blue(`ℹ️  [${this.getTimestamp()}] ${message}`));
  }

  static success(message) {
    console.log(chalk.green(`✅ [${this.getTimestamp()}] ${message}`));
  }

  static error(message) {
    console.log(chalk.red(`❌ [${this.getTimestamp()}] ${message}`));
  }

  static warn(message) {
    console.log(chalk.yellow(`⚠️  [${this.getTimestamp()}] ${message}`));
  }

  static command(commandName, user, guild) {
    console.log(chalk.cyan(`🔧 [${this.getTimestamp()}] Command: ${commandName} | User: ${user} | Guild: ${guild}`));
  }

  static startup() {
    console.log(chalk.magenta('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.magenta('║                    🤖 DAVIO BOT SYSTEM v4.0                ║'));
    console.log(chalk.magenta('║                Professional Discord Bot                      ║'));
    console.log(chalk.magenta('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
  }

  static ready(client) {
    console.log(chalk.green('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.green('║                    🎉 BOT ONLINE & READY                    ║'));
    console.log(chalk.green('╠══════════════════════════════════════════════════════════════╣'));
    console.log(chalk.green(`║ Bot: ${client.user.tag.padEnd(53)} ║`));
    console.log(chalk.green(`║ Servers: ${client.guilds.cache.size.toString().padEnd(50)} ║`));
    console.log(chalk.green(`║ Users: ${client.guilds.cache.reduce((a, g) => a + g.memberCount, 0).toString().padEnd(52)} ║`));
    console.log(chalk.green(`║ Commands: ${client.commands.size.toString().padEnd(49)} ║`));
    console.log(chalk.green('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
  }
}

// Professional Error Handling
process.on('unhandledRejection', (reason, promise) => {
  Logger.error(`Unhandled Rejection: ${reason}`);
  console.error('Promise:', promise);
});

process.on('uncaughtException', (err) => {
  Logger.error(`Uncaught Exception: ${err.message}`);
  console.error('Stack:', err.stack);
});

// Display startup banner
Logger.startup();

// Create Discord client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMessageReactions
  ],
  partials: [Partials.Message, Partials.Channel, Partials.Reaction, Partials.User]
});

client.commands = new Collection();

Logger.info('Initializing Discord client...');

// Command Loading System
function loadCommands() {
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    Logger.warn('Commands directory not found');
    return 0;
  }

  const commandFolders = fs.readdirSync(commandsPath);
  let loadedCount = 0;
  const categories = new Set();

  for (const folder of commandFolders) {
    const folderPath = path.join(commandsPath, folder);
    if (!fs.statSync(folderPath).isDirectory()) continue;

    const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
      try {
        const filePath = path.join(folderPath, file);
        delete require.cache[require.resolve(filePath)];
        const command = require(filePath);

        if (command.data && command.execute) {
          client.commands.set(command.data.name, command);
          categories.add(folder);
          loadedCount++;
        } else {
          Logger.warn(`Command ${file} missing required properties`);
        }
      } catch (error) {
        Logger.error(`Failed to load ${file}: ${error.message}`);
      }
    }
  }

  Logger.success(`Loaded ${loadedCount} commands from ${categories.size} categories`);
  return loadedCount;
}

// Load commands
const commandCount = loadCommands();

// Interaction Handler
client.on(Events.InteractionCreate, async interaction => {
  if (interaction.isChatInputCommand()) {
    const command = client.commands.get(interaction.commandName);
    
    if (!command) {
      return interaction.reply({
        content: '❌ Command not found!',
        ephemeral: true
      });
    }

    try {
      await command.execute(interaction);
    } catch (error) {
      Logger.error(`Command execution failed: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this command!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }
});

// Event Handlers - Only log important events
client.on(Events.MessageCreate, message => {
  // Only log bot mentions or commands to reduce spam
  if (!message.author.bot && message.mentions.has(client.user)) {
    Logger.info(`Bot mentioned by ${message.author.tag} in ${message.guild?.name || 'DM'}`);
  }
});

// Performance monitoring (reduced frequency)
let performanceInterval;

function startPerformanceMonitoring() {
  performanceInterval = setInterval(() => {
    const memUsage = process.memoryUsage();
    const uptime = Math.floor(process.uptime() / 60);
    
    Logger.info(`Performance - Memory: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB | Uptime: ${uptime}min | Guilds: ${client.guilds.cache.size}`);
  }, 600000); // Every 10 minutes instead of 5
}

// Ready Event
client.once(Events.ClientReady, async () => {
  Logger.ready(client);
  
  // Start performance monitoring
  startPerformanceMonitoring();

  // Register slash commands
  Logger.info('Registering slash commands...');
  
  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  
  try {
    const commands = Array.from(client.commands.values()).map(cmd => {
      const commandData = cmd.data.toJSON();
      
      // Validate command data
      if (commandData.description && commandData.description.length > 100) {
        commandData.description = commandData.description.substring(0, 97) + '...';
      }
      
      return commandData;
    });

    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: commands }
    );

    Logger.success(`Successfully registered ${commands.length} slash commands`);
  } catch (error) {
    Logger.error(`Failed to register commands: ${error.message}`);
  }
});

// Graceful shutdown
function gracefulShutdown(signal) {
  Logger.warn(`${signal} received - shutting down gracefully...`);
  
  if (performanceInterval) {
    clearInterval(performanceInterval);
  }
  
  client.destroy();
  process.exit(0);
}

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Login to Discord
client.login(process.env.TOKEN)
  .catch(err => {
    Logger.error(`Login failed: ${err.message}`);
    process.exit(1);
  });