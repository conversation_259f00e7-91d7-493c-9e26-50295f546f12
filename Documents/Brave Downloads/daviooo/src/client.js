// Description: Initializes the Discord client, loads commands/events, connects to MongoDB
require('dotenv').config();

// Check if all required environment variables are set
if (!process.env.TOKEN || !process.env.CLIENT_ID || !process.env.MONGO_URI) {
  console.error('❌ .env must contain TOKE<PERSON>, CLIENT_ID and MONGO_URI');
  process.exit(1);
}

const {
  Client,
  Collection,
  GatewayIntentBits,
  Partials,
  Events,
  REST,
  Routes
} = require('discord.js');
const PerfectSubcommandHandler = require('./handlers/subcommandHandler');
const PerfectContextMenuHandler = require('./handlers/contextMenuHandler');
const logger = require('./utils/consoleLogger');
const chalk = require('chalk');
const path                 = require('path');
const { connectDB }        = require('./utils/database');
const { loadEvents }       = require('./loaders/eventLoader');
const { handleAutomod }    = require('./handlers/automodHandler');
const { scheduleGiveaways }= require('./handlers/giveawayHandler');
const GiveawayButtons = require('./modules/giveaway/GiveawayButtons');
// Banner replaced with logger header
// Logger is imported above

// Display beautiful header
logger.clear();

// Create the client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers
  ],
  partials: [Partials.Message, Partials.Channel, Partials.Reaction]
});

// Initialize Perfect Professional Subcommand Handler
const subcommandHandler = new PerfectSubcommandHandler(client);
subcommandHandler.loadCommands(path.join(__dirname, 'commands'));

// Initialize Perfect Context Menu Handler
const contextMenuHandler = new PerfectContextMenuHandler(client);
contextMenuHandler.loadContextMenus(path.join(__dirname, 'commands'));

// Store commands in client for easy access
client.commands = subcommandHandler.getAllCommands();

// Load Events
loadEvents(client);

// Handle slash command interactions
client.on(Events.InteractionCreate, async interaction => {
  if (interaction.isChatInputCommand()) {
    const command = subcommandHandler.getCommand(interaction.commandName);
    if (!command) {
      return interaction.reply({
        content: '❌ Command not found!',
        ephemeral: true
      });
    }

    try {
      await command.execute(interaction);
    } catch (error) {
      logger.error(`Error executing command: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this command!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  // Handle context menu interactions
  else if (interaction.isContextMenuCommand()) {
    const contextMenu = contextMenuHandler.getContextMenu(interaction.commandName);
    if (!contextMenu) {
      return interaction.reply({
        content: '❌ Context menu command not found!',
        ephemeral: true
      });
    }

    try {
      await contextMenu.execute(interaction);
      logger.execute(`Context Menu: ${interaction.commandName}`, 0);
    } catch (error) {
      logger.error(`Error executing context menu: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this context menu!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  // Handle button interactions
  else if (interaction.isButton()) {
    // Handle giveaway buttons
    if (interaction.customId.startsWith('giveaway-')) {
      try {
        await GiveawayButtons.handleButtonInteraction(interaction);
      } catch (error) {
        logger.error(`Error handling giveaway button: ${error.message}`);

        const errorMessage = {
          content: '❌ An error occurred while processing this button!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      }
    }
  }
});

// Automod Handler
client.on(Events.MessageCreate, message => handleAutomod(message, client));

// Connect to MongoDB
connectDB()
  .then(() => {
    // Silent connection - will be shown in summary
  })
  .catch(err => logger.error(`MongoDB connection failed: ${err.message}`));

// On ready event: deploy commands & schedule giveaways
client.once(Events.ClientReady, async () => {
  logger.success(`Logged in as ${client.user.tag}`);

  // Register Slash Commands and Context Menus
  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  try {
    // Get all commands - limit for development
    const allSlashCommands = subcommandHandler.getCommandsJSON();
    const allContextMenus = contextMenuHandler.getContextMenusJSON();

    // Select only essential commands to stay under limit
    const essentialSlash = allSlashCommands.filter(cmd =>
      ['bot', 'moderation'].includes(cmd.name)
    ).slice(0, 2);

    const essentialContext = allContextMenus.filter(menu =>
      menu.name.includes('ban') || menu.name.includes('userinfo')
    ).slice(0, 3);

    const allCommands = [...essentialSlash, ...essentialContext];

    // Register all commands
    await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: allCommands }
    );

    // Show registration success
    setTimeout(() => {
      logger.success('🚀 All commands registered successfully!');
      console.log('');

      // Show beautiful summary after all boxes are shown
      setTimeout(() => {
        logger.summary();
      }, 500);
    }, 2500);
  } catch (error) {
    logger.error(`Failed to register commands: ${error.message}`);
  }

  // Schedule Giveaways
  scheduleGiveaways(client);
});

// Graceful shutdown & Exception Handling
process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('uncaughtException', (err) => {
  logger.error(`UNCAUGHT EXCEPTION: ${err.message}`);
  process.exit(1);
});

// Login the bot
client.login(process.env.TOKEN)
  .catch(err => logger.error(`Login failed: ${err.message}`));