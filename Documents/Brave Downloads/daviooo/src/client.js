// Description: Initializes the Discord client, loads commands/events, connects to MongoDB
require('dotenv').config();

// Check if all required environment variables are set
if (!process.env.TOKEN || !process.env.CLIENT_ID || !process.env.MONGO_URI) {
  console.error('❌ .env must contain TOKE<PERSON>, CLIENT_ID and MONGO_URI');
  process.exit(1);
}

const {
  Client,
  Collection,
  GatewayIntentBits,
  Partials,
  Events,
  REST,
  Routes
} = require('discord.js');
const PerfectSubcommandHandler = require('./handlers/subcommandHandler');
const PerfectContextMenuHandler = require('./handlers/contextMenuHandler');
const logger = require('./utils/consoleLogger');
const path                 = require('path');
const { connectDB }        = require('./utils/database');
const { loadEvents }       = require('./loaders/eventLoader');
const { handleAutomod, handleRaidProtection } = require('./handlers/automodHandler');
const { scheduleGiveaways }= require('./handlers/giveawayHandler');
const GiveawayButtons = require('./modules/giveaway/GiveawayButtons');
// Banner replaced with logger header
// Logger is imported above

// Display professional startup banner
logger.showStartupBanner();

// Create the client
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers
  ],
  partials: [Partials.Message, Partials.Channel, Partials.Reaction]
});

// Initialize Perfect Professional Subcommand Handler
const subcommandHandler = new PerfectSubcommandHandler(client);
subcommandHandler.loadCommands(path.join(__dirname, 'commands'));

// Initialize Perfect Context Menu Handler
const contextMenuHandler = new PerfectContextMenuHandler(client);
contextMenuHandler.loadContextMenus(path.join(__dirname, 'commands'));

// Store commands in client for easy access
client.commands = subcommandHandler.commands;

// Load Events
loadEvents(client);

// Handle slash command interactions
client.on(Events.InteractionCreate, async interaction => {
  if (interaction.isChatInputCommand()) {
    const command = subcommandHandler.getCommand(interaction.commandName);
    if (!command) {
      return interaction.reply({
        content: '❌ Command not found!',
        ephemeral: true
      });
    }

    try {
      await command.execute(interaction);
    } catch (error) {
      logger.error(`Error executing command: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this command!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  // Handle context menu interactions
  else if (interaction.isContextMenuCommand()) {
    const contextMenu = contextMenuHandler.getContextMenu(interaction.commandName);
    if (!contextMenu) {
      return interaction.reply({
        content: '❌ Context menu command not found!',
        ephemeral: true
      });
    }

    try {
      await contextMenu.execute(interaction);
      logger.execute(`Context Menu: ${interaction.commandName}`, 0);
    } catch (error) {
      logger.error(`Error executing context menu: ${error.message}`);

      const errorMessage = {
        content: '❌ An error occurred while executing this context menu!',
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }

  // Handle button interactions
  else if (interaction.isButton()) {
    // Handle giveaway buttons
    if (interaction.customId.startsWith('giveaway-')) {
      try {
        await GiveawayButtons.handleButtonInteraction(interaction);
      } catch (error) {
        logger.error(`Error handling giveaway button: ${error.message}`);

        const errorMessage = {
          content: '❌ An error occurred while processing this button!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      }
    }
  }
});

// Automod Handler
client.on(Events.MessageCreate, message => handleAutomod(message, client));

// Connect to MongoDB
connectDB()
  .then(() => {
    // Silent connection - will be shown in summary
  })
  .catch(err => logger.error(`MongoDB connection failed: ${err.message}`));

// On ready event: deploy commands & schedule giveaways
client.once(Events.ClientReady, async () => {
  logger.success(`Logged in as ${client.user.tag}`);

  // Register Slash Commands and Context Menus
  const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
  try {
    // Get ALL commands - FORCE REGISTER EVERYTHING
    const allSlashCommands = subcommandHandler.getCommandsJSON();
    const allContextMenus = contextMenuHandler.getContextMenusJSON();

    logger.info(`🔥 FORCING REGISTRATION OF ALL COMMANDS:`);
    logger.info(`   • ${allSlashCommands.length} Slash Commands`);
    logger.info(`   • ${allContextMenus.length} Context Menus`);
    logger.info(`   • Total Commands: ${allSlashCommands.length + allContextMenus.length}`);

    // FORCE REGISTER ALL COMMANDS - NO LIMITS
    let registeredCommands = [];
    let registrationSuccess = false;

    // Strategy 1: Try all commands at once (prioritize essential commands)
    try {
      // Select most important commands for registration
      const essentialSlashCommands = allSlashCommands.filter(cmd =>
        ['bot', 'moderation', 'giveaway', 'fun', 'economy'].includes(cmd.name)
      );

      // Select essential context menus
      const essentialContextMenus = allContextMenus.filter(menu =>
        menu.name.includes('ban') ||
        menu.name.includes('userinfo') ||
        menu.name.includes('kick') ||
        menu.name.includes('timeout') ||
        menu.name.includes('warn')
      );

      const priorityCommands = [...essentialSlashCommands, ...essentialContextMenus];

      logger.info(`🎯 Registering priority commands: ${priorityCommands.length}`);
      logger.info(`   • Essential Slash: ${essentialSlashCommands.length}`);
      logger.info(`   • Essential Context: ${essentialContextMenus.length}`);

      await rest.put(
        Routes.applicationCommands(process.env.CLIENT_ID),
        { body: priorityCommands }
      );

      registeredCommands = priorityCommands;
      registrationSuccess = true;
      logger.success('🎉 PRIORITY COMMANDS REGISTERED SUCCESSFULLY!');

    } catch (error) {
      logger.warn(`Strategy 1 failed: ${error.message}`);

      // Strategy 2: Register slash commands first, then context menus
      try {
        // Clear existing commands first
        await rest.put(
          Routes.applicationCommands(process.env.CLIENT_ID),
          { body: [] }
        );

        // Register all slash commands
        await rest.put(
          Routes.applicationCommands(process.env.CLIENT_ID),
          { body: allSlashCommands }
        );

        registeredCommands = allSlashCommands;
        registrationSuccess = true;
        logger.success('🎉 ALL SLASH COMMANDS REGISTERED! (Context menus will be added separately)');

        // Try to add context menus
        try {
          const combinedCommands = [...allSlashCommands, ...allContextMenus];
          await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: combinedCommands }
          );

          registeredCommands = combinedCommands;
          logger.success('🎉 CONTEXT MENUS ADDED SUCCESSFULLY!');

        } catch (contextError) {
          logger.warn(`Context menus failed: ${contextError.message}`);
        }

      } catch (error2) {
        logger.error(`Strategy 2 failed: ${error2.message}`);

        // Strategy 3: Register in batches
        try {
          await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: [] }
          );

          // Register commands in smaller batches
          const batchSize = 10;
          const batches = [];

          for (let i = 0; i < allSlashCommands.length; i += batchSize) {
            batches.push(allSlashCommands.slice(i, i + batchSize));
          }

          let successfulCommands = [];

          for (const batch of batches) {
            try {
              await rest.put(
                Routes.applicationCommands(process.env.CLIENT_ID),
                { body: [...successfulCommands, ...batch] }
              );
              successfulCommands.push(...batch);
              logger.info(`✅ Registered batch: ${batch.map(c => c.name).join(', ')}`);
            } catch (batchError) {
              logger.warn(`Batch failed: ${batch.map(c => c.name).join(', ')}`);
              break;
            }
          }

          registeredCommands = successfulCommands;
          registrationSuccess = true;
          logger.success(`🎉 BATCH REGISTRATION: ${successfulCommands.length} commands registered!`);

        } catch (error3) {
          logger.error(`All strategies failed: ${error3.message}`);

          // Last resort: Register absolutely essential commands
          const mustHaveCommands = allSlashCommands.filter(cmd =>
            ['bot', 'moderation', 'giveaway'].includes(cmd.name)
          );

          await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: mustHaveCommands }
          );

          registeredCommands = mustHaveCommands;
          logger.error('⚠️ EMERGENCY MODE: Only essential commands registered!');
        }
      }
    }

    // Show professional registration results
    const slashCount = registeredCommands.filter(cmd => cmd.type === undefined || cmd.type === 1).length;
    const contextCount = registeredCommands.filter(cmd => cmd.type === 2 || cmd.type === 3).length;
    const totalPossible = allSlashCommands.length + allContextMenus.length;

    // Professional registration status display
    logger.showRegistrationStatus({
      registered: registeredCommands.length,
      total: totalPossible,
      slashCommands: `${slashCount}/${allSlashCommands.length}`,
      contextMenus: `${contextCount}/${allContextMenus.length}`
    });

    // List registered categories professionally
    const categories = [...new Set(allSlashCommands.map(cmd => cmd.name))];
    const registeredCategories = categories.filter(cat =>
      registeredCommands.some(cmd => cmd.name === cat)
    );

    logger.success(`🎯 Registered Categories: ${registeredCategories.join(', ')}`);
    console.log('');

    // Show professional summary and academic excellence
    logger.summary();
    logger.showAcademicStats();
  } catch (error) {
    logger.error(`Failed to register commands: ${error.message}`);
  }

  // Schedule Giveaways
  scheduleGiveaways(client);
});

// Graceful shutdown & Exception Handling
process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down...');
  client.destroy();
  process.exit(0);
});
process.on('uncaughtException', (err) => {
  logger.error(`UNCAUGHT EXCEPTION: ${err.message}`);
  process.exit(1);
});

// Login the bot
client.login(process.env.TOKEN)
  .catch(err => logger.error(`Login failed: ${err.message}`));