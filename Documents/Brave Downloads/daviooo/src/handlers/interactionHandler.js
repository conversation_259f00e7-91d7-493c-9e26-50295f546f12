// 🎯 PROFESSIONAL INTERACTION HANDLER - ACADEMIC EXCELLENCE
// Advanced interaction handling with comprehensive logging and error management

const logger = require('../utils/logger');

class InteractionHandler {
  constructor(client) {
    this.client = client;
    this.commandStats = new Map();
    this.errorCount = 0;
    this.successCount = 0;
    
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.client.on('interactionCreate', async (interaction) => {
      try {
        if (interaction.isChatInputCommand()) {
          await this.handleSlashCommand(interaction);
        } else if (interaction.isButton()) {
          await this.handleButton(interaction);
        } else if (interaction.isSelectMenu()) {
          await this.handleSelectMenu(interaction);
        } else if (interaction.isModalSubmit()) {
          await this.handleModal(interaction);
        } else if (interaction.isContextMenuCommand()) {
          await this.handleContextMenu(interaction);
        }
      } catch (error) {
        await this.handleError(interaction, error);
      }
    });
  }

  async handleSlashCommand(interaction) {
    const { commandName, user, guild } = interaction;
    const startTime = Date.now();
    
    // Log command execution
    logger.command(
      user.username, 
      commandName, 
      guild ? guild.name : 'DM'
    );
    
    // Update command statistics
    const commandKey = `${commandName}`;
    const stats = this.commandStats.get(commandKey) || { count: 0, totalTime: 0 };
    stats.count++;
    
    try {
      // Command will be handled by djs-slash-handler
      // We just track the execution
      const executionTime = Date.now() - startTime;
      stats.totalTime += executionTime;
      this.commandStats.set(commandKey, stats);
      this.successCount++;
      
      // Log successful execution
      if (executionTime > 1000) {
        logger.warning(`Slow command execution: ${commandName} (${executionTime}ms)`);
      }
      
    } catch (error) {
      this.errorCount++;
      throw error;
    }
  }

  async handleButton(interaction) {
    const { customId, user } = interaction;
    logger.info(`Button interaction: ${customId} by ${user.username}`);
    
    // Handle common button patterns
    if (customId.startsWith('giveaway_')) {
      await this.handleGiveawayButton(interaction);
    } else if (customId.startsWith('ticket_')) {
      await this.handleTicketButton(interaction);
    } else if (customId.startsWith('poll_')) {
      await this.handlePollButton(interaction);
    }
  }

  async handleSelectMenu(interaction) {
    const { customId, user, values } = interaction;
    logger.info(`Select menu interaction: ${customId} by ${user.username}`);
    
    // Handle select menu interactions
    if (customId.startsWith('role_')) {
      await this.handleRoleSelect(interaction);
    }
  }

  async handleModal(interaction) {
    const { customId, user } = interaction;
    logger.info(`Modal submission: ${customId} by ${user.username}`);
    
    // Handle modal submissions
    if (customId.startsWith('ticket_')) {
      await this.handleTicketModal(interaction);
    }
  }

  async handleContextMenu(interaction) {
    const { commandName, user } = interaction;
    logger.command(user.username, `Context: ${commandName}`);
  }

  async handleGiveawayButton(interaction) {
    // Giveaway button logic would go here
    await interaction.reply({ content: '🎉 Giveaway interaction handled!', ephemeral: true });
  }

  async handleTicketButton(interaction) {
    // Ticket button logic would go here
    await interaction.reply({ content: '🎫 Ticket interaction handled!', ephemeral: true });
  }

  async handlePollButton(interaction) {
    // Poll button logic would go here
    await interaction.reply({ content: '📊 Poll interaction handled!', ephemeral: true });
  }

  async handleRoleSelect(interaction) {
    // Role selection logic would go here
    await interaction.reply({ content: '🎭 Role selection handled!', ephemeral: true });
  }

  async handleTicketModal(interaction) {
    // Ticket modal logic would go here
    await interaction.reply({ content: '📝 Ticket modal handled!', ephemeral: true });
  }

  async handleError(interaction, error) {
    this.errorCount++;
    
    logger.error(`Interaction error: ${error.message}`, error.stack);
    
    const errorEmbed = {
      color: 0xff0000,
      title: '❌ Error',
      description: 'An error occurred while processing your request.',
      fields: [
        {
          name: 'Error Type',
          value: error.name || 'Unknown Error',
          inline: true
        },
        {
          name: 'Command',
          value: interaction.commandName || interaction.customId || 'Unknown',
          inline: true
        }
      ],
      timestamp: new Date().toISOString(),
      footer: {
        text: 'DAVIO Bot Error Handler'
      }
    };

    try {
      if (interaction.replied || interaction.deferred) {
        await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    } catch (replyError) {
      logger.error('Failed to send error message', replyError.message);
    }
  }

  // 📊 Get interaction statistics
  getStats() {
    return {
      totalCommands: this.successCount + this.errorCount,
      successfulCommands: this.successCount,
      failedCommands: this.errorCount,
      successRate: this.successCount / (this.successCount + this.errorCount) * 100,
      commandStats: Object.fromEntries(this.commandStats),
      uptime: logger.getUptime()
    };
  }

  // 🔄 Reset statistics
  resetStats() {
    this.commandStats.clear();
    this.errorCount = 0;
    this.successCount = 0;
    logger.info('Interaction statistics reset');
  }
}

module.exports = InteractionHandler;
