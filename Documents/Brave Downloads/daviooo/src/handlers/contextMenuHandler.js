// Perfect Context Menu Handler for Discord.js
// Automatically creates context menus for all commands

const fs = require('fs');
const path = require('path');
const { ContextMenuCommandBuilder, ApplicationCommandType } = require('discord.js');
const logger = require('../utils/consoleLogger');

class PerfectContextMenuHandler {
  constructor(client) {
    this.client = client;
    this.contextMenus = new Map();
    this.userCommands = new Map();
    this.messageCommands = new Map();
  }

  /**
   * Load and create context menus for all commands
   * @param {string} commandsPath - Path to the commands directory
   */
  loadContextMenus(commandsPath) {
    const categories = fs.readdirSync(commandsPath).filter(item => {
      return fs.lstatSync(path.join(commandsPath, item)).isDirectory();
    });

    for (const category of categories) {
      const categoryPath = path.join(commandsPath, category);
      const commandFiles = fs.readdirSync(categoryPath).filter(file => 
        file.endsWith('.js') && file !== 'index.js'
      );

      for (const file of commandFiles) {
        try {
          const commandPath = path.join(categoryPath, file);
          const command = require(commandPath);

          if (!command.data || !command.execute) {
            continue;
          }

          // Create context menus based on command type
          this.createContextMenusForCommand(command, category, file);

        } catch (error) {
          logger.error(`Error loading context menu for ${file}: ${error.message}`);
        }
      }
    }

    // Create compact context menu summary
    if (this.contextMenus.size > 0) {
      const contextMenuItems = [];
      for (const [name, menu] of this.contextMenus) {
        const type = menu.type === 2 ? 'User' : 'Message';
        contextMenuItems.push({
          name: name,
          count: type
        });
      }

      setTimeout(() => {
        logger.loadingSummary(contextMenuItems, 'CONTEXT MENUS LOADED');
      }, 1500);
    }
  }

  /**
   * Create appropriate context menus for a command
   */
  createContextMenusForCommand(command, category, filename) {
    const commandData = command.data.toJSON();
    const commandName = commandData.name;
    const baseName = filename.replace('.js', '');

    // Determine which context menus to create based on command functionality
    const contextMenuTypes = this.determineContextMenuTypes(commandName, category);

    for (const type of contextMenuTypes) {
      const contextMenuName = this.generateContextMenuName(commandName, category, type);
      
      try {
        const contextMenu = new ContextMenuCommandBuilder()
          .setName(contextMenuName)
          .setType(type);

        // Store the context menu with its executor
        this.contextMenus.set(contextMenuName, {
          data: contextMenu,
          execute: this.createContextMenuExecutor(command, type, category, commandName),
          originalCommand: command,
          category: category,
          type: type
        });

        // Organize by type for easier access
        if (type === ApplicationCommandType.User) {
          this.userCommands.set(contextMenuName, this.contextMenus.get(contextMenuName));
        } else if (type === ApplicationCommandType.Message) {
          this.messageCommands.set(contextMenuName, this.contextMenus.get(contextMenuName));
        }

        // Silent loading - will be shown in summary

      } catch (error) {
        logger.error(`Error creating context menu ${contextMenuName}: ${error.message}`);
      }
    }
  }

  /**
   * Determine which context menu types to create for a command (ultra-selective for development bot)
   */
  determineContextMenuTypes(commandName, category) {
    const types = [];

    // ULTRA SELECTIVE: Only the most essential context menus for development bot limits

    // Only the most critical moderation commands for user context
    const criticalUserCommands = {
      'moderation': ['userinfo', 'ban', 'kick', 'timeout', 'warn'], // Top 5 moderation commands
      'utility': ['avatar'] // Only avatar
    };

    // Only the most critical message commands
    const criticalMessageCommands = {
      'moderation': ['report'] // Only report for messages
    };

    // Check if this command should have a user context menu
    if (criticalUserCommands[category] && criticalUserCommands[category].some(cmd => commandName.includes(cmd))) {
      types.push(ApplicationCommandType.User);
    }

    // Check if this command should have a message context menu
    if (criticalMessageCommands[category] && criticalMessageCommands[category].some(cmd => commandName.includes(cmd))) {
      types.push(ApplicationCommandType.Message);
    }

    return types;
  }

  /**
   * Generate a unique context menu name
   */
  generateContextMenuName(commandName, category, type) {
    const typePrefix = type === ApplicationCommandType.User ? 'User' : 'Msg';
    return `${typePrefix} ${category} ${commandName}`.substring(0, 32); // Discord limit
  }

  /**
   * Create an executor function for context menu
   */
  createContextMenuExecutor(originalCommand, type, category, commandName) {
    return async (interaction) => {
      try {
        // Create a mock interaction that mimics slash command structure
        const mockInteraction = this.createMockSlashInteraction(interaction, type, category, commandName);

        // Execute the original command with the mock interaction
        if (originalCommand.execute.length > 1) {
          await originalCommand.execute(mockInteraction, this.client);
        } else {
          await originalCommand.execute(mockInteraction);
        }

      } catch (error) {
        console.error(`❌ Error in context menu ${interaction.commandName}:`, error);
        
        const errorMessage = {
          content: '❌ An error occurred while executing this context menu command!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      }
    };
  }

  /**
   * Create a mock slash interaction for compatibility
   */
  createMockSlashInteraction(contextInteraction, type, category, commandName) {
    const mockInteraction = Object.create(contextInteraction);
    
    // Override properties to make it look like a slash command
    mockInteraction.commandName = category;
    mockInteraction.isChatInputCommand = () => true;
    mockInteraction.isContextMenuCommand = () => false;
    
    // Create mock options object
    mockInteraction.options = {
      getSubcommand: () => commandName,
      getString: (name) => {
        if (type === ApplicationCommandType.Message && name === 'message') {
          return contextInteraction.targetMessage?.content || '';
        }
        return null;
      },
      getUser: (name) => {
        if (type === ApplicationCommandType.User && name === 'user') {
          return contextInteraction.targetUser;
        }
        return null;
      },
      getMember: (name) => {
        if (type === ApplicationCommandType.User && name === 'user') {
          return contextInteraction.targetMember;
        }
        return null;
      },
      getChannel: () => contextInteraction.channel,
      getRole: () => null,
      getInteger: () => null,
      getNumber: () => null,
      getBoolean: () => null,
      getAttachment: () => null,
      getMentionable: () => null
    };

    return mockInteraction;
  }

  /**
   * Get all context menus as JSON for registration
   */
  getContextMenusJSON() {
    return Array.from(this.contextMenus.values()).map(menu => menu.data.toJSON());
  }

  /**
   * Get a context menu by name
   */
  getContextMenu(name) {
    return this.contextMenus.get(name) || null;
  }

  /**
   * Get all context menus
   */
  getAllContextMenus() {
    return this.contextMenus;
  }

  /**
   * Get context menu count
   */
  getContextMenuCount() {
    return this.contextMenus.size;
  }

  /**
   * Get user context menus
   */
  getUserContextMenus() {
    return this.userCommands;
  }

  /**
   * Get message context menus
   */
  getMessageContextMenus() {
    return this.messageCommands;
  }
}

module.exports = PerfectContextMenuHandler;
