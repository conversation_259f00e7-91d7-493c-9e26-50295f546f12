// Professional Event Handler
const { Events } = require('discord.js');
const Logger = require('../utils/Logger');

class EventHandler {
  constructor(client) {
    this.client = client;
    this.setupEvents();
  }

  setupEvents() {
    // Guild events
    this.client.on(Events.GuildCreate, this.handleGuildCreate.bind(this));
    this.client.on(Events.GuildDelete, this.handleGuildDelete.bind(this));
    
    // Error events
    this.client.on(Events.Error, this.handleError.bind(this));
    this.client.on(Events.Warn, this.handleWarn.bind(this));
    
    // Rate limit events
    this.client.on(Events.RateLimited, this.handleRateLimit.bind(this));
  }

  handleGuildCreate(guild) {
    Logger.success(`Added to server: ${guild.name} (${guild.memberCount} members)`);
  }

  handleGuildDelete(guild) {
    Logger.warn(`Removed from server: ${guild.name}`);
  }

  handleError(error) {
    Logger.error(`Discord.js Error: ${error.message}`);
  }

  handleWarn(warning) {
    Logger.warn(`Discord.js Warning: ${warning}`);
  }

  handleRateLimit(rateLimitData) {
    Logger.warn(`Rate limited: ${rateLimitData.method} ${rateLimitData.url} - ${rateLimitData.timeout}ms`);
  }
}

module.exports = EventHandler;