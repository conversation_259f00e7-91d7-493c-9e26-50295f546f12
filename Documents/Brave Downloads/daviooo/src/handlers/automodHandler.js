// 🛡️ ENTERPRISE-G<PERSON>DE AUTOMOD HANDLER - PROFESSIONAL SECURITY ENGINE
// Advanced message filtering, raid protection, and intelligent threat detection

const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const AutoMod = require('../database/models/automod');

// 🎨 PROFESSIONAL EMOJIS & COLORS
const EMOJIS = {
  SHIELD: '🛡️',
  WARNING: '⚠️',
  BAN: '🔨',
  KICK: '👢',
  TIMEOUT: '⏰',
  DELETE: '🗑️',
  SCAM: '🎣',
  SPAM: '📢',
  CAPS: '🔠',
  LINK: '🔗',
  INVITE: '📨'
};

const COLORS = {
  WARNING: '#FFB02E',
  ERROR: '#FF3838',
  SUCCESS: '#00D166'
};

// 📊 SPAM TRACKING
const spamTracker = new Map();
const raidTracker = new Map();

// 🛡️ MAIN AUTOMOD HANDLER
const handleAutomod = async (message) => {
  try {
    // Ignore bots and system messages
    if (message.author.bot || message.system) return;

    const guildId = message.guild.id;
    const automod = await AutoMod.findOne({ guildId });

    if (!automod) return; // No automod settings

    // Check immunity
    if (await isImmune(message, automod)) return;

    // Content filtering
    await checkInvites(message, automod);
    await checkLinks(message, automod);
    await checkSpam(message, automod);
    await checkCaps(message, automod);
    await checkMentionSpam(message, automod);
    await checkScam(message, automod);

  } catch (error) {
    console.error('Automod Handler Error:', error);
  }
};

// 🔒 RAID PROTECTION HANDLER
const handleRaidProtection = async (member) => {
  try {
    const guildId = member.guild.id;
    const automod = await AutoMod.findOne({ guildId });

    if (!automod || !automod.raidProtection.enabled) return;

    const now = Date.now();
    const timeWindow = automod.raidProtection.timeWindow * 1000;

    // Initialize or update raid tracker
    if (!raidTracker.has(guildId)) {
      raidTracker.set(guildId, []);
    }

    const joins = raidTracker.get(guildId);
    joins.push(now);

    // Remove old joins outside time window
    const recentJoins = joins.filter(time => now - time < timeWindow);
    raidTracker.set(guildId, recentJoins);

    // Check if threshold exceeded
    if (recentJoins.length >= automod.raidProtection.joinThreshold) {
      await executeRaidAction(member.guild, automod);

      // Update stats
      automod.stats.raidsBlocked++;
      await automod.save();

      // Clear tracker
      raidTracker.delete(guildId);
    }

  } catch (error) {
    console.error('Raid Protection Error:', error);
  }
};

// 🚫 CONTENT FILTER FUNCTIONS
async function checkInvites(message, automod) {
  if (!automod.antiInvites.enabled) return;

  const inviteRegex = /discord\.gg\/[a-zA-Z0-9]+|discord\.com\/invite\/[a-zA-Z0-9]+|discordapp\.com\/invite\/[a-zA-Z0-9]+/gi;

  if (inviteRegex.test(message.content)) {
    await executeAction(message, automod.antiInvites.action, 'Invite Link Detected', EMOJIS.INVITE);
    automod.stats.messagesDeleted++;
    await automod.save();
  }
}

async function checkLinks(message, automod) {
  if (!automod.antiLinks.enabled) return;

  const linkRegex = /https?:\/\/[^\s]+/gi;
  const links = message.content.match(linkRegex);

  if (links) {
    // Check if any links are not in allowed domains
    const hasDisallowedLink = links.some(link => {
      return !automod.antiLinks.allowedDomains.some(domain => link.includes(domain));
    });

    if (hasDisallowedLink) {
      await executeAction(message, automod.antiLinks.action, 'External Link Detected', EMOJIS.LINK);
      automod.stats.messagesDeleted++;
      await automod.save();
    }
  }
}

async function checkSpam(message, automod) {
  if (!automod.antiSpam.enabled) return;

  const userId = message.author.id;
  const now = Date.now();
  const timeWindow = automod.antiSpam.timeWindow * 1000;

  // Initialize or update spam tracker
  if (!spamTracker.has(userId)) {
    spamTracker.set(userId, []);
  }

  const userMessages = spamTracker.get(userId);
  userMessages.push(now);

  // Remove old messages outside time window
  const recentMessages = userMessages.filter(time => now - time < timeWindow);
  spamTracker.set(userId, recentMessages);

  // Check if limit exceeded
  if (recentMessages.length > automod.antiSpam.messageLimit) {
    await executeAction(message, automod.antiSpam.action, 'Spam Detected', EMOJIS.SPAM);
    automod.stats.messagesDeleted++;
    await automod.save();

    // Clear user's spam tracker
    spamTracker.delete(userId);
  }
}

async function checkCaps(message, automod) {
  if (!automod.antiCaps.enabled) return;

  const content = message.content;
  const capsCount = (content.match(/[A-Z]/g) || []).length;
  const totalLetters = (content.match(/[A-Za-z]/g) || []).length;

  if (totalLetters > 10) { // Only check if message has enough letters
    const capsPercentage = (capsCount / totalLetters) * 100;

    if (capsPercentage >= automod.antiCaps.threshold) {
      await executeAction(message, automod.antiCaps.action, 'Excessive Caps Detected', EMOJIS.CAPS);
      automod.stats.messagesDeleted++;
      await automod.save();
    }
  }
}

async function checkMentionSpam(message, automod) {
  if (!automod.mentionSpam.enabled) return;

  const mentionCount = message.mentions.users.size + message.mentions.roles.size;

  if (mentionCount > automod.mentionSpam.limit) {
    await executeAction(message, automod.mentionSpam.action, 'Mention Spam Detected', EMOJIS.SPAM);
    automod.stats.messagesDeleted++;
    await automod.save();
  }
}

async function checkScam(message, automod) {
  if (!automod.antiScam.enabled) return;

  // Common scam patterns
  const scamPatterns = [
    /free\s+nitro/gi,
    /discord\s+nitro\s+gift/gi,
    /steam\s+gift/gi,
    /click\s+here\s+to\s+claim/gi,
    /limited\s+time\s+offer/gi,
    /congratulations.*won/gi
  ];

  const isScam = scamPatterns.some(pattern => pattern.test(message.content));

  if (isScam) {
    await executeAction(message, automod.antiScam.action, 'Potential Scam Detected', EMOJIS.SCAM);
    automod.stats.messagesDeleted++;
    await automod.save();
  }
}

// ⚡ ACTION EXECUTION
async function executeAction(message, action, reason, emoji) {
  try {
    const member = message.member;

    switch (action) {
      case 'delete':
        await message.delete();
        await sendAutomodLog(message, reason, emoji, 'Message Deleted');
        break;

      case 'warn':
        await message.delete();
        await sendWarning(message, reason, emoji);
        await sendAutomodLog(message, reason, emoji, 'User Warned');
        break;

      case 'timeout':
        await message.delete();
        if (member.moderatable) {
          await member.timeout(10 * 60 * 1000, `Automod: ${reason}`); // 10 minutes
          await sendAutomodLog(message, reason, emoji, 'User Timed Out (10m)');
        }
        break;

      case 'kick':
        await message.delete();
        if (member.kickable) {
          await member.kick(`Automod: ${reason}`);
          await sendAutomodLog(message, reason, emoji, 'User Kicked');
        }
        break;

      case 'ban':
        await message.delete();
        if (member.bannable) {
          await member.ban({ reason: `Automod: ${reason}`, deleteMessageDays: 1 });
          await sendAutomodLog(message, reason, emoji, 'User Banned');
        }
        break;
    }
  } catch (error) {
    console.error('Action Execution Error:', error);
  }
}

// 🔒 RAID ACTION EXECUTION
async function executeRaidAction(guild, automod) {
  try {
    switch (automod.raidProtection.action) {
      case 'lockdown':
        // Lock all channels
        const channels = guild.channels.cache.filter(c => c.type === 0); // Text channels
        for (const [, channel] of channels) {
          try {
            await channel.permissionOverwrites.edit(guild.roles.everyone, {
              SendMessages: false
            });
          } catch (error) {
            console.error(`Failed to lock channel ${channel.name}:`, error);
          }
        }
        await sendRaidLog(guild, 'Server Lockdown Activated', EMOJIS.SHIELD);
        break;

      case 'kick':
        // Kick recent joiners (last 5 minutes)
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        const recentMembers = guild.members.cache.filter(m =>
          m.joinedTimestamp > fiveMinutesAgo && !m.user.bot
        );

        for (const [, member] of recentMembers) {
          try {
            if (member.kickable) {
              await member.kick('Automod: Raid Protection');
            }
          } catch (error) {
            console.error(`Failed to kick ${member.user.tag}:`, error);
          }
        }
        await sendRaidLog(guild, `Kicked ${recentMembers.size} Recent Joiners`, EMOJIS.KICK);
        break;

      case 'ban':
        // Ban recent joiners
        const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
        const suspiciousMembers = guild.members.cache.filter(m =>
          m.joinedTimestamp > tenMinutesAgo && !m.user.bot
        );

        for (const [, member] of suspiciousMembers) {
          try {
            if (member.bannable) {
              await member.ban({ reason: 'Automod: Raid Protection', deleteMessageDays: 1 });
            }
          } catch (error) {
            console.error(`Failed to ban ${member.user.tag}:`, error);
          }
        }
        await sendRaidLog(guild, `Banned ${suspiciousMembers.size} Suspicious Members`, EMOJIS.BAN);
        break;
    }
  } catch (error) {
    console.error('Raid Action Error:', error);
  }
}

// 🛡️ IMMUNITY CHECK
async function isImmune(message, automod) {
  const member = message.member;

  // Check if user has admin permissions
  if (member.permissions.has(PermissionFlagsBits.Administrator)) return true;

  // Check immune roles
  const hasImmuneRole = member.roles.cache.some(role =>
    automod.immuneRoles.includes(role.id)
  );
  if (hasImmuneRole) return true;

  // Check immune channels
  if (automod.immuneChannels.includes(message.channel.id)) return true;

  return false;
}

// 📝 LOGGING FUNCTIONS
async function sendWarning(message, reason, emoji) {
  try {
    const embed = new EmbedBuilder()
      .setTitle(`${emoji} **Automod Warning**`)
      .setDescription(`${EMOJIS.WARNING} **${message.author}**, your message was removed due to: **${reason}**`)
      .setColor(COLORS.WARNING)
      .setTimestamp();

    const warning = await message.channel.send({ embeds: [embed] });
    setTimeout(() => warning.delete().catch(() => {}), 10000);
  } catch (error) {
    console.error('Warning Send Error:', error);
  }
}

async function sendAutomodLog(message, reason, emoji, action) {
  try {
    const automod = await AutoMod.findOne({ guildId: message.guild.id });
    if (!automod || !automod.logChannel) return;

    const logChannel = message.guild.channels.cache.get(automod.logChannel);
    if (!logChannel) return;

    const embed = new EmbedBuilder()
      .setTitle(`${emoji} **Automod Action**`)
      .setDescription(`**Action:** ${action}\n**Reason:** ${reason}`)
      .addFields(
        { name: 'User', value: `${message.author} (${message.author.id})`, inline: true },
        { name: 'Channel', value: `${message.channel}`, inline: true },
        { name: 'Message Content', value: message.content.slice(0, 1000) || 'No content', inline: false }
      )
      .setColor(COLORS.ERROR)
      .setTimestamp();

    await logChannel.send({ embeds: [embed] });
  } catch (error) {
    console.error('Automod Log Error:', error);
  }
}

async function sendRaidLog(guild, action, emoji) {
  try {
    const automod = await AutoMod.findOne({ guildId: guild.id });
    if (!automod || !automod.logChannel) return;

    const logChannel = guild.channels.cache.get(automod.logChannel);
    if (!logChannel) return;

    const embed = new EmbedBuilder()
      .setTitle(`${emoji} **Raid Protection Activated**`)
      .setDescription(`**Action:** ${action}`)
      .addFields(
        { name: 'Server', value: guild.name, inline: true },
        { name: 'Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
      )
      .setColor(COLORS.ERROR)
      .setTimestamp();

    await logChannel.send({ embeds: [embed] });
  } catch (error) {
    console.error('Raid Log Error:', error);
  }
}

// 🧹 CLEANUP FUNCTION
function cleanupTrackers() {
  const now = Date.now();
  const fiveMinutes = 5 * 60 * 1000;

  // Clean spam tracker
  for (const [userId, messages] of spamTracker.entries()) {
    const recentMessages = messages.filter(time => now - time < fiveMinutes);
    if (recentMessages.length === 0) {
      spamTracker.delete(userId);
    } else {
      spamTracker.set(userId, recentMessages);
    }
  }

  // Clean raid tracker
  for (const [guildId, joins] of raidTracker.entries()) {
    const recentJoins = joins.filter(time => now - time < fiveMinutes);
    if (recentJoins.length === 0) {
      raidTracker.delete(guildId);
    } else {
      raidTracker.set(guildId, recentJoins);
    }
  }
}

// Run cleanup every 5 minutes
setInterval(cleanupTrackers, 5 * 60 * 1000);

// 📤 EXPORTS
module.exports = {
  handleAutomod,
  handleRaidProtection
};