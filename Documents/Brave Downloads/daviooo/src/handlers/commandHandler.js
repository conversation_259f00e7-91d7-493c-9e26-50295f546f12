// Professional Command Handler
const fs = require('fs');
const path = require('path');
const { Collection } = require('discord.js');
const Logger = require('../utils/Logger');

class CommandHandler {
  constructor(client) {
    this.client = client;
    this.client.commands = new Collection();
  }

  async loadCommands() {
    const commandsPath = path.join(__dirname, '../commands');
    
    if (!fs.existsSync(commandsPath)) {
      Logger.warn('Commands directory not found');
      return { loaded: 0, failed: 0, categories: new Set() };
    }

    const commandFolders = fs.readdirSync(commandsPath);
    let loaded = 0;
    let failed = 0;
    const categories = new Set();

    for (const folder of commandFolders) {
      const folderPath = path.join(commandsPath, folder);
      if (!fs.statSync(folderPath).isDirectory()) continue;

      const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

      for (const file of commandFiles) {
        try {
          const filePath = path.join(folderPath, file);
          delete require.cache[require.resolve(filePath)];
          const command = require(filePath);

          if (this.validateCommand(command)) {
            this.client.commands.set(command.data.name, command);
            categories.add(folder);
            loaded++;
            Logger.debug(`Loaded command: ${command.data.name} (${folder})`);
          } else {
            failed++;
            Logger.warn(`Invalid command structure: ${file}`);
          }
        } catch (error) {
          failed++;
          Logger.error(`Failed to load ${file}: ${error.message}`);
        }
      }
    }

    return { loaded, failed, categories };
  }

  validateCommand(command) {
    return command && 
           command.data && 
           command.execute && 
           typeof command.execute === 'function' &&
           command.data.name &&
           command.data.description;
  }

  async handleInteraction(interaction) {
    if (!interaction.isChatInputCommand()) return;

    const command = this.client.commands.get(interaction.commandName);
    if (!command) {
      Logger.warn(`Unknown command attempted: ${interaction.commandName}`);
      return interaction.reply({ 
        content: '❌ Command not found!', 
        ephemeral: true 
      });
    }

    try {
      await command.execute(interaction);
      
      // Log important commands only
      if (this.isImportantCommand(interaction.commandName)) {
        Logger.info(`${interaction.commandName} used by ${interaction.user.tag} in ${interaction.guild?.name || 'DM'}`);
      }
    } catch (error) {
      Logger.error(`Command ${interaction.commandName} failed: ${error.message}`);
      await this.handleCommandError(interaction, error);
    }
  }

  isImportantCommand(commandName) {
    const importantCommands = ['ban', 'kick', 'timeout', 'warn', 'clear', 'mute'];
    return importantCommands.includes(commandName);
  }

  async handleCommandError(interaction, error) {
    const errorMessage = { 
      content: '❌ An error occurred while executing this command!', 
      ephemeral: true 
    };

    try {
      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    } catch (followUpError) {
      Logger.error(`Failed to send error message: ${followUpError.message}`);
    }
  }
}

module.exports = CommandHandler;