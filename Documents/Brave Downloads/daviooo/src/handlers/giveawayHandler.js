// Professional Giveaway Handler with Error Recovery
const Giveaway = require('../database/models/Giveaway');
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const logger = require('../utils/consoleLogger');

async function scheduleGiveaways(client) {
  try {
    const ongoingGiveaways = await Giveaway.find({ ended: false });
    let scheduledCount = 0;
    let cleanedCount = 0;

    for (const giveaway of ongoingGiveaways) {
      const remaining = giveaway.endsAt - Date.now();

      if (remaining > 0) {
        setTimeout(() => endGiveaway(giveaway, client), remaining);
        scheduledCount++;
      } else {
        // End expired giveaways immediately
        await endGiveaway(giveaway, client);
        cleanedCount++;
      }
    }

    if (scheduledCount > 0) {
      logger.success(`Scheduled ${scheduledCount} giveaways`);
    }
    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} expired giveaways`);
    }
  } catch (error) {
    logger.error(`Error scheduling giveaways: ${error.message}`);
  }
}

async function endGiveaway(giveaway, client) {
  if (giveaway.ended) return;

  try {
    const updated = await Giveaway.findById(giveaway._id);
    if (!updated) {
      logger.warn('Giveaway not found in database during end process');
      return;
    }

    // Validate channel exists
    let channel;
    try {
      channel = await client.channels.fetch(updated.channelId);
    } catch (error) {
      // Silently mark as ended - channel no longer exists
      await Giveaway.findByIdAndUpdate(updated._id, { ended: true });
      return;
    }

    // Validate message exists
    let message;
    try {
      message = await channel.messages.fetch(updated.messageId);
    } catch (error) {
      // Silently mark as ended - message no longer exists
      await Giveaway.findByIdAndUpdate(updated._id, { ended: true });
      return;
    }

    // Mark as ended in database
    await Giveaway.findByIdAndUpdate(updated._id, { ended: true });

    // Check if enough participants
    if (updated.participants.length < 2) {
      const endEmbed = new EmbedBuilder()
        .setColor(0x808080)
        .setTitle('🎉 Giveaway Ended! 🎉')
        .setDescription(`**Prize**: ${updated.prize}
**Status**: Cancelled - Not enough participants (minimum 2 required)
**Total Participants**: ${updated.participants.length}`)
        .addFields(
          { name: '👥 Participants', value: updated.participants.length.toString(), inline: true },
          { name: '👑 Host', value: `<@${updated.hostId}>`, inline: true }
        )
        .setFooter({ text: `Giveaway Ended • ${new Date().toLocaleString()}` })
        .setTimestamp();

      const buttonRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder().setCustomId('giveaway-ended').setLabel('Cancelled').setStyle(ButtonStyle.Secondary).setDisabled(true),
        new ButtonBuilder().setCustomId('giveaway-info').setLabel('Info').setStyle(ButtonStyle.Secondary).setDisabled(true)
      );

      await message.edit({ embeds: [endEmbed], components: [buttonRow] });
      await message.reply('❌ Giveaway cancelled: Not enough participants (minimum 2 required).');

      logger.info(`Giveaway cancelled: ${updated.prize} - Not enough participants`);
    } else {
      // Select winners and update database
      const winners = selectWinners(updated.participants, updated.winnerCount);
      await Giveaway.findByIdAndUpdate(updated._id, { winnerIds: winners });

      const endEmbed = new EmbedBuilder()
        .setColor(0x00FF00)
        .setTitle('🎉 Giveaway Ended! 🎉')
        .setDescription(`**Prize**: ${updated.prize}
**Winners**: ${winners.map(u => `<@${u}>`).join(', ')}
**Total Participants**: ${updated.participants.length}`)
        .addFields(
          { name: '👥 Participants', value: updated.participants.length.toString(), inline: true },
          { name: '👑 Host', value: `<@${updated.hostId}>`, inline: true }
        )
        .setFooter({ text: `Ended at ${new Date().toLocaleString()}` })
        .setTimestamp();

      const buttonRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder().setCustomId('giveaway-ended').setLabel('Ended').setStyle(ButtonStyle.Secondary).setDisabled(true),
        new ButtonBuilder().setCustomId('giveaway-info').setLabel('Info').setStyle(ButtonStyle.Secondary).setDisabled(true)
      );

      await message.edit({ embeds: [endEmbed], components: [buttonRow] });

      // Send winner announcement
      if (winners.length > 0) {
        const giveawayURL = `https://discord.com/channels/${updated.guildId}/${updated.channelId}/${updated.messageId}`;
        const winMsg = await message.reply({
          content: `🎉 **CONGRATULATIONS!** 🎉

Winner${winners.length > 1 ? 's' : ''} of **${updated.prize}**:
${winners.map(u => `<@${u}>`).join(', ')}

Thank you to all ${updated.participants.length} participants!
[View the giveaway](${giveawayURL})`,
          allowedMentions: { users: winners }
        });

        // Try to pin the winner message
        try {
          await winMsg.pin();
        } catch (pinError) {
          logger.warn('Could not pin winner message: Missing permissions');
        }
      }

      logger.success(`Giveaway ended successfully: ${updated.prize} - ${winners.length} winner(s)`);
    }
  } catch (error) {
    logger.error(`Error ending giveaway: ${error.message}`);
    // Mark as ended in database to prevent retry loops
    try {
      await Giveaway.findByIdAndUpdate(giveaway._id, { ended: true });
      logger.info('Marked problematic giveaway as ended in database');
    } catch (dbError) {
      logger.error(`Failed to mark giveaway as ended: ${dbError.message}`);
    }
  }
}

function selectWinners(participants, count) {
  const uniqueParticipants = [...new Set(participants)];
  const winners = [];
  while (winners.length < count && uniqueParticipants.length) {
    const i = Math.floor(Math.random() * uniqueParticipants.length);
    winners.push(uniqueParticipants.splice(i, 1)[0]);
  }
  return winners;
}

module.exports = { scheduleGiveaways, endGiveaway };