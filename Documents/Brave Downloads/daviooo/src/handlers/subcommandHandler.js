// Perfect Professional Subcommand Handler for Discord.js
// Handles all command structures with perfect error handling and cooldowns

const fs = require('fs');
const path = require('path');
const { SlashCommandBuilder, SlashCommandSubcommandBuilder, Collection } = require('discord.js');
const logger = require('../utils/consoleLogger');

class PerfectSubcommandHandler {
  constructor(client) {
    this.commands = new Map();
    this.client = client;
    this.cooldowns = new Collection();
    this.commandStats = new Map();
  }

  /**
   * Load all commands from the commands directory and convert them to subcommands
   * @param {string} commandsPath - Path to the commands directory
   */
  loadCommands(commandsPath) {
    const categories = fs.readdirSync(commandsPath).filter(item => {
      return fs.lstatSync(path.join(commandsPath, item)).isDirectory();
    });

    for (const category of categories) {
      const categoryPath = path.join(commandsPath, category);
      const commandFiles = fs.readdirSync(categoryPath).filter(file => 
        file.endsWith('.js') && file !== 'index.js'
      );

      if (commandFiles.length === 0) continue;

      // Create main command for this category
      const mainCommand = new SlashCommandBuilder()
        .setName(category)
        .setDescription(`All ${category} related commands`);

      const subcommandExecutors = new Map();

      // Process each command file in the category
      for (const file of commandFiles) {
        try {
          const commandPath = path.join(categoryPath, file);
          const command = require(commandPath);

          if (!command.data || !command.execute) {
            logger.skip(file, 'Invalid command structure');
            continue;
          }

          // Convert command to subcommand
          const subcommand = this.convertToSubcommand(command);
          if (subcommand) {
            mainCommand.addSubcommand(subcommand);
            subcommandExecutors.set(subcommand.name, {
              executor: command.execute,
              originalCommand: command
            });
            logger.command(category, subcommand.name);
          }
        } catch (error) {
          logger.error(`Error loading ${file}: ${error.message}`);
        }
      }

      // Create perfect executor for the main command
      const mainExecutor = async (interaction) => {
        const subcommandName = interaction.options.getSubcommand();
        const commandData = subcommandExecutors.get(subcommandName);

        if (!commandData) {
          return this.sendErrorReply(interaction, '❌ Unknown subcommand');
        }

        const { executor, originalCommand } = commandData;
        const userId = interaction.user.id;
        const commandKey = `${category}-${subcommandName}`;

        try {
          // Perfect cooldown handling
          if (await this.handleCooldown(interaction, userId, commandKey, originalCommand)) {
            return; // Cooldown message already sent
          }

          // Perfect permission handling
          if (await this.handlePermissions(interaction, originalCommand)) {
            return; // Permission error already sent
          }

          // Update command statistics
          this.updateCommandStats(commandKey);

          // Execute with perfect error handling
          const startTime = Date.now();

          if (executor.length > 1) {
            await executor(interaction, this.client);
          } else {
            await executor(interaction);
          }

          // Log successful execution
          const executionTime = Date.now() - startTime;
          logger.execute(`/${category} ${subcommandName}`, executionTime);

        } catch (error) {
          await this.handleCommandError(interaction, error, commandKey);
        }
      };

      // Store the command
      this.commands.set(category, {
        data: mainCommand,
        execute: mainExecutor
      });

      logger.category(category, subcommandExecutors.size);
    }

    // Create beautiful command summary box
    const commandList = [];
    for (const [category, command] of this.commands) {
      const subcommandCount = command.data.options?.length || 0;
      commandList.push(`${category} (${subcommandCount} subcommands)`);
    }

    logger.separator('SLASH COMMANDS');
    logger.createBox('slash commands', commandList, 'green');
  }

  /**
   * Convert a regular command to a subcommand
   * @param {Object} command - The command object to convert
   * @returns {SlashCommandSubcommandBuilder|null} - The converted subcommand
   */
  convertToSubcommand(command) {
    if (!command.data) return null;

    const commandData = command.data.toJSON();
    const subcommand = new SlashCommandSubcommandBuilder()
      .setName(commandData.name)
      .setDescription(commandData.description || 'No description provided');

    // Copy options from the original command
    if (commandData.options) {
      for (const option of commandData.options) {
        // Skip subcommand and subcommand group types
        if (option.type === 1 || option.type === 2) continue;

        this.addOptionToSubcommand(subcommand, option);
      }
    }

    return subcommand;
  }

  /**
   * Add an option to a subcommand
   * @param {SlashCommandSubcommandBuilder} subcommand - The subcommand to add the option to
   * @param {Object} option - The option to add
   */
  addOptionToSubcommand(subcommand, option) {
    const optionMethods = {
      3: 'addStringOption',      // STRING
      4: 'addIntegerOption',     // INTEGER
      5: 'addBooleanOption',     // BOOLEAN
      6: 'addUserOption',        // USER
      7: 'addChannelOption',     // CHANNEL
      8: 'addRoleOption',        // ROLE
      9: 'addMentionableOption', // MENTIONABLE
      10: 'addNumberOption',     // NUMBER
      11: 'addAttachmentOption'  // ATTACHMENT
    };

    const methodName = optionMethods[option.type];
    if (!methodName) {
      console.warn(`Unknown option type ${option.type} for option ${option.name}`);
      return;
    }

    const optionBuilder = subcommand[methodName](opt => {
      opt.setName(option.name)
         .setDescription(option.description || 'No description')
         .setRequired(Boolean(option.required));

      // Add choices if they exist
      if (option.choices && option.choices.length > 0) {
        opt.addChoices(...option.choices);
      }

      return opt;
    });
  }

  /**
   * Get all commands as JSON for registration
   * @returns {Array} - Array of command data for Discord API
   */
  getCommandsJSON() {
    return Array.from(this.commands.values()).map(cmd => cmd.data.toJSON());
  }

  /**
   * Get a command by name
   * @param {string} name - The command name
   * @returns {Object|null} - The command object or null if not found
   */
  getCommand(name) {
    return this.commands.get(name) || null;
  }

  /**
   * Get all commands
   * @returns {Map} - Map of all commands
   */
  getAllCommands() {
    return this.commands;
  }

  /**
   * Get the total number of commands loaded
   * @returns {number} - Number of commands
   */
  getCommandCount() {
    return this.commands.size;
  }

  /**
   * Perfect cooldown handling
   */
  async handleCooldown(interaction, userId, commandKey, originalCommand) {
    const cooldownAmount = originalCommand.cooldown || 3000; // Default 3 seconds
    const cooldownKey = `${userId}-${commandKey}`;

    if (this.cooldowns.has(cooldownKey)) {
      const expirationTime = this.cooldowns.get(cooldownKey) + cooldownAmount;
      const now = Date.now();

      if (now < expirationTime) {
        const timeLeft = (expirationTime - now) / 1000;
        await this.sendErrorReply(interaction,
          `⏰ Please wait **${timeLeft.toFixed(1)}s** before using this command again.`
        );
        return true;
      }
    }

    this.cooldowns.set(cooldownKey, Date.now());

    // Clean up old cooldowns
    setTimeout(() => this.cooldowns.delete(cooldownKey), cooldownAmount);

    return false;
  }

  /**
   * Perfect permission handling
   */
  async handlePermissions(interaction, originalCommand) {
    if (originalCommand.moderatorOnly && !interaction.member.permissions.has('Administrator')) {
      await this.sendErrorReply(interaction,
        '❌ You need **Administrator** permissions to use this command.'
      );
      return true;
    }

    if (originalCommand.requiredPermissions) {
      const missing = interaction.member.permissions.missing(originalCommand.requiredPermissions);
      if (missing.length > 0) {
        await this.sendErrorReply(interaction,
          `❌ You need the following permissions: **${missing.join(', ')}**`
        );
        return true;
      }
    }

    return false;
  }

  /**
   * Perfect error handling
   */
  async handleCommandError(interaction, error, commandKey) {
    console.error(`❌ Error in command ${commandKey}:`, error);

    // Update error statistics
    const stats = this.commandStats.get(commandKey) || { uses: 0, errors: 0 };
    stats.errors++;
    this.commandStats.set(commandKey, stats);

    await this.sendErrorReply(interaction,
      '❌ An error occurred while executing this command! Please try again later.'
    );
  }

  /**
   * Perfect error reply handling
   */
  async sendErrorReply(interaction, content) {
    const errorMessage = { content, ephemeral: true };

    try {
      if (interaction.replied || interaction.deferred) {
        await interaction.followUp(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    } catch (error) {
      console.error('Failed to send error reply:', error);
    }
  }

  /**
   * Update command statistics
   */
  updateCommandStats(commandKey) {
    const stats = this.commandStats.get(commandKey) || { uses: 0, errors: 0 };
    stats.uses++;
    this.commandStats.set(commandKey, stats);
  }

  /**
   * Get command statistics
   */
  getCommandStats() {
    return Object.fromEntries(this.commandStats);
  }
}

module.exports = PerfectSubcommandHandler;
