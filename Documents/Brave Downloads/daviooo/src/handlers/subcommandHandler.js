// Perfect Subcommand Handler with 25-Command Limit Fix
const { SlashCommandBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/consoleLogger');

class PerfectSubcommandHandler {
  constructor(client) {
    this.client = client;
    this.commands = new Map();
    this.cooldowns = new Map();
    this.commandStats = new Map();
  }

  async loadCommands() {
    const commandsPath = path.join(__dirname, '../commands');
    const categories = fs.readdirSync(commandsPath);

    for (const category of categories) {
      const categoryPath = path.join(commandsPath, category);
      
      if (!fs.statSync(categoryPath).isDirectory()) continue;

      const commandFiles = fs.readdirSync(categoryPath).filter(file => file.endsWith('.js'));
      
      if (commandFiles.length === 0) continue;

      // Handle Discord's 25 subcommand limit
      const maxSubcommands = 25;
      let commandParts = [];
      let currentPart = [];

      for (const file of commandFiles) {
        try {
          const commandPath = path.join(categoryPath, file);
          const command = require(commandPath);

          if (!command.data || !command.execute) {
            continue; // Skip invalid commands
          }

          currentPart.push({ file, command });

          // Split into parts if we hit the limit
          if (currentPart.length >= maxSubcommands) {
            commandParts.push(currentPart);
            currentPart = [];
          }
        } catch (error) {
          logger.error(`Error loading ${file}: ${error.message}`);
        }
      }

      // Add remaining commands
      if (currentPart.length > 0) {
        commandParts.push(currentPart);
      }

      // Create commands for each part
      commandParts.forEach((part, index) => {
        this.createCommandFromPart(category, part, index);
      });
    }

    // Show summary
    this.showCommandSummary();
  }

  createCommandFromPart(category, commandPart, partIndex) {
    const commandName = partIndex === 0 ? category : `${category}${partIndex + 1}`;
    const subcommandExecutors = new Map();

    // Create main command
    const mainCommand = new SlashCommandBuilder()
      .setName(commandName)
      .setDescription(`${category.charAt(0).toUpperCase() + category.slice(1)} commands${partIndex > 0 ? ` (Part ${partIndex + 1})` : ''}`);

    // Add subcommands
    commandPart.forEach(({ file, command }) => {
      const subcommand = this.convertToSubcommand(command);
      if (subcommand) {
        mainCommand.addSubcommand(subcommand);
        subcommandExecutors.set(subcommand.name, {
          executor: command.execute,
          originalCommand: command
        });
      }
    });

    // Create main executor
    const mainExecutor = async (interaction) => {
      const subcommandName = interaction.options.getSubcommand();
      const commandData = subcommandExecutors.get(subcommandName);

      if (!commandData) {
        return await interaction.reply({
          content: '❌ Unknown subcommand!',
          ephemeral: true
        });
      }

      const { executor, originalCommand } = commandData;
      const userId = interaction.user.id;
      const commandKey = `${commandName}-${subcommandName}`;

      try {
        // Check cooldowns
        if (this.isOnCooldown(userId, commandKey)) {
          const remainingTime = this.getCooldownTime(userId, commandKey);
          return await interaction.reply({
            content: `⏰ You're on cooldown! Please wait ${Math.ceil(remainingTime / 1000)} more seconds.`,
            ephemeral: true
          });
        }

        // Set cooldown (3 seconds default)
        this.setCooldown(userId, commandKey, 3000);

        // Update stats
        this.updateCommandStats(commandKey);

        // Execute command
        const startTime = Date.now();
        
        if (executor.length > 1) {
          await executor(interaction, this.client);
        } else {
          await executor(interaction);
        }

        // Log execution
        const executionTime = Date.now() - startTime;
        logger.execute(`/${commandName} ${subcommandName}`, executionTime);

      } catch (error) {
        logger.error(`Command error in /${commandName} ${subcommandName}: ${error.message}`);
        
        const errorMessage = {
          content: '❌ An error occurred while executing this command!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      }
    };

    // Store the command
    this.commands.set(commandName, {
      data: mainCommand,
      execute: mainExecutor
    });
  }

  convertToSubcommand(command) {
    if (!command.data) return null;

    const commandData = command.data.toJSON();
    const subcommandName = commandData.name;

    return (subcommand) => {
      subcommand
        .setName(subcommandName)
        .setDescription(commandData.description || `${subcommandName} command`);

      // Add options
      if (commandData.options) {
        commandData.options.forEach(option => {
          this.addOptionToSubcommand(subcommand, option);
        });
      }

      return subcommand;
    };
  }

  addOptionToSubcommand(subcommand, option) {
    try {
      switch (option.type) {
        case 1: // SUB_COMMAND
        case 2: // SUB_COMMAND_GROUP
          break; // Skip these
        case 3: // STRING
          subcommand.addStringOption(opt => this.configureOption(opt, option));
          break;
        case 4: // INTEGER
          subcommand.addIntegerOption(opt => this.configureOption(opt, option));
          break;
        case 5: // BOOLEAN
          subcommand.addBooleanOption(opt => this.configureOption(opt, option));
          break;
        case 6: // USER
          subcommand.addUserOption(opt => this.configureOption(opt, option));
          break;
        case 7: // CHANNEL
          subcommand.addChannelOption(opt => this.configureOption(opt, option));
          break;
        case 8: // ROLE
          subcommand.addRoleOption(opt => this.configureOption(opt, option));
          break;
        case 9: // MENTIONABLE
          subcommand.addMentionableOption(opt => this.configureOption(opt, option));
          break;
        case 10: // NUMBER
          subcommand.addNumberOption(opt => this.configureOption(opt, option));
          break;
        case 11: // ATTACHMENT
          subcommand.addAttachmentOption(opt => this.configureOption(opt, option));
          break;
      }
    } catch (error) {
      // Silent skip for unknown option types
    }
  }

  configureOption(optionBuilder, option) {
    optionBuilder
      .setName(option.name)
      .setDescription(option.description || 'No description')
      .setRequired(option.required || false);

    if (option.choices) {
      optionBuilder.addChoices(...option.choices);
    }

    return optionBuilder;
  }

  // Cooldown management
  isOnCooldown(userId, commandKey) {
    const cooldownKey = `${userId}-${commandKey}`;
    const cooldownEnd = this.cooldowns.get(cooldownKey);
    return cooldownEnd && Date.now() < cooldownEnd;
  }

  getCooldownTime(userId, commandKey) {
    const cooldownKey = `${userId}-${commandKey}`;
    const cooldownEnd = this.cooldowns.get(cooldownKey);
    return cooldownEnd ? cooldownEnd - Date.now() : 0;
  }

  setCooldown(userId, commandKey, duration = 3000) {
    const cooldownKey = `${userId}-${commandKey}`;
    this.cooldowns.set(cooldownKey, Date.now() + duration);
    
    // Auto-cleanup after cooldown
    setTimeout(() => {
      this.cooldowns.delete(cooldownKey);
    }, duration);
  }

  // Stats management
  updateCommandStats(commandKey) {
    const current = this.commandStats.get(commandKey) || 0;
    this.commandStats.set(commandKey, current + 1);
  }

  // Get commands for registration
  getCommandsJSON() {
    return Array.from(this.commands.values()).map(command => command.data.toJSON());
  }

  // Get command executor
  getCommand(commandName) {
    return this.commands.get(commandName);
  }

  // Show command summary
  showCommandSummary() {
    const commandItems = [];
    for (const [category, command] of this.commands) {
      commandItems.push(category);
    }
    
    logger.showBox(commandItems, 'SLASH COMMANDS', 'green');
  }
}

module.exports = PerfectSubcommandHandler;
