// Advanced Professional Subcommand Handler for Discord.js
// Intelligently handles different command structures and converts them to subcommands

const fs = require('fs');
const path = require('path');
const { SlashCommandBuilder, SlashCommandSubcommandBuilder } = require('discord.js');

class AdvancedSubcommandHandler {
  constructor(client) {
    this.commands = new Map();
    this.client = client;
    this.cooldowns = new Map();
  }

  /**
   * Load all commands from the commands directory and convert them to subcommands
   * @param {string} commandsPath - Path to the commands directory
   */
  loadCommands(commandsPath) {
    const categories = fs.readdirSync(commandsPath).filter(item => {
      return fs.lstatSync(path.join(commandsPath, item)).isDirectory();
    });

    for (const category of categories) {
      const categoryPath = path.join(commandsPath, category);
      const commandFiles = fs.readdirSync(categoryPath).filter(file => 
        file.endsWith('.js') && file !== 'index.js'
      );

      if (commandFiles.length === 0) continue;

      // Create main command for this category
      const mainCommand = new SlashCommandBuilder()
        .setName(category)
        .setDescription(`All ${category} related commands`);

      const subcommandExecutors = new Map();

      // Process each command file in the category
      for (const file of commandFiles) {
        try {
          const commandPath = path.join(categoryPath, file);
          const command = require(commandPath);

          if (!command.data || !command.execute) {
            console.warn(`⚠️  Skipping ${file}: Invalid command structure`);
            continue;
          }

          // Convert command to subcommand
          const subcommand = this.convertToSubcommand(command);
          if (subcommand) {
            mainCommand.addSubcommand(subcommand);
            subcommandExecutors.set(subcommand.name, {
              executor: command.execute,
              originalCommand: command
            });
            console.log(`✅ Loaded subcommand: /${category} ${subcommand.name}`);
          }
        } catch (error) {
          console.error(`❌ Error loading ${file}:`, error.message);
        }
      }

      // Create intelligent executor for the main command
      const mainExecutor = async (interaction) => {
        const subcommandName = interaction.options.getSubcommand();
        const commandData = subcommandExecutors.get(subcommandName);

        if (!commandData) {
          return interaction.reply({
            content: '❌ Unknown subcommand',
            ephemeral: true
          });
        }

        const { executor, originalCommand } = commandData;

        try {
          // Handle cooldowns
          if (originalCommand.cooldown) {
            const cooldownKey = `${interaction.user.id}-${category}-${subcommandName}`;
            const now = Date.now();
            const cooldownAmount = originalCommand.cooldown;

            if (this.cooldowns.has(cooldownKey)) {
              const expirationTime = this.cooldowns.get(cooldownKey) + cooldownAmount;
              if (now < expirationTime) {
                const timeLeft = (expirationTime - now) / 1000;
                return interaction.reply({
                  content: `⏰ Please wait ${timeLeft.toFixed(1)} more seconds before using this command again.`,
                  ephemeral: true
                });
              }
            }
            this.cooldowns.set(cooldownKey, now);
          }

          // Handle permission checks
          if (originalCommand.moderatorOnly && !interaction.member.permissions.has('Administrator')) {
            return interaction.reply({
              content: '❌ You need administrator permissions to use this command.',
              ephemeral: true
            });
          }

          // Execute with appropriate parameters
          if (executor.length > 1) {
            // Command expects (interaction, client)
            await executor(interaction, this.client);
          } else {
            // Command expects (interaction)
            await executor(interaction);
          }
        } catch (error) {
          console.error(`Error executing subcommand ${subcommandName}:`, error);

          const errorMessage = {
            content: '❌ An error occurred while executing this command!',
            ephemeral: true
          };

          if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
          } else {
            await interaction.reply(errorMessage);
          }
        }
      };

      // Store the command
      this.commands.set(category, {
        data: mainCommand,
        execute: mainExecutor
      });

      console.log(`🚀 Created category command: /${category} with ${subcommandExecutors.size} subcommands`);
    }
  }

  /**
   * Convert a regular command to a subcommand
   * @param {Object} command - The command object to convert
   * @returns {SlashCommandSubcommandBuilder|null} - The converted subcommand
   */
  convertToSubcommand(command) {
    if (!command.data) return null;

    const commandData = command.data.toJSON();
    const subcommand = new SlashCommandSubcommandBuilder()
      .setName(commandData.name)
      .setDescription(commandData.description || 'No description provided');

    // Copy options from the original command
    if (commandData.options) {
      for (const option of commandData.options) {
        // Skip subcommand and subcommand group types
        if (option.type === 1 || option.type === 2) continue;

        this.addOptionToSubcommand(subcommand, option);
      }
    }

    return subcommand;
  }

  /**
   * Add an option to a subcommand
   * @param {SlashCommandSubcommandBuilder} subcommand - The subcommand to add the option to
   * @param {Object} option - The option to add
   */
  addOptionToSubcommand(subcommand, option) {
    const optionMethods = {
      3: 'addStringOption',      // STRING
      4: 'addIntegerOption',     // INTEGER
      5: 'addBooleanOption',     // BOOLEAN
      6: 'addUserOption',        // USER
      7: 'addChannelOption',     // CHANNEL
      8: 'addRoleOption',        // ROLE
      9: 'addMentionableOption', // MENTIONABLE
      10: 'addNumberOption',     // NUMBER
      11: 'addAttachmentOption'  // ATTACHMENT
    };

    const methodName = optionMethods[option.type];
    if (!methodName) {
      console.warn(`Unknown option type ${option.type} for option ${option.name}`);
      return;
    }

    const optionBuilder = subcommand[methodName](opt => {
      opt.setName(option.name)
         .setDescription(option.description || 'No description')
         .setRequired(Boolean(option.required));

      // Add choices if they exist
      if (option.choices && option.choices.length > 0) {
        opt.addChoices(...option.choices);
      }

      return opt;
    });
  }

  /**
   * Get all commands as JSON for registration
   * @returns {Array} - Array of command data for Discord API
   */
  getCommandsJSON() {
    return Array.from(this.commands.values()).map(cmd => cmd.data.toJSON());
  }

  /**
   * Get a command by name
   * @param {string} name - The command name
   * @returns {Object|null} - The command object or null if not found
   */
  getCommand(name) {
    return this.commands.get(name) || null;
  }

  /**
   * Get all commands
   * @returns {Map} - Map of all commands
   */
  getAllCommands() {
    return this.commands;
  }

  /**
   * Get the total number of commands loaded
   * @returns {number} - Number of commands
   */
  getCommandCount() {
    return this.commands.size;
  }
}

module.exports = AdvancedSubcommandHandler;
