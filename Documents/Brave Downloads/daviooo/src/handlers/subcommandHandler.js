// Perfect Subcommand Handler with Groups & 25-Command Limit Fix
const { SlashCommandBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/consoleLogger');

class PerfectSubcommandHandler {
  constructor(client) {
    this.client = client;
    this.commands = new Map();
    this.cooldowns = new Map();
    this.commandStats = new Map();

    // Define subcommand groups for better organization
    this.subcommandGroups = {
      automod: {
        'filters': ['invites', 'links', 'spam', 'caps', 'mentions', 'scam'],
        'raid': ['protection'],
        'settings': ['logs', 'immune-role', 'immune-channel']
      },
      moderation: {
        'user': ['ban', 'kick', 'timeout', 'untimeout', 'unban', 'warn', 'warnings', 'userinfo', 'nick'],
        'channel': ['lock', 'unlock', 'slowmode', 'clear', 'purge', 'snipe'],
        'server': ['serverinfo', 'membercount', 'announce', 'modpanel'],
        'roles': ['role-add', 'role-remove', 'role-all'],
        'reports': ['report', 'setreportchannel', 'modmsg']
      },
      economy: {
        'money': ['cash', 'checkbalance', 'money', 'bank', 'deposit', 'withdraw'],
        'earn': ['daily', 'work', 'beg', 'rob'],
        'games': ['coinflip', 'slots', 'lootbox'],
        'social': ['give', 'leaderboard']
      },
      fun: {
        'games': ['8ball', 'hack', 'gay', 'crushrate', 'iq'],
        'animals': ['cat', 'dog', 'animal', 'pug', 'woof'],
        'content': ['meme', 'joke', 'fact', 'randompic'],
        'interactive': ['emojify', 'randompicker', 'dailychallenge', 'cry', 'nitro']
      },
      tools: {
        'utility': ['translate', 'timer', 'poll'],
        'design': ['embed-builder', 'logo']
      }
    };
  }

  async loadCommands() {
    const commandsPath = path.join(__dirname, '../commands');
    const categories = fs.readdirSync(commandsPath);

    for (const category of categories) {
      const categoryPath = path.join(commandsPath, category);

      if (!fs.statSync(categoryPath).isDirectory()) continue;

      const commandFiles = fs.readdirSync(categoryPath).filter(file => file.endsWith('.js'));

      if (commandFiles.length === 0) continue;

      // Check if this category should use subcommand groups
      if (this.subcommandGroups[category]) {
        this.createGroupedCommand(category, categoryPath, commandFiles);
      } else {
        this.createRegularCommand(category, categoryPath, commandFiles);
      }
    }

    // Show summary
    this.showCommandSummary();
  }

  createGroupedCommand(category, categoryPath, commandFiles) {
    const groups = this.subcommandGroups[category];
    const subcommandExecutors = new Map();
    const loadedCommands = new Map();

    // Load all commands first
    for (const file of commandFiles) {
      try {
        const commandPath = path.join(categoryPath, file);
        const command = require(commandPath);

        if (!command.data || !command.execute) {
          continue;
        }

        const commandName = command.data.name;
        loadedCommands.set(commandName, command);
      } catch (error) {
        logger.error(`Error loading ${file}: ${error.message}`);
      }
    }

    // Create main command with groups
    const mainCommand = new SlashCommandBuilder()
      .setName(category)
      .setDescription(`${category.charAt(0).toUpperCase() + category.slice(1)} commands with organized groups`);

    // Add subcommand groups
    Object.entries(groups).forEach(([groupName, commandNames]) => {
      mainCommand.addSubcommandGroup(group => {
        group
          .setName(groupName)
          .setDescription(`${groupName.charAt(0).toUpperCase() + groupName.slice(1)} related commands`);

        // Add subcommands to this group
        commandNames.forEach(commandName => {
          const command = loadedCommands.get(commandName);
          if (command) {
            const subcommand = this.convertToSubcommand(command);
            if (subcommand) {
              group.addSubcommand(subcommand);
              subcommandExecutors.set(`${groupName}-${commandName}`, {
                executor: command.execute,
                originalCommand: command
              });
            }
          }
        });

        return group;
      });
    });

    // Add ungrouped commands as regular subcommands
    loadedCommands.forEach((command, commandName) => {
      const isGrouped = Object.values(groups).some(groupCommands =>
        groupCommands.includes(commandName)
      );

      if (!isGrouped) {
        const subcommand = this.convertToSubcommand(command);
        if (subcommand) {
          mainCommand.addSubcommand(subcommand);
          subcommandExecutors.set(commandName, {
            executor: command.execute,
            originalCommand: command
          });
        }
      }
    });

    // Create main executor for grouped commands
    const mainExecutor = async (interaction) => {
      let subcommandName, groupName;

      try {
        groupName = interaction.options.getSubcommandGroup();
        subcommandName = interaction.options.getSubcommand();
      } catch (error) {
        subcommandName = interaction.options.getSubcommand();
      }

      const commandKey = groupName ? `${groupName}-${subcommandName}` : subcommandName;
      const commandData = subcommandExecutors.get(commandKey);

      if (!commandData) {
        return await interaction.reply({
          content: '❌ Unknown subcommand!',
          ephemeral: true
        });
      }

      const { executor, originalCommand } = commandData;
      const userId = interaction.user.id;
      const cooldownKey = `${category}-${commandKey}`;

      try {
        // Check cooldowns
        if (this.isOnCooldown(userId, cooldownKey)) {
          const remainingTime = this.getCooldownTime(userId, cooldownKey);
          return await interaction.reply({
            content: `⏰ You're on cooldown! Please wait ${Math.ceil(remainingTime / 1000)} more seconds.`,
            ephemeral: true
          });
        }

        // Set cooldown
        this.setCooldown(userId, cooldownKey, 3000);

        // Update stats
        this.updateCommandStats(cooldownKey);

        // Execute command
        const startTime = Date.now();

        if (executor.length > 1) {
          await executor(interaction, this.client);
        } else {
          await executor(interaction);
        }

        // Log execution
        const executionTime = Date.now() - startTime;
        const fullCommandName = groupName ? `/${category} ${groupName} ${subcommandName}` : `/${category} ${subcommandName}`;
        logger.execute(fullCommandName, executionTime);

      } catch (error) {
        logger.error(`Command error in ${fullCommandName}: ${error.message}`);

        const errorMessage = {
          content: '❌ An error occurred while executing this command!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      }
    };

    // Store the command
    this.commands.set(category, {
      data: mainCommand,
      execute: mainExecutor
    });
  }

  createRegularCommand(category, categoryPath, commandFiles) {
    // Handle Discord's 25 subcommand limit
    const maxSubcommands = 25;
    let commandParts = [];
    let currentPart = [];

    for (const file of commandFiles) {
      try {
        const commandPath = path.join(categoryPath, file);
        const command = require(commandPath);

        if (!command.data || !command.execute) {
          continue;
        }

        currentPart.push({ file, command });

        // Split into parts if we hit the limit
        if (currentPart.length >= maxSubcommands) {
          commandParts.push(currentPart);
          currentPart = [];
        }
      } catch (error) {
        logger.error(`Error loading ${file}: ${error.message}`);
      }
    }

    // Add remaining commands
    if (currentPart.length > 0) {
      commandParts.push(currentPart);
    }

    // Create commands for each part
    commandParts.forEach((part, index) => {
      this.createCommandFromPart(category, part, index);
    });
  }

  createCommandFromPart(category, commandPart, partIndex) {
    const commandName = partIndex === 0 ? category : `${category}${partIndex + 1}`;
    const subcommandExecutors = new Map();

    // Create main command
    const mainCommand = new SlashCommandBuilder()
      .setName(commandName)
      .setDescription(`${category.charAt(0).toUpperCase() + category.slice(1)} commands${partIndex > 0 ? ` (Part ${partIndex + 1})` : ''}`);

    // Add subcommands
    commandPart.forEach(({ file, command }) => {
      const subcommand = this.convertToSubcommand(command);
      if (subcommand) {
        mainCommand.addSubcommand(subcommand);
        subcommandExecutors.set(subcommand.name, {
          executor: command.execute,
          originalCommand: command
        });
      }
    });

    // Create main executor
    const mainExecutor = async (interaction) => {
      const subcommandName = interaction.options.getSubcommand();
      const commandData = subcommandExecutors.get(subcommandName);

      if (!commandData) {
        return await interaction.reply({
          content: '❌ Unknown subcommand!',
          ephemeral: true
        });
      }

      const { executor, originalCommand } = commandData;
      const userId = interaction.user.id;
      const commandKey = `${commandName}-${subcommandName}`;

      try {
        // Check cooldowns
        if (this.isOnCooldown(userId, commandKey)) {
          const remainingTime = this.getCooldownTime(userId, commandKey);
          return await interaction.reply({
            content: `⏰ You're on cooldown! Please wait ${Math.ceil(remainingTime / 1000)} more seconds.`,
            ephemeral: true
          });
        }

        // Set cooldown
        this.setCooldown(userId, commandKey, 3000);

        // Update stats
        this.updateCommandStats(commandKey);

        // Execute command
        const startTime = Date.now();

        if (executor.length > 1) {
          await executor(interaction, this.client);
        } else {
          await executor(interaction);
        }

        // Log execution
        const executionTime = Date.now() - startTime;
        logger.execute(`/${commandName} ${subcommandName}`, executionTime);

      } catch (error) {
        logger.error(`Command error in /${commandName} ${subcommandName}: ${error.message}`);

        const errorMessage = {
          content: '❌ An error occurred while executing this command!',
          ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(errorMessage);
        } else {
          await interaction.reply(errorMessage);
        }
      }
    };

    // Store the command
    this.commands.set(commandName, {
      data: mainCommand,
      execute: mainExecutor
    });
  }

  convertToSubcommand(command) {
    if (!command.data) return null;

    const commandData = command.data.toJSON();
    const subcommandName = commandData.name;

    return (subcommand) => {
      subcommand
        .setName(subcommandName)
        .setDescription(commandData.description || `${subcommandName} command`);

      // Add options
      if (commandData.options) {
        commandData.options.forEach(option => {
          this.addOptionToSubcommand(subcommand, option);
        });
      }

      return subcommand;
    };
  }

  addOptionToSubcommand(subcommand, option) {
    try {
      switch (option.type) {
        case 1: // SUB_COMMAND
        case 2: // SUB_COMMAND_GROUP
          break; // Skip these
        case 3: // STRING
          subcommand.addStringOption(opt => this.configureOption(opt, option));
          break;
        case 4: // INTEGER
          subcommand.addIntegerOption(opt => this.configureOption(opt, option));
          break;
        case 5: // BOOLEAN
          subcommand.addBooleanOption(opt => this.configureOption(opt, option));
          break;
        case 6: // USER
          subcommand.addUserOption(opt => this.configureOption(opt, option));
          break;
        case 7: // CHANNEL
          subcommand.addChannelOption(opt => this.configureOption(opt, option));
          break;
        case 8: // ROLE
          subcommand.addRoleOption(opt => this.configureOption(opt, option));
          break;
        case 9: // MENTIONABLE
          subcommand.addMentionableOption(opt => this.configureOption(opt, option));
          break;
        case 10: // NUMBER
          subcommand.addNumberOption(opt => this.configureOption(opt, option));
          break;
        case 11: // ATTACHMENT
          subcommand.addAttachmentOption(opt => this.configureOption(opt, option));
          break;
      }
    } catch (error) {
      // Silent skip for unknown option types
    }
  }

  configureOption(optionBuilder, option) {
    optionBuilder
      .setName(option.name)
      .setDescription(option.description || 'No description')
      .setRequired(option.required || false);

    if (option.choices) {
      optionBuilder.addChoices(...option.choices);
    }

    return optionBuilder;
  }

  // Cooldown management
  isOnCooldown(userId, commandKey) {
    const cooldownKey = `${userId}-${commandKey}`;
    const cooldownEnd = this.cooldowns.get(cooldownKey);
    return cooldownEnd && Date.now() < cooldownEnd;
  }

  getCooldownTime(userId, commandKey) {
    const cooldownKey = `${userId}-${commandKey}`;
    const cooldownEnd = this.cooldowns.get(cooldownKey);
    return cooldownEnd ? cooldownEnd - Date.now() : 0;
  }

  setCooldown(userId, commandKey, duration = 3000) {
    const cooldownKey = `${userId}-${commandKey}`;
    this.cooldowns.set(cooldownKey, Date.now() + duration);

    // Auto-cleanup after cooldown
    setTimeout(() => {
      this.cooldowns.delete(cooldownKey);
    }, duration);
  }

  // Stats management
  updateCommandStats(commandKey) {
    const current = this.commandStats.get(commandKey) || 0;
    this.commandStats.set(commandKey, current + 1);
  }

  // Get commands for registration
  getCommandsJSON() {
    return Array.from(this.commands.values()).map(command => command.data.toJSON());
  }

  // Get command executor
  getCommand(commandName) {
    return this.commands.get(commandName);
  }

  // Show command summary
  showCommandSummary() {
    const commandItems = [];
    for (const [category, command] of this.commands) {
      const groupInfo = this.subcommandGroups[category] ? ' (with groups)' : '';
      commandItems.push(`${category}${groupInfo}`);
    }

    logger.showBox(commandItems, 'SLASH COMMANDS', 'green');
  }
}

module.exports = PerfectSubcommandHandler;