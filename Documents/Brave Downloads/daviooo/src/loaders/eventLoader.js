// Loads all events from the events folder with beautiful UI
const fs = require('fs');
const path = require('path');
const logger = require('../utils/consoleLogger');

function loadEvents(client) {
  const eventsPath = path.join(__dirname, '../events');
  const files = fs.readdirSync(eventsPath).filter(f => f.endsWith('.js'));

  const eventList = [];

  for (const file of files) {
    try {
      const event = require(path.join(eventsPath, file));
      const eventName = file.replace('.js', '');

      if (event.once) {
        client.once(event.name, (...args) => event.execute(...args, client));
      } else {
        client.on(event.name, (...args) => event.execute(...args, client));
      }

      // Silent loading - will be shown in summary
      eventList.push({ name: eventName, loaded: true });
    } catch (error) {
      logger.error(`Failed to load event ${file}: ${error.message}`);
      eventList.push({ name: file.replace('.js', ''), loaded: false });
    }
  }

  // Create beautiful event status box
  const eventItems = eventList.map(event => event.name);

  setTimeout(() => {
    logger.showBox(eventItems, 'EVENT STATUS', 'yellow');
  }, 1600);
}

module.exports = { loadEvents };