const fs   = require('fs');
const path = require('path');
const chalk = require('chalk');

function loadCommands(client) {
  const commandsPath = path.join(__dirname, '../commands');
  let count = 0;

  // Durch jeden Kategorie-Ordner in /commands
  fs.readdirSync(commandsPath).forEach(category => {
    const categoryPath = path.join(commandsPath, category);
    if (!fs.lstatSync(categoryPath).isDirectory()) return;

    // Lade *nur* die index.js in diesem Ordner
    const entry = path.join(categoryPath, 'index.js');
    if (!fs.existsSync(entry)) {
      // Silent skip - no index.js found
      return;
    }

    try {
      const command = require(entry);
      if (command.data && command.execute) {
        client.commands.set(command.data.name, command);
        // Silent loading
        count++;
      } else {
        console.log(chalk.red(`❌ Invalid category command in ${entry}!`));
      }
    } catch (error) {
      console.log(chalk.red(`❌ Error loading command from ${entry}:`), error);
    }
  });

  console.log(chalk.blueBright(`\n🚀 Successfully loaded ${chalk.bold(count)} category commands!\n`));
}

module.exports = { loadCommands };