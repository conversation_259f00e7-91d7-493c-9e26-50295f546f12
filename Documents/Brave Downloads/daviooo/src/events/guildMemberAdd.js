// 🛡️ GUILD MEMBER ADD EVENT - RAID PROTECTION & AUTOMOD INTEGRATION
// Professional member join handling with enterprise-grade security

const { Events } = require('discord.js');
const { handleRaidProtection } = require('../handlers/automodHandler');

module.exports = {
  name: Events.GuildMemberAdd,
  async execute(member) {
    try {
      // 🔒 Raid Protection Check
      await handleRaidProtection(member);
      
      // 📊 Log member join (optional)
      console.log(`🆕 New member joined ${member.guild.name}: ${member.user.tag}`);
      
    } catch (error) {
      console.error('Guild Member Add Event Error:', error);
    }
  }
};
