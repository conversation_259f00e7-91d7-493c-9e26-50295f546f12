// ✏️ MESSAGE UPDATE EVENT - EDIT TRACKING SYSTEM
// Professional message edit tracking for moderation and snipe functionality

const { Events } = require('discord.js');
const { handleAutomod } = require('../handlers/automodHandler');

// 📝 EDIT CACHE
const editCache = new Map();
const MAX_EDITS_PER_CHANNEL = 25;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

module.exports = {
  name: Events.MessageUpdate,
  async execute(oldMessage, newMessage) {
    try {
      // Skip if bot message, system message, or no content change
      if (newMessage.author.bot || newMessage.system) return;
      if (oldMessage.content === newMessage.content) return;
      
      // Skip if no guild (DMs)
      if (!newMessage.guild) return;
      
      // 🛡️ RE-RUN AUTOMOD ON EDITED MESSAGE
      await handleAutomod(newMessage);
      
      // 📝 TRACK MESSAGE EDIT FOR SNIPE FUNCTIONALITY
      const editData = {
        messageId: newMessage.id,
        oldContent: oldMessage.content,
        newContent: newMessage.content,
        author: {
          id: newMessage.author.id,
          tag: newMessage.author.tag,
          displayName: newMessage.author.displayName,
          avatar: newMessage.author.displayAvatarURL()
        },
        channel: {
          id: newMessage.channel.id,
          name: newMessage.channel.name
        },
        guild: {
          id: newMessage.guild.id,
          name: newMessage.guild.name
        },
        oldAttachments: oldMessage.attachments.map(att => ({
          name: att.name,
          url: att.url,
          size: att.size
        })),
        newAttachments: newMessage.attachments.map(att => ({
          name: att.name,
          url: att.url,
          size: att.size
        })),
        editedAt: newMessage.editedTimestamp,
        cachedAt: Date.now()
      };
      
      // Store in edit cache
      const channelKey = `${newMessage.guild.id}-${newMessage.channel.id}`;
      if (!editCache.has(channelKey)) {
        editCache.set(channelKey, []);
      }
      
      const channelEdits = editCache.get(channelKey);
      channelEdits.unshift(editData);
      
      // Limit cache size per channel
      if (channelEdits.length > MAX_EDITS_PER_CHANNEL) {
        channelEdits.splice(MAX_EDITS_PER_CHANNEL);
      }
      
      // 🧹 Cleanup old cache entries
      cleanupEditCache();
      
      // 📊 LOG EDIT FOR MODERATION
      console.log(`✏️ Message edited in ${newMessage.guild.name}/#${newMessage.channel.name} by ${newMessage.author.tag}`);
      
    } catch (error) {
      console.error('✏️ Message Update Event Error:', error);
    }
  }
};

// 🧹 EDIT CACHE CLEANUP
function cleanupEditCache() {
  const now = Date.now();
  
  for (const [channelKey, edits] of editCache.entries()) {
    const validEdits = edits.filter(edit => 
      now - edit.cachedAt < CACHE_DURATION
    );
    
    if (validEdits.length === 0) {
      editCache.delete(channelKey);
    } else {
      editCache.set(channelKey, validEdits);
    }
  }
}

// 📤 EXPORT CACHE ACCESS
module.exports.getEditCache = () => editCache;

// Run cleanup every hour
setInterval(cleanupEditCache, 60 * 60 * 1000);
