const { ActivityType, PresenceUpdateStatus } = require("discord.js");
const axios = require('axios');
const fs = require('fs');
const path = require('path');

module.exports = {
  name: 'ready',
  once: true,
  execute(client) {
    // Silent initialization

    // Cache variables for performance optimization
    let totalUsers = 0;
    let totalGuilds = 0;
    let fakeUserCount = 0;

    /**
     * Persistent fake user count management
     */
    class UserCountManager {
      constructor() {
        this.dataPath = path.join(__dirname, '../data/usercount.json');
        this.ensureDataDirectory();
        this.data = this.loadData();
      }

      ensureDataDirectory() {
        const dataDir = path.dirname(this.dataPath);
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }
      }

      loadData() {
        try {
          if (fs.existsSync(this.dataPath)) {
            const data = JSON.parse(fs.readFileSync(this.dataPath, 'utf8'));
            // Silent loading
            return data;
          }
        } catch (error) {
          console.log('⚠️  Could not load user count data, starting fresh');
        }
        
        return {
          count: 50000, // Starting count
          lastUpdate: Date.now(),
          baseGrowth: 10, // Base growth per interval
          lastApiUpdate: 0
        };
      }

      saveData() {
        try {
          fs.writeFileSync(this.dataPath, JSON.stringify(this.data, null, 2));
        } catch (error) {
          console.error('❌ Error saving user count data:', error.message);
        }
      }

      async fetchApiUserCount() {
        // Try multiple APIs to get realistic user counts
        const apis = [
          {
            name: 'JSONPlaceholder Users',
            url: 'https://jsonplaceholder.typicode.com/users',
            extract: (data) => data.length * 5000 // 10 users * 5000 = 50k base
          },
          {
            name: 'Discord Bot Lists',
            url: 'https://top.gg/api/bots/235148962103951360', // Carl-bot ID
            extract: (data) => Math.floor((data.server_count || 0) / 100) // Convert servers to users
          },
          {
            name: 'GitHub API',
            url: 'https://api.github.com/repos/microsoft/vscode/stargazers?per_page=1',
            extract: (data, headers) => {
              const link = headers.link;
              if (link) {
                const match = link.match(/page=(\d+)>; rel="last"/);
                return match ? Math.floor(parseInt(match[1]) / 10) : 75000;
              }
              return 75000;
            }
          }
        ];

        for (const api of apis) {
          try {
            // Silent fetching
            const response = await axios.get(api.url, { 
              timeout: 5000,
              headers: { 'User-Agent': 'Discord-Bot-Stats/1.0' }
            });
            
            const extractedCount = api.extract(response.data, response.headers);
            if (extractedCount && extractedCount > 1000) {
              // Silent success
              return extractedCount;
            }
          } catch (error) {
            console.log(`⚠️  ${api.name} failed: ${error.message}`);
          }
        }

        // Fallback: Generate realistic count
        return this.generateRealisticCount();
      }

      generateRealisticCount() {
        // Generate a realistic growing count based on time
        const baseCount = 45000;
        const daysPassed = (Date.now() - 1704067200000) / (1000 * 60 * 60 * 24); // Since Jan 1, 2024
        const timeGrowth = Math.floor(daysPassed * 15); // 15 users per day
        const randomBoost = Math.floor(Math.random() * 10000) + 5000; // 5k-15k random
        
        return baseCount + timeGrowth + randomBoost;
      }

      async updateCount() {
        const now = Date.now();
        
        // Fetch from API every 30 minutes
        if (now - this.data.lastApiUpdate > 1800000) {
          const apiCount = await this.fetchApiUserCount();
          
          // Only update if API count is significantly higher
          if (apiCount > this.data.count) {
            this.data.count = apiCount;
            // Silent update
          }
          
          this.data.lastApiUpdate = now;
        }

        // Regular growth every 2-5 minutes
        if (now - this.data.lastUpdate > 180000) { // 3 minutes
          const growth = this.data.baseGrowth + Math.floor(Math.random() * 15); // 10-25 growth
          this.data.count += growth;
          this.data.lastUpdate = now;
          
          // Silent growth
        }

        this.saveData();
        return this.data.count;
      }

      getCurrentCount() {
        return this.data.count;
      }
    }

    const userCountManager = new UserCountManager();

    /**
     * Updates cached statistics for optimal performance
     * Called every 5 minutes to reduce CPU load from frequent calculations
     */
    const updateStats = async () => {
      totalUsers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
      totalGuilds = client.guilds.cache.size;
      fakeUserCount = await userCountManager.updateCount();
    };

    /**
     * Main function to manage bot activity rotation
     */
    const updateActivity = () => {
      // Initialize stats
      updateStats();
      
      // Update stats every 3 minutes for better growth visibility
      setInterval(updateStats, 180000);

      const statuses = [
        {
          name: "/help ⎮ davio.top",
          type: ActivityType.Streaming,
          url: 'https://www.twitch.tv/epicdavidde',
          duration: 15000, // 15 seconds - professional duration
          status: PresenceUpdateStatus.DoNotDisturb,
        },
        {
          name: "with ${fakeUserCount} Users! 👥",
          type: ActivityType.Playing,
          duration: 30000, // 30 seconds - longer duration for better mobile icon stability
          status: PresenceUpdateStatus.Idle, // Idle status works better with mobile icon
          mobile: true, // Custom property to indicate mobile status
        },
        {
          name: "Serving ${totalGuilds}+ Servers! ⌂",
          type: ActivityType.Custom,
          duration: 18000, // 18 seconds - extended professional duration
          status: PresenceUpdateStatus.DoNotDisturb,
        },
        {
          name: "/help ⎮ davio.top",
          type: ActivityType.Streaming,
          url: 'https://www.twitch.tv/epicdavidde',
          duration: 15000, // 15 seconds - professional duration
          status: PresenceUpdateStatus.DoNotDisturb,
        },
      ];

      let currentIndex = 0;

      /**
       * OPTION C: Recursive function with extra pause between status changes
       * Adds 2 seconds buffer time for Discord to properly register status changes
       */
      const setNextActivity = () => {
        // Create a copy of the current status to avoid mutating the original array
        const currentStatus = { ...statuses[currentIndex] };

        // Replace placeholders with current cached values
        currentStatus.name = currentStatus.name
          .replace("${fakeUserCount}", fakeUserCount.toLocaleString())
          .replace("${totalGuilds}", totalGuilds);

        try {
          // Set bot presence with mobile optimization
          if (currentStatus.mobile) {
            // Display mobile icon by setting afk to true
            client.user.setPresence({ 
              activities: [currentStatus], 
              status: currentStatus.status, 
              afk: true 
            });
          } else {
            client.user.setPresence({ 
              activities: [currentStatus], 
              status: currentStatus.status, 
              afk: false 
            });
          }

          // OPTION C: Schedule next activity change with extra 2-second buffer
          setTimeout(() => {
            currentIndex = (currentIndex + 1) % statuses.length;
            setNextActivity();
          }, currentStatus.duration + 2000); // +2 seconds extra pause for Discord processing

        } catch (error) {
          console.error(`❌ Error setting bot activity:`, error);
          
          // Retry after 10 seconds on error
          setTimeout(() => {
            setNextActivity();
          }, 10000);
        }
      };

      // Initialize activity rotation
      setNextActivity();
    };

    // Start the activity management system
    updateActivity();
    
    // Silent startup - activity system running
  }
};