const { ActivityType, PresenceUpdateStatus } = require("discord.js");
const chalk = require('chalk');

// 🎨 BEAUTIFUL LOGGER CLASS FOR READY EVENT
class BeautifulReadyLogger {
  static startup(client) {
    console.log(chalk.green('┌─ BOT STATUS ──────────────────────────────────────────────────┐'));
    console.log(chalk.green(`│ 🎉 DAVIO BOT IS NOW ONLINE AND READY!                       │`));
    console.log(chalk.green('├──────────────────────────────────────────────────────────────┤'));
    console.log(chalk.green(`│ 🤖 Bot: ${client.user.tag.padEnd(47)} │`));
    console.log(chalk.green(`│ 🏰 Servers: ${client.guilds.cache.size.toString().padEnd(43)} │`));
    console.log(chalk.green(`│ 👥 Users: ${client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0).toString().padEnd(45)} │`));
    console.log(chalk.green(`│ ⚡ Commands: ${client.commands?.size || '122'.padEnd(41)} │`));
    console.log(chalk.green('└──────────────────────────────────────────────────────────────┘'));
  }

  static activityUpdate(activity, userCount, serverCount) {
    console.log(chalk.blue('┌─ ACTIVITY UPDATE ─────────────────────────────────────────────┐'));
    console.log(chalk.blue(`│ 🎮 Status: ${activity.padEnd(44)} │`));
    console.log(chalk.blue(`│ 👥 Users: ${userCount.toLocaleString().padEnd(45)} │`));
    console.log(chalk.blue(`│ 🏰 Servers: ${serverCount.toString().padEnd(43)} │`));
    console.log(chalk.blue('└──────────────────────────────────────────────────────────────┘'));
  }

  static systemReady() {
    console.log(chalk.magenta('┌─ SYSTEM STATUS ───────────────────────────────────────────────┐'));
    console.log(chalk.magenta('│ ✅ All systems operational and ready for commands!         │'));
    console.log(chalk.magenta('│ 🚀 Bot is now serving users across all servers!            │'));
    console.log(chalk.magenta('└──────────────────────────────────────────────────────────────┘'));
  }
}

module.exports = {
  name: 'ready',
  once: true,
  execute(client) {
    // 🎨 BEAUTIFUL STARTUP SEQUENCE
    BeautifulReadyLogger.startup(client);

    // Cache variables for performance optimization
    let totalUsers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
    let totalGuilds = client.guilds.cache.size;
    let displayUserCount = totalUsers;

    /**
     * 🎨 BEAUTIFUL ACTIVITY ROTATION SYSTEM
     */
    const updateActivity = () => {
      const statuses = [
        {
          name: "/help ⎮ davio.top",
          type: ActivityType.Streaming,
          url: 'https://www.twitch.tv/epicdavidde',
          duration: 15000,
          status: PresenceUpdateStatus.DoNotDisturb,
        },
        {
          name: `with ${totalUsers.toLocaleString()} Users! 👥`,
          type: ActivityType.Playing,
          duration: 20000,
          status: PresenceUpdateStatus.Online,
        },
        {
          name: `Serving ${totalGuilds}+ Servers! 🏰`,
          type: ActivityType.Custom,
          duration: 18000,
          status: PresenceUpdateStatus.Idle,
        },
        {
          name: "🎮 122+ Commands Available!",
          type: ActivityType.Playing,
          duration: 16000,
          status: PresenceUpdateStatus.Online,
        },
      ];

      let currentIndex = 0;

      const setNextActivity = () => {
        const currentStatus = statuses[currentIndex];

        try {
          client.user.setPresence({
            activities: [currentStatus],
            status: currentStatus.status
          });

          // 🎨 BEAUTIFUL ACTIVITY LOG
          BeautifulReadyLogger.activityUpdate(
            currentStatus.name,
            totalUsers,
            totalGuilds
          );

          setTimeout(() => {
            currentIndex = (currentIndex + 1) % statuses.length;
            setNextActivity();
          }, currentStatus.duration);

        } catch (error) {
          console.log(chalk.red('┌─ ACTIVITY ERROR ──────────────────────────────────────────────┐'));
          console.log(chalk.red('│ ❌ Error setting bot activity - retrying in 10 seconds...   │'));
          console.log(chalk.red('└──────────────────────────────────────────────────────────────┘'));

          setTimeout(() => {
            setNextActivity();
          }, 10000);
        }
      };

      // Initialize activity rotation
      setNextActivity();
    };

    // 🚀 START BEAUTIFUL ACTIVITY SYSTEM
    updateActivity();

    // 🎨 FINAL BEAUTIFUL READY MESSAGE
    setTimeout(() => {
      BeautifulReadyLogger.systemReady();
    }, 2000);
  }
};