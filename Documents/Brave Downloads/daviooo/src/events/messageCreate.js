// 📝 MESSAGE CREATE EVENT - AUTOMOD INTEGRATION
// Professional message filtering and content moderation

const { Events } = require('discord.js');
const { handleAutomod } = require('../handlers/automodHandler');

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // 🛡️ Automod Check
      await handleAutomod(message);
      
    } catch (error) {
      console.error('Message Create Event Error:', error);
    }
  }
};
