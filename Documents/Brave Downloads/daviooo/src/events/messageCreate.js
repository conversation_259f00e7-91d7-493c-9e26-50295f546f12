// 📝 MASTER MESSAGE CREATE EVENT - PROFESSIONAL EVENT COORDINATION
// Orchestrates all message-based features with optimal performance

const { Events } = require('discord.js');

// 🎯 IMPORT ALL MESSAGE HANDLERS
const automodHandler = require('./messageCreate/automod');
const economyHandler = require('./messageCreate/economy');
const snipeHandler = require('./messageCreate/snipe');
const afkHandler = require('./messageCreate/afk');
const chatbotHandler = require('./messageCreate/chatbot');
const statisticsHandler = require('./messageCreate/statistics');

// 📊 PERFORMANCE TRACKING
const performanceStats = {
  totalMessages: 0,
  handlerExecutions: {
    automod: 0,
    economy: 0,
    snipe: 0,
    afk: 0,
    chatbot: 0,
    statistics: 0
  },
  errors: 0,
  averageExecutionTime: 0
};

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    const startTime = Date.now();
    
    try {
      // 📊 Update performance stats
      performanceStats.totalMessages++;
      
      // Skip if no guild (DMs)
      if (!message.guild) return;
      
      // 🎯 EXECUTE ALL HANDLERS IN PARALLEL FOR OPTIMAL PERFORMANCE
      const handlerPromises = [
        // 🛡️ SECURITY FIRST - Automod has highest priority
        executeHandler('automod', automodHandler, message),
        
        // 📊 ANALYTICS - Statistics tracking
        executeHandler('statistics', statisticsHandler, message),
        
        // 👀 MODERATION - Snipe tracking
        executeHandler('snipe', snipeHandler, message),
        
        // 😴 USER EXPERIENCE - AFK system
        executeHandler('afk', afkHandler, message),
        
        // 💰 ENGAGEMENT - Economy system
        executeHandler('economy', economyHandler, message),
        
        // 🤖 INTERACTION - Chatbot (lowest priority)
        executeHandler('chatbot', chatbotHandler, message)
      ];
      
      // Wait for all handlers to complete
      await Promise.allSettled(handlerPromises);
      
      // 📊 Update performance metrics
      const executionTime = Date.now() - startTime;
      updatePerformanceStats(executionTime);
      
    } catch (error) {
      console.error('📝 Master Message Event Error:', error);
      performanceStats.errors++;
    }
  }
};

// 🎯 HANDLER EXECUTION WITH ERROR ISOLATION
async function executeHandler(handlerName, handler, message) {
  try {
    await handler.execute(message);
    performanceStats.handlerExecutions[handlerName]++;
  } catch (error) {
    console.error(`📝 ${handlerName.toUpperCase()} Handler Error:`, error);
    performanceStats.errors++;
  }
}

// 📊 PERFORMANCE STATISTICS UPDATE
function updatePerformanceStats(executionTime) {
  const currentAvg = performanceStats.averageExecutionTime;
  const totalMessages = performanceStats.totalMessages;
  
  // Calculate rolling average
  performanceStats.averageExecutionTime = 
    ((currentAvg * (totalMessages - 1)) + executionTime) / totalMessages;
}

// 📊 PERFORMANCE MONITORING (Every 10 minutes)
setInterval(() => {
  if (performanceStats.totalMessages > 0) {
    console.log(`
┌─────────────────────────────────────────────────────────────────────────────┐
│                           📊 MESSAGE EVENT PERFORMANCE                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ Total Messages: ${performanceStats.totalMessages.toString().padEnd(10)} │ Avg Time: ${performanceStats.averageExecutionTime.toFixed(2)}ms │
│ Errors: ${performanceStats.errors.toString().padEnd(16)} │ Success Rate: ${((1 - performanceStats.errors / performanceStats.totalMessages) * 100).toFixed(1)}% │
├─────────────────────────────────────────────────────────────────────────────┤
│ Handler Executions:                                                         │
│ • Automod: ${performanceStats.handlerExecutions.automod.toString().padEnd(8)} │ • Economy: ${performanceStats.handlerExecutions.economy.toString().padEnd(8)} │
│ • Snipe: ${performanceStats.handlerExecutions.snipe.toString().padEnd(10)} │ • AFK: ${performanceStats.handlerExecutions.afk.toString().padEnd(12)} │
│ • Chatbot: ${performanceStats.handlerExecutions.chatbot.toString().padEnd(8)} │ • Stats: ${performanceStats.handlerExecutions.statistics.toString().padEnd(10)} │
└─────────────────────────────────────────────────────────────────────────────┘`);
  }
}, 10 * 60 * 1000);

// 📤 EXPORT PERFORMANCE STATS
module.exports.getPerformanceStats = () => performanceStats;
