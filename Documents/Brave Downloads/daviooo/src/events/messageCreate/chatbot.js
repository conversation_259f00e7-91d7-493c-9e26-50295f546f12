// 🤖 CHATBOT MESSAGE EVENT - AI CONVERSATION SYSTEM
// Professional AI chatbot integration for natural conversations

const { Events } = require('discord.js');

// 🤖 CHATBOT SETTINGS
const CHATBOT_CONFIG = {
  TRIGGER_WORDS: ['davio', 'bot', 'help', 'hallo', 'hi', 'hey'],
  RESPONSE_CHANCE: 0.1, // 10% chance to respond to random messages
  COOLDOWN: 30000, // 30 seconds cooldown per user
  MAX_LENGTH: 2000 // Max response length
};

// 💬 SIMPLE RESPONSES
const RESPONSES = {
  greetings: [
    '👋 Hallo! Wie kann ich dir helfen?',
    '🎉 Hey! Schön dich zu sehen!',
    '✨ Hi! Was kann ich für dich tun?',
    '🤖 Hallo! Ich bin DAVIO, dein Bot-Assistent!'
  ],
  help: [
    '❓ Brauchst du Hilfe? Verwende `/help` für alle Commands!',
    '🔧 Ich kann dir mit vielen Sachen helfen! <PERSON>hau dir `/help` an.',
    '📚 Für eine vollständige Liste meiner Features, nutze `/help`!',
    '⚡ Ich habe viele coole Commands! Probiere `/help` aus!'
  ],
  random: [
    '🎲 Das ist interessant!',
    '💭 Hmm, erzähl mir mehr!',
    '🤔 Das klingt spannend!',
    '✨ Cool! Was denkst du darüber?',
    '🎯 Guter Punkt!',
    '🔥 Das ist awesome!',
    '💡 Interessante Perspektive!',
    '🌟 Das gefällt mir!'
  ],
  thanks: [
    '😊 Gerne! Immer da um zu helfen!',
    '🎉 Kein Problem! Das mache ich gerne!',
    '✨ Freut mich, dass ich helfen konnte!',
    '💖 Immer gerne! Bis bald!'
  ]
};

// ⏰ USER COOLDOWNS
const cooldowns = new Map();

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Skip if bot message, system message, or command
      if (message.author.bot || message.system || message.content.startsWith('/')) return;
      
      // Skip if message too short
      if (message.content.length < 3) return;
      
      const userId = message.author.id;
      const content = message.content.toLowerCase();
      const now = Date.now();
      
      // Check cooldown
      if (cooldowns.has(userId)) {
        const expirationTime = cooldowns.get(userId) + CHATBOT_CONFIG.COOLDOWN;
        if (now < expirationTime) return;
      }
      
      // 🎯 Check for trigger conditions
      let shouldRespond = false;
      let responseType = 'random';
      
      // Direct mention or bot name
      if (message.mentions.has(message.client.user) || 
          CHATBOT_CONFIG.TRIGGER_WORDS.some(word => content.includes(word))) {
        shouldRespond = true;
        
        // Determine response type
        if (content.includes('hallo') || content.includes('hi') || content.includes('hey')) {
          responseType = 'greetings';
        } else if (content.includes('help') || content.includes('hilfe')) {
          responseType = 'help';
        } else if (content.includes('danke') || content.includes('thanks')) {
          responseType = 'thanks';
        }
      }
      // Random response chance
      else if (Math.random() < CHATBOT_CONFIG.RESPONSE_CHANCE) {
        shouldRespond = true;
      }
      
      if (shouldRespond) {
        // Set cooldown
        cooldowns.set(userId, now);
        
        // 🤖 Generate response
        const responses = RESPONSES[responseType];
        const response = responses[Math.floor(Math.random() * responses.length)];
        
        // 💬 Send response with typing indicator
        await message.channel.sendTyping();
        
        // Random delay for more natural feel
        const delay = Math.random() * 2000 + 1000; // 1-3 seconds
        setTimeout(async () => {
          try {
            await message.reply({
              content: response,
              allowedMentions: { repliedUser: false }
            });
          } catch (error) {
            console.error('Chatbot Response Error:', error);
          }
        }, delay);
      }
      
    } catch (error) {
      console.error('🤖 Chatbot Message Event Error:', error);
    }
  }
};
