// 😴 AFK MESSAGE EVENT - AWAY FROM KEYBOARD SYSTEM
// Professional AFK status management and notifications

const { Events } = require('discord.js');
const AFK = require('../../database/models/afk');

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Skip if bot message or system message
      if (message.author.bot || message.system) return;
      
      const userId = message.author.id;
      const guildId = message.guild.id;
      
      // 🔍 Check if user is AFK and remove status
      const userAFK = await AFK.findOne({ userId, guildId });
      if (userAFK) {
        // Remove AFK status
        await AFK.deleteOne({ userId, guildId });
        
        // 👋 Welcome back message
        const welcomeEmbed = {
          color: 0x00D166,
          title: '👋 **Welcome Back!**',
          description: `${message.author}, you're no longer AFK!`,
          fields: [
            {
              name: '😴 **Previous Status**',
              value: userAFK.reason || 'No reason provided',
              inline: true
            },
            {
              name: '⏰ **Duration**',
              value: `<t:${Math.floor(userAFK.timestamp / 1000)}:R>`,
              inline: true
            }
          ],
          thumbnail: { url: message.author.displayAvatarURL() },
          timestamp: new Date().toISOString()
        };
        
        const welcomeMsg = await message.channel.send({ embeds: [welcomeEmbed] });
        setTimeout(() => welcomeMsg.delete().catch(() => {}), 10000);
      }
      
      // 🔍 Check for AFK mentions
      if (message.mentions.users.size > 0) {
        for (const [mentionedUserId, mentionedUser] of message.mentions.users) {
          // Skip if mentioning self
          if (mentionedUserId === userId) continue;
          
          const mentionedAFK = await AFK.findOne({ 
            userId: mentionedUserId, 
            guildId 
          });
          
          if (mentionedAFK) {
            // 😴 AFK notification
            const afkEmbed = {
              color: 0xFFB02E,
              title: '😴 **User is AFK**',
              description: `${mentionedUser} is currently away`,
              fields: [
                {
                  name: '📝 **Reason**',
                  value: mentionedAFK.reason || 'No reason provided',
                  inline: true
                },
                {
                  name: '⏰ **Since**',
                  value: `<t:${Math.floor(mentionedAFK.timestamp / 1000)}:R>`,
                  inline: true
                }
              ],
              thumbnail: { url: mentionedUser.displayAvatarURL() },
              timestamp: new Date().toISOString()
            };
            
            const afkMsg = await message.channel.send({ embeds: [afkEmbed] });
            setTimeout(() => afkMsg.delete().catch(() => {}), 15000);
          }
        }
      }
      
    } catch (error) {
      console.error('😴 AFK Message Event Error:', error);
    }
  }
};
