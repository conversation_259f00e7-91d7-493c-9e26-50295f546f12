// 💰 ECONOMY MESSAGE EVENT - CURRENCY & XP SYSTEM
// Professional economy system with message-based rewards

const { Events } = require('discord.js');
const Economy = require('../../database/models/economy');

// 💎 ECONOMY SETTINGS
const REWARDS = {
  MESSAGE_COINS: { min: 1, max: 5 },
  MESSAGE_XP: { min: 10, max: 25 },
  COOLDOWN: 60000 // 1 minute cooldown
};

// 📊 USER COOLDOWNS
const cooldowns = new Map();

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Skip if bot message, system message, or command
      if (message.author.bot || message.system || message.content.startsWith('/')) return;
      
      // Skip if message too short
      if (message.content.length < 5) return;
      
      const userId = message.author.id;
      const guildId = message.guild.id;
      const now = Date.now();
      
      // Check cooldown
      const cooldownKey = `${userId}-${guildId}`;
      if (cooldowns.has(cooldownKey)) {
        const expirationTime = cooldowns.get(cooldownKey) + REWARDS.COOLDOWN;
        if (now < expirationTime) return;
      }
      
      // Set new cooldown
      cooldowns.set(cooldownKey, now);
      
      // 💰 Award coins and XP
      const coins = Math.floor(Math.random() * (REWARDS.MESSAGE_COINS.max - REWARDS.MESSAGE_COINS.min + 1)) + REWARDS.MESSAGE_COINS.min;
      const xp = Math.floor(Math.random() * (REWARDS.MESSAGE_XP.max - REWARDS.MESSAGE_XP.min + 1)) + REWARDS.MESSAGE_XP.min;
      
      // Get or create user economy data
      let userEconomy = await Economy.findOne({ userId, guildId });
      if (!userEconomy) {
        userEconomy = new Economy({ userId, guildId });
      }
      
      // Add rewards
      userEconomy.balance += coins;
      userEconomy.xp += xp;
      
      // Check for level up
      const newLevel = Math.floor(userEconomy.xp / 1000) + 1;
      if (newLevel > userEconomy.level) {
        userEconomy.level = newLevel;
        
        // 🎉 Level up notification (optional)
        if (Math.random() < 0.3) { // 30% chance to show level up
          const levelUpEmbed = {
            color: 0x00D166,
            title: '🎉 **Level Up!**',
            description: `${message.author} reached **Level ${newLevel}**!`,
            thumbnail: { url: message.author.displayAvatarURL() },
            timestamp: new Date().toISOString()
          };
          
          const levelMsg = await message.channel.send({ embeds: [levelUpEmbed] });
          setTimeout(() => levelMsg.delete().catch(() => {}), 10000);
        }
      }
      
      await userEconomy.save();
      
    } catch (error) {
      console.error('💰 Economy Message Event Error:', error);
    }
  }
};
