// 🛡️ AUTOMOD MESSAGE EVENT - ENTERPRISE SECURITY FILTERING
// Professional content moderation and threat detection

const { Events } = require('discord.js');
const { handleAutomod } = require('../../handlers/automodHandler');

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Skip if bot message or system message
      if (message.author.bot || message.system) return;
      
      // 🛡️ Execute Automod Protection
      await handleAutomod(message);
      
    } catch (error) {
      console.error('🛡️ Automod Message Event Error:', error);
    }
  }
};
