// 📊 STATISTICS MESSAGE EVENT - SERVER ANALYTICS SYSTEM
// Professional message tracking and server statistics

const { Events } = require('discord.js');
const ServerStats = require('../../database/models/serverStats');

// 📈 STATISTICS TRACKING
const statsCache = new Map();
const SAVE_INTERVAL = 60000; // Save every minute

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Skip if bot message or system message
      if (message.author.bot || message.system) return;
      
      const guildId = message.guild.id;
      const userId = message.author.id;
      const channelId = message.channel.id;
      const now = new Date();
      const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
      
      // 📊 Initialize cache for guild if not exists
      if (!statsCache.has(guildId)) {
        statsCache.set(guildId, {
          totalMessages: 0,
          todayMessages: 0,
          users: new Map(),
          channels: new Map(),
          lastSave: Date.now()
        });
      }
      
      const guildStats = statsCache.get(guildId);
      
      // 📈 Update message counts
      guildStats.totalMessages++;
      guildStats.todayMessages++;
      
      // 👤 Update user stats
      if (!guildStats.users.has(userId)) {
        guildStats.users.set(userId, {
          messages: 0,
          lastActive: now
        });
      }
      const userStats = guildStats.users.get(userId);
      userStats.messages++;
      userStats.lastActive = now;
      
      // 📝 Update channel stats
      if (!guildStats.channels.has(channelId)) {
        guildStats.channels.set(channelId, {
          messages: 0,
          name: message.channel.name
        });
      }
      const channelStats = guildStats.channels.get(channelId);
      channelStats.messages++;
      channelStats.name = message.channel.name; // Update name in case it changed
      
      // 💾 Save to database periodically
      if (Date.now() - guildStats.lastSave > SAVE_INTERVAL) {
        await saveStatsToDatabase(guildId, guildStats, today);
        guildStats.lastSave = Date.now();
      }
      
    } catch (error) {
      console.error('📊 Statistics Message Event Error:', error);
    }
  }
};

// 💾 SAVE STATISTICS TO DATABASE
async function saveStatsToDatabase(guildId, stats, today) {
  try {
    // Get or create server stats document
    let serverStats = await ServerStats.findOne({ guildId });
    if (!serverStats) {
      serverStats = new ServerStats({ 
        guildId,
        totalMessages: 0,
        dailyStats: new Map(),
        userStats: new Map(),
        channelStats: new Map()
      });
    }
    
    // Update total messages
    serverStats.totalMessages += stats.totalMessages;
    
    // Update daily stats
    if (!serverStats.dailyStats.has(today)) {
      serverStats.dailyStats.set(today, {
        messages: 0,
        activeUsers: 0
      });
    }
    const dailyStats = serverStats.dailyStats.get(today);
    dailyStats.messages += stats.todayMessages;
    dailyStats.activeUsers = stats.users.size;
    
    // Update user stats
    for (const [userId, userStat] of stats.users) {
      if (!serverStats.userStats.has(userId)) {
        serverStats.userStats.set(userId, {
          totalMessages: 0,
          lastActive: userStat.lastActive
        });
      }
      const dbUserStats = serverStats.userStats.get(userId);
      dbUserStats.totalMessages += userStat.messages;
      dbUserStats.lastActive = userStat.lastActive;
    }
    
    // Update channel stats
    for (const [channelId, channelStat] of stats.channels) {
      if (!serverStats.channelStats.has(channelId)) {
        serverStats.channelStats.set(channelId, {
          totalMessages: 0,
          name: channelStat.name
        });
      }
      const dbChannelStats = serverStats.channelStats.get(channelId);
      dbChannelStats.totalMessages += channelStat.messages;
      dbChannelStats.name = channelStat.name;
    }
    
    // Save to database
    await serverStats.save();
    
    // Reset cache counters
    stats.totalMessages = 0;
    stats.todayMessages = 0;
    stats.users.clear();
    stats.channels.clear();
    
  } catch (error) {
    console.error('Database Save Error:', error);
  }
}

// 📤 EXPORT CACHE ACCESS
module.exports.getStatsCache = () => statsCache;

// 💾 Save all cached stats on process exit
process.on('SIGINT', async () => {
  console.log('📊 Saving statistics before exit...');
  const today = new Date().toISOString().split('T')[0];
  
  for (const [guildId, stats] of statsCache) {
    if (stats.totalMessages > 0) {
      await saveStatsToDatabase(guildId, stats, today);
    }
  }
  
  console.log('📊 Statistics saved successfully!');
});

// 🧹 Periodic cleanup and save
setInterval(async () => {
  const today = new Date().toISOString().split('T')[0];
  
  for (const [guildId, stats] of statsCache) {
    if (stats.totalMessages > 0) {
      await saveStatsToDatabase(guildId, stats, today);
    }
  }
}, SAVE_INTERVAL);
