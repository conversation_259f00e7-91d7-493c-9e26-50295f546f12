// 👀 SNIPE MESSAGE EVENT - MESSAGE TRACKING SYSTEM
// Professional message deletion tracking for moderation

const { Events } = require('discord.js');

// 📝 MESSAGE CACHE
const messageCache = new Map();
const editCache = new Map();

// 🧹 CACHE LIMITS
const MAX_CACHE_SIZE = 1000;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Skip if bot message or system message
      if (message.author.bot || message.system) return;
      
      // Skip if no content
      if (!message.content && message.attachments.size === 0) return;
      
      // 📝 Cache message for snipe functionality
      const messageData = {
        id: message.id,
        content: message.content,
        author: {
          id: message.author.id,
          tag: message.author.tag,
          displayName: message.author.displayName,
          avatar: message.author.displayAvatarURL()
        },
        channel: {
          id: message.channel.id,
          name: message.channel.name
        },
        guild: {
          id: message.guild.id,
          name: message.guild.name
        },
        attachments: message.attachments.map(att => ({
          name: att.name,
          url: att.url,
          size: att.size
        })),
        timestamp: message.createdTimestamp,
        cachedAt: Date.now()
      };
      
      // Store in cache
      const channelKey = `${message.guild.id}-${message.channel.id}`;
      if (!messageCache.has(channelKey)) {
        messageCache.set(channelKey, []);
      }
      
      const channelMessages = messageCache.get(channelKey);
      channelMessages.unshift(messageData);
      
      // Limit cache size per channel
      if (channelMessages.length > 50) {
        channelMessages.splice(50);
      }
      
      // 🧹 Cleanup old cache entries
      cleanupCache();
      
    } catch (error) {
      console.error('👀 Snipe Message Event Error:', error);
    }
  }
};

// 🧹 CACHE CLEANUP FUNCTION
function cleanupCache() {
  const now = Date.now();
  
  // Clean message cache
  for (const [channelKey, messages] of messageCache.entries()) {
    const validMessages = messages.filter(msg => 
      now - msg.cachedAt < CACHE_DURATION
    );
    
    if (validMessages.length === 0) {
      messageCache.delete(channelKey);
    } else {
      messageCache.set(channelKey, validMessages);
    }
  }
  
  // Clean edit cache
  for (const [channelKey, edits] of editCache.entries()) {
    const validEdits = edits.filter(edit => 
      now - edit.cachedAt < CACHE_DURATION
    );
    
    if (validEdits.length === 0) {
      editCache.delete(channelKey);
    } else {
      editCache.set(channelKey, validEdits);
    }
  }
  
  // Limit total cache size
  if (messageCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(messageCache.entries());
    const toDelete = entries.slice(MAX_CACHE_SIZE);
    toDelete.forEach(([key]) => messageCache.delete(key));
  }
}

// 📤 EXPORT CACHE ACCESS
module.exports.getMessageCache = () => messageCache;
module.exports.getEditCache = () => editCache;

// Run cleanup every hour
setInterval(cleanupCache, 60 * 60 * 1000);
