require('dotenv').config();

// <PERSON><PERSON> sic<PERSON>, dass alle nötigen Umgebungsvariablen gesetzt sind
const { TOKEN, CLIENT_ID, MONGO_URI } = process.env;
if (!TOKEN || !CLIENT_ID || !MONGO_URI) {
  console.error('❌ .env is missing TOKEN, CLIENT_ID or MONGO_URI');
  process.exit(1);
}

const { startSharding } = require('./shard');

startSharding();

const express = require('express');
const app = express();
const port = 2503;

app.get('/', (req, res) => res.send('Bot ist online 🟢'));

app.listen(port, () => {
  console.log(`Status-API läuft auf http://localhost:${port}`);
});