// Professional Giveaway Button Handlers
const { ActionRow<PERSON><PERSON>er, ButtonBuilder, ButtonStyle } = require('discord.js');
const config = require('./GiveawayConfig');
const GiveawayDatabase = require('./GiveawayDatabase');
const GiveawayEmbeds = require('./GiveawayEmbeds');
const GiveawayValidator = require('./GiveawayValidator');
const logger = require('../../utils/consoleLogger');

class GiveawayButtons {
  /**
   * Handle giveaway join button
   */
  static async handleJoin(interaction) {
    try {
      const giveaway = await Giveaway.findOne({
        messageId: interaction.message.id,
        ended: false
      });

      if (!giveaway) {
        return await interaction.reply({
          content: '❌ This giveaway has ended or could not be found!',
          ephemeral: true
        });
      }

      // Validate participation
      const validation = GiveawayValidator.validateParticipation(giveaway, interaction.user.id);
      if (!validation.valid) {
        return await interaction.reply({
          content: `❌ ${validation.error}`,
          ephemeral: true
        });
      }

      // If already participating, offer to leave
      if (validation.isParticipating) {
        const leaveButton = new ButtonBuilder()
          .setCustomId('giveaway-leave')
          .setLabel('Leave Giveaway')
          .setEmoji('🚪')
          .setStyle(ButtonStyle.Danger);

        const row = new ActionRowBuilder().addComponents(leaveButton);

        return await interaction.reply({
          content: '❓ You are already participating! Do you want to leave this giveaway?',
          components: [row],
          ephemeral: true
        });
      }

      // Add user to participants
      giveaway.participants.push(interaction.user.id);
      await giveaway.save();

      // Update the embed with new participant count
      await this.updateParticipantCount(interaction.message, giveaway.participants.length);

      await interaction.reply({
        content: '✅ You have successfully joined the giveaway! Good luck! 🍀',
        ephemeral: true
      });

      logger.info(`User ${interaction.user.tag} joined giveaway. Total participants: ${giveaway.participants.length}`);

    } catch (error) {
      logger.error(`Error handling giveaway join: ${error.message}`);
      await interaction.reply({
        content: '❌ Failed to join giveaway. Please try again.',
        ephemeral: true
      });
    }
  }

  /**
   * Handle giveaway leave button
   */
  static async handleLeave(interaction) {
    try {
      // Find the original giveaway message
      let giveawayMessageId = interaction.message.id;
      
      // If this is a reply, get the referenced message ID
      if (interaction.message.reference) {
        giveawayMessageId = interaction.message.reference.messageId;
      }

      const giveaway = await Giveaway.findOne({
        messageId: giveawayMessageId,
        ended: false
      });

      if (!giveaway) {
        return await interaction.reply({
          content: '❌ This giveaway has ended or could not be found!',
          ephemeral: true
        });
      }

      const participantIndex = giveaway.participants.indexOf(interaction.user.id);
      if (participantIndex === -1) {
        return await interaction.reply({
          content: '❌ You are not participating in this giveaway!',
          ephemeral: true
        });
      }

      // Remove user from participants
      giveaway.participants.splice(participantIndex, 1);
      await giveaway.save();

      // Update the original giveaway message
      const channel = interaction.channel;
      const originalMessage = await channel.messages.fetch(giveaway.messageId);
      await this.updateParticipantCount(originalMessage, giveaway.participants.length);

      await interaction.reply({
        content: '✅ You have left the giveaway!',
        ephemeral: true
      });

      logger.info(`User ${interaction.user.tag} left giveaway. Remaining participants: ${giveaway.participants.length}`);

    } catch (error) {
      logger.error(`Error handling giveaway leave: ${error.message}`);
      await interaction.reply({
        content: '❌ Failed to leave the giveaway. Please try again.',
        ephemeral: true
      });
    }
  }

  /**
   * Handle giveaway info button
   */
  static async handleInfo(interaction) {
    try {
      const giveaway = await Giveaway.findOne({
        messageId: interaction.message.id
      });

      if (!giveaway) {
        return await interaction.reply({
          content: '❌ Giveaway not found!',
          ephemeral: true
        });
      }

      const embed = GiveawayUtils.createInfoEmbed(giveaway, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: true
      });

    } catch (error) {
      logger.error(`Error showing giveaway info: ${error.message}`);
      await interaction.reply({
        content: '❌ Failed to get giveaway information.',
        ephemeral: true
      });
    }
  }

  /**
   * Handle ended giveaway button
   */
  static async handleEnded(interaction) {
    await interaction.reply({
      content: '❌ This giveaway has already ended!',
      ephemeral: true
    });
  }

  /**
   * Update participant count in giveaway embed
   */
  static async updateParticipantCount(message, newCount) {
    try {
      if (!message.embeds || message.embeds.length === 0) {
        logger.warn('No embeds found in giveaway message');
        return;
      }

      const embed = EmbedBuilder.from(message.embeds[0]);
      
      // Update the participants field
      if (embed.data.fields && embed.data.fields.length > 0) {
        const participantsField = embed.data.fields.find(field => 
          field.name.includes('Participants') || field.name.includes('👥')
        );
        
        if (participantsField) {
          participantsField.value = newCount.toString();
        }
      }

      await message.edit({ embeds: [embed] });

    } catch (error) {
      logger.error(`Error updating participant count: ${error.message}`);
    }
  }

  /**
   * Get all button handlers
   */
  static getHandlers() {
    return {
      'giveaway-join': this.handleJoin,
      'giveaway-leave': this.handleLeave,
      'giveaway-info': this.handleInfo,
      'giveaway-ended': this.handleEnded
    };
  }

  /**
   * Handle any giveaway button interaction
   */
  static async handleButtonInteraction(interaction) {
    const handlers = this.getHandlers();
    const handler = handlers[interaction.customId];

    if (handler) {
      await handler(interaction);
    } else {
      logger.warn(`Unknown giveaway button: ${interaction.customId}`);
      await interaction.reply({
        content: '❌ Unknown button interaction!',
        ephemeral: true
      });
    }
  }

  /**
   * Create confirmation buttons for dangerous actions
   */
  static createConfirmationButtons(action, giveawayId) {
    return new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`giveaway-confirm-${action}-${giveawayId}`)
          .setLabel('Confirm')
          .setEmoji('✅')
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId(`giveaway-cancel-${action}`)
          .setLabel('Cancel')
          .setEmoji('❌')
          .setStyle(ButtonStyle.Secondary)
      );
  }

  /**
   * Create management buttons for giveaway hosts
   */
  static createManagementButtons(giveawayId, isEnded = false) {
    const buttons = [];

    if (!isEnded) {
      buttons.push(
        new ButtonBuilder()
          .setCustomId(`giveaway-end-${giveawayId}`)
          .setLabel('End Early')
          .setEmoji('🏁')
          .setStyle(ButtonStyle.Danger)
      );
    } else {
      buttons.push(
        new ButtonBuilder()
          .setCustomId(`giveaway-reroll-${giveawayId}`)
          .setLabel('Reroll')
          .setEmoji('🎲')
          .setStyle(ButtonStyle.Primary)
      );
    }

    buttons.push(
      new ButtonBuilder()
        .setCustomId(`giveaway-stats-${giveawayId}`)
        .setLabel('Statistics')
        .setEmoji('📊')
        .setStyle(ButtonStyle.Secondary)
    );

    return new ActionRowBuilder().addComponents(buttons);
  }

  /**
   * Validate button interaction permissions
   */
  static validateButtonPermissions(interaction, giveaway, requiredRole = null) {
    // Host can always interact
    if (giveaway.hostId === interaction.user.id) {
      return { valid: true };
    }

    // Check for management permissions
    if (GiveawayUtils.hasGiveawayPermission(interaction.member)) {
      return { valid: true };
    }

    // Check for specific role if required
    if (requiredRole && interaction.member.roles.cache.has(requiredRole)) {
      return { valid: true };
    }

    return {
      valid: false,
      error: 'You don\'t have permission to perform this action!'
    };
  }

  /**
   * Log button interaction for analytics
   */
  static logButtonInteraction(interaction, action, giveawayId) {
    logger.info(`Giveaway button interaction: ${action} by ${interaction.user.tag} for giveaway ${giveawayId}`);
  }
}

module.exports = GiveawayButtons;
