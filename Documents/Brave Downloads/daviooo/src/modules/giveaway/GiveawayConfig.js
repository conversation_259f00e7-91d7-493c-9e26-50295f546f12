// Giveaway System Configuration
module.exports = {
  // Limits and restrictions
  LIMITS: {
    MIN_DURATION: 10000, // 10 seconds
    MAX_DURATION: 30 * 24 * 60 * 60 * 1000, // 30 days
    MIN_WINNERS: 1,
    MAX_WINNERS: 10,
    MAX_ACTIVE_GIVEAWAYS: 10,
    MIN_PARTICIPANTS_FOR_REROLL: 2,
    PRIZE_MAX_LENGTH: 256
  },

  // Cooldowns
  COOLDOWNS: {
    USER_GIVEAWAY_COOLDOWN: 5 * 60 * 1000, // 5 minutes
    BUTTON_INTERACTION_COOLDOWN: 1000, // 1 second
    REROLL_COOLDOWN: 30 * 1000 // 30 seconds
  },

  // Colors for embeds
  COLORS: {
    ACTIVE: 0xFF1493, // Deep Pink
    ENDED_SUCCESS: 0x00FF00, // Green
    ENDED_CANCELLED: 0x808080, // Gray
    ERROR: 0xFF0000, // Red
    INFO: 0x0099FF // Blue
  },

  // Emojis
  EMOJIS: {
    GIVEAWAY: '🎉',
    TROPHY: '🏆',
    PARTICIPANTS: '👥',
    HOST: '👑',
    TIME: '⏰',
    WINNERS: '🎊',
    JOIN: '🎁',
    LEAVE: '🚪',
    INFO: 'ℹ️',
    ENDED: '🏁',
    REROLL: '🎲',
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️'
  },

  // Messages
  MESSAGES: {
    GIVEAWAY_STARTED: 'Giveaway started successfully!',
    GIVEAWAY_ENDED: 'Giveaway ended!',
    GIVEAWAY_CANCELLED: 'Giveaway cancelled - not enough participants',
    JOINED_GIVEAWAY: 'You have successfully joined the giveaway! Good luck!',
    LEFT_GIVEAWAY: 'You have left the giveaway!',
    ALREADY_PARTICIPATING: 'You are already participating in this giveaway!',
    NOT_PARTICIPATING: 'You are not participating in this giveaway!',
    GIVEAWAY_NOT_FOUND: 'Giveaway not found or has already ended!',
    NO_PERMISSION: 'You need Manage Guild permission to use this command!',
    INVALID_DURATION: 'Please provide a valid duration (minimum 10 seconds)!',
    INVALID_WINNERS: 'Winner count must be between 1 and 10!',
    INVALID_PRIZE: 'Prize description is too long (max 256 characters)!',
    COOLDOWN_ACTIVE: 'You are on cooldown! Please wait before starting another giveaway.',
    MAX_GIVEAWAYS_REACHED: 'Maximum active giveaways reached. Please end some giveaways first.',
    CHANNEL_PERMISSIONS_MISSING: 'I don\'t have the required permissions in that channel!',
    HOST_CANNOT_PARTICIPATE: 'You cannot participate in your own giveaway!',
    GIVEAWAY_ALREADY_ENDED: 'This giveaway has already ended!',
    NOT_ENOUGH_PARTICIPANTS: 'Cannot reroll: Not enough participants (minimum 2 required)!',
    REROLL_SUCCESS: 'Giveaway rerolled successfully!',
    WINNERS_ANNOUNCED: 'Congratulations to the winners!'
  },

  // Required permissions for bot
  REQUIRED_PERMISSIONS: [
    'SendMessages',
    'EmbedLinks',
    'AddReactions',
    'ReadMessageHistory',
    'ManageMessages'
  ],

  // Database settings
  DATABASE: {
    CLEANUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
    MAX_ENDED_GIVEAWAYS_KEEP: 100 // Keep last 100 ended giveaways
  },

  // Button IDs
  BUTTON_IDS: {
    JOIN: 'giveaway-join',
    LEAVE: 'giveaway-leave',
    INFO: 'giveaway-info',
    ENDED: 'giveaway-ended',
    REROLL: 'giveaway-reroll',
    END_EARLY: 'giveaway-end-early',
    CONFIRM_END: 'giveaway-confirm-end',
    CANCEL_ACTION: 'giveaway-cancel-action'
  },

  // Validation patterns
  PATTERNS: {
    MESSAGE_ID: /^\d{17,19}$/,
    DURATION: /^(\d+)([smhd])$/i
  },

  // Default values
  DEFAULTS: {
    GIVEAWAY_DESCRIPTION: 'React with 🎁 to enter!',
    EMBED_FOOTER: 'Ends at',
    WINNER_ANNOUNCEMENT_DURATION: 24 * 60 * 60 * 1000 // 24 hours
  }
};
