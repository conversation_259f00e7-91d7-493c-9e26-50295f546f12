// Professional Giveaway Scheduler
const config = require('./GiveawayConfig');
const GiveawayDatabase = require('./GiveawayDatabase');
const GiveawayEmbeds = require('./GiveawayEmbeds');
const { <PERSON><PERSON>ow<PERSON><PERSON>er, ButtonBuilder, ButtonStyle } = require('discord.js');
const logger = require('../../utils/consoleLogger');

class GiveawayScheduler {
  constructor(client) {
    this.client = client;
    this.scheduledGiveaways = new Map();
    this.cleanupInterval = null;
  }

  /**
   * Initialize the scheduler
   */
  async initialize() {
    try {
      // Schedule existing giveaways
      await this.scheduleExistingGiveaways();
      
      // Start cleanup interval
      this.startCleanupInterval();
      
      // Check for expired giveaways on startup
      await this.checkExpiredGiveaways();
      
      logger.success('Giveaway scheduler initialized successfully');
    } catch (error) {
      logger.error(`Failed to initialize giveaway scheduler: ${error.message}`);
    }
  }

  /**
   * Schedule existing giveaways from database
   */
  async scheduleExistingGiveaways() {
    try {
      const result = await GiveawayDatabase.getExpiredGiveaways();
      if (!result.success) {
        logger.error(`Failed to fetch giveaways: ${result.error}`);
        return;
      }

      let scheduledCount = 0;
      let expiredCount = 0;

      for (const giveaway of result.giveaways) {
        const remaining = giveaway.endsAt - Date.now();
        
        if (remaining > 0) {
          this.scheduleGiveaway(giveaway);
          scheduledCount++;
        } else {
          // End expired giveaways immediately
          await this.endGiveaway(giveaway);
          expiredCount++;
        }
      }

      if (scheduledCount > 0) {
        logger.success(`Scheduled ${scheduledCount} giveaways`);
      }
      if (expiredCount > 0) {
        logger.info(`Processed ${expiredCount} expired giveaways`);
      }
    } catch (error) {
      logger.error(`Error scheduling existing giveaways: ${error.message}`);
    }
  }

  /**
   * Schedule a single giveaway
   */
  scheduleGiveaway(giveaway) {
    const remaining = giveaway.endsAt - Date.now();
    
    if (remaining <= 0) {
      // End immediately if already expired
      this.endGiveaway(giveaway);
      return;
    }

    // Clear existing timeout if any
    if (this.scheduledGiveaways.has(giveaway.messageId)) {
      clearTimeout(this.scheduledGiveaways.get(giveaway.messageId));
    }

    // Schedule the giveaway to end
    const timeout = setTimeout(() => {
      this.endGiveaway(giveaway);
      this.scheduledGiveaways.delete(giveaway.messageId);
    }, remaining);

    this.scheduledGiveaways.set(giveaway.messageId, timeout);
  }

  /**
   * End a giveaway
   */
  async endGiveaway(giveaway) {
    try {
      // Validate that channel and message still exist
      const validation = await this.validateGiveawayExists(giveaway);
      if (!validation.valid) {
        // Mark as ended in database if channel/message no longer exists
        await GiveawayDatabase.endGiveaway(giveaway.messageId, [], true);
        return;
      }

      const { channel, message } = validation.data;
      const guild = channel.guild;

      // Get fresh giveaway data from database
      const dbResult = await GiveawayDatabase.getGiveawayByMessageId(giveaway.messageId);
      if (!dbResult.success || !dbResult.giveaway) {
        logger.warn('Giveaway not found in database during end process');
        return;
      }

      const freshGiveaway = dbResult.giveaway;

      // Check if enough participants
      if (freshGiveaway.participants.length < config.LIMITS.MIN_PARTICIPANTS_FOR_REROLL) {
        // Cancel giveaway - not enough participants
        const cancelledEmbed = GiveawayEmbeds.createCancelledEmbed(freshGiveaway, guild);
        const disabledButtons = this.createDisabledButtons();

        await message.edit({ 
          embeds: [cancelledEmbed], 
          components: [disabledButtons] 
        });

        await message.reply({
          content: `${config.EMOJIS.WARNING} ${config.MESSAGES.GIVEAWAY_CANCELLED}`,
          allowedMentions: { parse: [] }
        });

        // Mark as cancelled in database
        await GiveawayDatabase.endGiveaway(freshGiveaway.messageId, [], true);
        
        logger.info(`Giveaway cancelled: ${freshGiveaway.prize} - Not enough participants`);
      } else {
        // Select winners
        const winners = this.selectWinners(freshGiveaway.participants, freshGiveaway.winnerCount);
        
        // Update database with winners
        await GiveawayDatabase.endGiveaway(freshGiveaway.messageId, winners, false);

        // Create ended embed
        const endedEmbed = GiveawayEmbeds.createEndedEmbed(freshGiveaway, winners, guild);
        const disabledButtons = this.createDisabledButtons();

        await message.edit({ 
          embeds: [endedEmbed], 
          components: [disabledButtons] 
        });

        // Send winner announcement
        if (winners.length > 0) {
          const { embed: winnerEmbed, giveawayURL } = GiveawayEmbeds.createWinnerAnnouncementEmbed(
            freshGiveaway, 
            winners, 
            guild
          );

          const winnerMessage = await message.reply({
            content: `${config.EMOJIS.TROPHY} **CONGRATULATIONS!** ${config.EMOJIS.TROPHY}\n\nWinner${winners.length > 1 ? 's' : ''} of **${freshGiveaway.prize}**:\n${winners.map(id => `<@${id}>`).join(', ')}\n\nThank you to all ${freshGiveaway.participants.length} participants!\n[View the giveaway](${giveawayURL})`,
            embeds: [winnerEmbed],
            allowedMentions: { users: winners }
          });

          // Try to pin the winner message
          try {
            await winnerMessage.pin();
          } catch (pinError) {
            logger.warn('Could not pin winner message: Missing permissions');
          }
        }

        logger.success(`Giveaway ended successfully: ${freshGiveaway.prize} - ${winners.length} winner(s)`);
      }
    } catch (error) {
      logger.error(`Error ending giveaway: ${error.message}`);
      
      // Mark as ended in database to prevent retry loops
      try {
        await GiveawayDatabase.endGiveaway(giveaway.messageId, [], true);
        logger.info('Marked problematic giveaway as ended in database');
      } catch (dbError) {
        logger.error(`Failed to mark giveaway as ended: ${dbError.message}`);
      }
    }
  }

  /**
   * Validate that giveaway channel and message exist
   */
  async validateGiveawayExists(giveaway) {
    try {
      // Check if channel exists
      let channel;
      try {
        channel = await this.client.channels.fetch(giveaway.channelId);
      } catch (error) {
        return {
          valid: false,
          error: 'Channel no longer exists',
          code: 'CHANNEL_NOT_FOUND'
        };
      }

      if (!channel) {
        return {
          valid: false,
          error: 'Channel not found',
          code: 'CHANNEL_NOT_FOUND'
        };
      }

      // Check if message exists
      let message;
      try {
        message = await channel.messages.fetch(giveaway.messageId);
      } catch (error) {
        return {
          valid: false,
          error: 'Giveaway message no longer exists',
          code: 'MESSAGE_NOT_FOUND'
        };
      }

      if (!message) {
        return {
          valid: false,
          error: 'Giveaway message not found',
          code: 'MESSAGE_NOT_FOUND'
        };
      }

      return {
        valid: true,
        data: { channel, message }
      };
    } catch (error) {
      return {
        valid: false,
        error: `Validation error: ${error.message}`,
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Select random winners from participants
   */
  selectWinners(participants, winnerCount) {
    if (participants.length === 0) return [];
    
    const shuffled = [...participants].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, Math.min(winnerCount, participants.length));
  }

  /**
   * Create disabled buttons for ended giveaways
   */
  createDisabledButtons() {
    return new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(config.BUTTON_IDS.ENDED)
        .setLabel('Ended')
        .setEmoji(config.EMOJIS.ENDED)
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(true),
      new ButtonBuilder()
        .setCustomId(config.BUTTON_IDS.INFO)
        .setLabel('Info')
        .setEmoji(config.EMOJIS.INFO)
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(true)
    );
  }

  /**
   * Check for expired giveaways
   */
  async checkExpiredGiveaways() {
    try {
      const result = await GiveawayDatabase.getExpiredGiveaways();
      if (result.success && result.giveaways.length > 0) {
        for (const giveaway of result.giveaways) {
          await this.endGiveaway(giveaway);
        }
      }
    } catch (error) {
      logger.error(`Error checking expired giveaways: ${error.message}`);
    }
  }

  /**
   * Start cleanup interval
   */
  startCleanupInterval() {
    // Run cleanup every 24 hours
    this.cleanupInterval = setInterval(async () => {
      try {
        const result = await GiveawayDatabase.cleanupOldGiveaways();
        if (result.success && result.deletedCount > 0) {
          logger.info(`Cleaned up ${result.deletedCount} old giveaways`);
        }
      } catch (error) {
        logger.error(`Error during giveaway cleanup: ${error.message}`);
      }
    }, config.DATABASE.CLEANUP_INTERVAL);
  }

  /**
   * Cancel scheduled giveaway
   */
  cancelScheduledGiveaway(messageId) {
    if (this.scheduledGiveaways.has(messageId)) {
      clearTimeout(this.scheduledGiveaways.get(messageId));
      this.scheduledGiveaways.delete(messageId);
      return true;
    }
    return false;
  }

  /**
   * Cleanup on shutdown
   */
  cleanup() {
    // Clear all timeouts
    for (const timeout of this.scheduledGiveaways.values()) {
      clearTimeout(timeout);
    }
    this.scheduledGiveaways.clear();

    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    logger.info('Giveaway scheduler cleaned up');
  }
}

module.exports = GiveawayScheduler;
