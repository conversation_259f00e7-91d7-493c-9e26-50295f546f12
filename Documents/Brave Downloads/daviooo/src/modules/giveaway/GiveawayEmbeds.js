// Professional Giveaway Embed Builder
const { EmbedBuilder } = require('discord.js');
const config = require('./GiveawayConfig');

class GiveawayEmbeds {
  /**
   * Create active giveaway embed
   */
  static createActiveEmbed(giveaway, guild) {
    const embed = new EmbedBuilder()
      .setColor(config.COLORS.ACTIVE)
      .setTitle(`${config.EMOJIS.GIVEAWAY} **GIVEAWAY** ${config.EMOJIS.GIVEAWAY}`)
      .setDescription(`**Prize:** ${giveaway.prize}\n\n${config.DEFAULTS.GIVEAWAY_DESCRIPTION}`)
      .addFields(
        { 
          name: `${config.EMOJIS.PARTICIPANTS} Participants`, 
          value: giveaway.participants.length.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.WINNERS} Winners`, 
          value: giveaway.winnerCount.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.HOST} Host`, 
          value: `<@${giveaway.hostId}>`, 
          inline: true 
        }
      )
      .setFooter({ 
        text: `${config.DEFAULTS.EMBED_FOOTER} • ${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp(giveaway.endsAt);

    return embed;
  }

  /**
   * Create ended giveaway embed (success)
   */
  static createEndedEmbed(giveaway, winners, guild) {
    const embed = new EmbedBuilder()
      .setColor(config.COLORS.ENDED_SUCCESS)
      .setTitle(`${config.EMOJIS.TROPHY} **GIVEAWAY ENDED** ${config.EMOJIS.TROPHY}`)
      .setDescription(`**Prize:** ${giveaway.prize}`)
      .addFields(
        { 
          name: `${config.EMOJIS.WINNERS} Winners`, 
          value: winners.length > 0 ? winners.map(id => `<@${id}>`).join('\n') : 'No winners', 
          inline: false 
        },
        { 
          name: `${config.EMOJIS.PARTICIPANTS} Total Participants`, 
          value: giveaway.participants.length.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.HOST} Host`, 
          value: `<@${giveaway.hostId}>`, 
          inline: true 
        }
      )
      .setFooter({ 
        text: `Ended at • ${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp();

    return embed;
  }

  /**
   * Create cancelled giveaway embed
   */
  static createCancelledEmbed(giveaway, guild) {
    const embed = new EmbedBuilder()
      .setColor(config.COLORS.ENDED_CANCELLED)
      .setTitle(`${config.EMOJIS.WARNING} **GIVEAWAY CANCELLED** ${config.EMOJIS.WARNING}`)
      .setDescription(`**Prize:** ${giveaway.prize}\n\n**Reason:** ${config.MESSAGES.GIVEAWAY_CANCELLED}`)
      .addFields(
        { 
          name: `${config.EMOJIS.PARTICIPANTS} Participants`, 
          value: giveaway.participants.length.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.HOST} Host`, 
          value: `<@${giveaway.hostId}>`, 
          inline: true 
        }
      )
      .setFooter({ 
        text: `Cancelled at • ${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp();

    return embed;
  }

  /**
   * Create giveaway info embed
   */
  static createInfoEmbed(giveaway, user, guild) {
    const isParticipating = giveaway.participants.includes(user.id);
    const timeLeft = giveaway.endsAt - Date.now();
    const timeLeftString = timeLeft > 0 ? `<t:${Math.floor(giveaway.endsAt.getTime() / 1000)}:R>` : 'Ended';

    const embed = new EmbedBuilder()
      .setColor(config.COLORS.INFO)
      .setTitle(`${config.EMOJIS.INFO} **GIVEAWAY INFORMATION**`)
      .setDescription(`**Prize:** ${giveaway.prize}`)
      .addFields(
        { 
          name: `${config.EMOJIS.TIME} Status`, 
          value: giveaway.ended ? 'Ended' : 'Active', 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.TIME} Time Left`, 
          value: timeLeftString, 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.PARTICIPANTS} Your Status`, 
          value: isParticipating ? `${config.EMOJIS.SUCCESS} Participating` : `${config.EMOJIS.ERROR} Not participating`, 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.PARTICIPANTS} Total Participants`, 
          value: giveaway.participants.length.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.WINNERS} Winners`, 
          value: giveaway.winnerCount.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.HOST} Host`, 
          value: `<@${giveaway.hostId}>`, 
          inline: true 
        }
      )
      .setFooter({ 
        text: `Giveaway ID: ${giveaway._id} • ${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp(giveaway.createdAt);

    return embed;
  }

  /**
   * Create winner announcement embed
   */
  static createWinnerAnnouncementEmbed(giveaway, winners, guild) {
    const giveawayURL = `https://discord.com/channels/${giveaway.guildId}/${giveaway.channelId}/${giveaway.messageId}`;
    
    const embed = new EmbedBuilder()
      .setColor(config.COLORS.ENDED_SUCCESS)
      .setTitle(`${config.EMOJIS.TROPHY} **GIVEAWAY WINNERS** ${config.EMOJIS.TROPHY}`)
      .setDescription(`${config.MESSAGES.WINNERS_ANNOUNCED}\n\n**Prize:** ${giveaway.prize}`)
      .addFields(
        { 
          name: `${config.EMOJIS.WINNERS} Winner${winners.length > 1 ? 's' : ''}`, 
          value: winners.map(id => `<@${id}>`).join('\n'), 
          inline: false 
        },
        { 
          name: `${config.EMOJIS.PARTICIPANTS} Total Participants`, 
          value: giveaway.participants.length.toString(), 
          inline: true 
        },
        { 
          name: `${config.EMOJIS.HOST} Host`, 
          value: `<@${giveaway.hostId}>`, 
          inline: true 
        }
      )
      .setFooter({ 
        text: `${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp();

    return { embed, giveawayURL };
  }

  /**
   * Create error embed
   */
  static createErrorEmbed(message, title = 'Error') {
    return new EmbedBuilder()
      .setColor(config.COLORS.ERROR)
      .setTitle(`${config.EMOJIS.ERROR} **${title.toUpperCase()}**`)
      .setDescription(message)
      .setTimestamp();
  }

  /**
   * Create success embed
   */
  static createSuccessEmbed(message, title = 'Success') {
    return new EmbedBuilder()
      .setColor(config.COLORS.ENDED_SUCCESS)
      .setTitle(`${config.EMOJIS.SUCCESS} **${title.toUpperCase()}**`)
      .setDescription(message)
      .setTimestamp();
  }

  /**
   * Create giveaway list embed
   */
  static createListEmbed(giveaways, guild, page = 1, totalPages = 1) {
    const embed = new EmbedBuilder()
      .setColor(config.COLORS.INFO)
      .setTitle(`${config.EMOJIS.GIVEAWAY} **ACTIVE GIVEAWAYS**`)
      .setDescription(giveaways.length === 0 ? 'No active giveaways in this server.' : `Found ${giveaways.length} active giveaway(s)`)
      .setFooter({ 
        text: `Page ${page}/${totalPages} • ${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp();

    giveaways.forEach((giveaway, index) => {
      const timeLeft = `<t:${Math.floor(giveaway.endsAt.getTime() / 1000)}:R>`;
      embed.addFields({
        name: `${index + 1}. ${giveaway.prize}`,
        value: `**Channel:** <#${giveaway.channelId}>\n**Winners:** ${giveaway.winnerCount}\n**Participants:** ${giveaway.participants.length}\n**Ends:** ${timeLeft}`,
        inline: false
      });
    });

    return embed;
  }

  /**
   * Create statistics embed
   */
  static createStatsEmbed(stats, guild) {
    const embed = new EmbedBuilder()
      .setColor(config.COLORS.INFO)
      .setTitle(`${config.EMOJIS.INFO} **GIVEAWAY STATISTICS**`)
      .setDescription(`Statistics for **${guild?.name || 'Unknown Server'}**`)
      .addFields(
        { name: `${config.EMOJIS.GIVEAWAY} Total Giveaways`, value: stats.total.toString(), inline: true },
        { name: `${config.EMOJIS.SUCCESS} Active`, value: stats.active.toString(), inline: true },
        { name: `${config.EMOJIS.TROPHY} Completed`, value: stats.completed.toString(), inline: true },
        { name: `${config.EMOJIS.WARNING} Cancelled`, value: stats.cancelled.toString(), inline: true },
        { name: `${config.EMOJIS.PARTICIPANTS} Total Participants`, value: stats.totalParticipants.toString(), inline: true },
        { name: `${config.EMOJIS.WINNERS} Total Winners`, value: stats.totalWinners.toString(), inline: true }
      )
      .setFooter({ 
        text: `${guild?.name || 'Unknown Server'}`,
        iconURL: guild?.iconURL() || undefined
      })
      .setTimestamp();

    return embed;
  }
}

module.exports = GiveawayEmbeds;
