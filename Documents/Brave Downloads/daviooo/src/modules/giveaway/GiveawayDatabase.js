// Professional Giveaway Database Operations
const Giveaway = require('../../database/models/Giveaway');
const config = require('./GiveawayConfig');
const logger = require('../../utils/consoleLogger');

class GiveawayDatabase {
  /**
   * Create a new giveaway
   */
  static async createGiveaway(giveawayData) {
    try {
      const giveaway = new Giveaway({
        guildId: giveawayData.guildId,
        channelId: giveawayData.channelId,
        messageId: giveawayData.messageId,
        hostId: giveawayData.hostId,
        prize: giveawayData.prize,
        winnerCount: giveawayData.winnerCount,
        endsAt: giveawayData.endsAt,
        participants: [],
        winnerIds: [],
        ended: false,
        cancelled: false,
        createdAt: new Date()
      });

      await giveaway.save();
      return { success: true, giveaway };
    } catch (error) {
      logger.error(`Error creating giveaway: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get giveaway by message ID
   */
  static async getGiveawayByMessageId(messageId) {
    try {
      const giveaway = await Giveaway.findOne({ messageId });
      return { success: true, giveaway };
    } catch (error) {
      logger.error(`Error fetching giveaway: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get active giveaways for a guild
   */
  static async getActiveGiveaways(guildId, limit = 10, skip = 0) {
    try {
      const giveaways = await Giveaway.find({
        guildId,
        ended: false
      })
      .sort({ endsAt: 1 })
      .limit(limit)
      .skip(skip);

      const total = await Giveaway.countDocuments({
        guildId,
        ended: false
      });

      return { success: true, giveaways, total };
    } catch (error) {
      logger.error(`Error fetching active giveaways: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all giveaways that should end
   */
  static async getExpiredGiveaways() {
    try {
      const giveaways = await Giveaway.find({
        ended: false,
        endsAt: { $lte: new Date() }
      });

      return { success: true, giveaways };
    } catch (error) {
      logger.error(`Error fetching expired giveaways: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add participant to giveaway
   */
  static async addParticipant(messageId, userId) {
    try {
      const result = await Giveaway.updateOne(
        { 
          messageId, 
          ended: false,
          participants: { $ne: userId }
        },
        { 
          $addToSet: { participants: userId }
        }
      );

      if (result.matchedCount === 0) {
        return { success: false, error: 'Giveaway not found or user already participating' };
      }

      return { success: true };
    } catch (error) {
      logger.error(`Error adding participant: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Remove participant from giveaway
   */
  static async removeParticipant(messageId, userId) {
    try {
      const result = await Giveaway.updateOne(
        { 
          messageId, 
          ended: false,
          participants: userId
        },
        { 
          $pull: { participants: userId }
        }
      );

      if (result.matchedCount === 0) {
        return { success: false, error: 'Giveaway not found or user not participating' };
      }

      return { success: true };
    } catch (error) {
      logger.error(`Error removing participant: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * End giveaway and set winners
   */
  static async endGiveaway(messageId, winnerIds, cancelled = false) {
    try {
      const result = await Giveaway.updateOne(
        { messageId, ended: false },
        { 
          $set: { 
            ended: true, 
            winnerIds: winnerIds || [],
            cancelled,
            endedAt: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        return { success: false, error: 'Giveaway not found or already ended' };
      }

      return { success: true };
    } catch (error) {
      logger.error(`Error ending giveaway: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get giveaway statistics for a guild
   */
  static async getGiveawayStats(guildId) {
    try {
      const [total, active, completed, cancelled] = await Promise.all([
        Giveaway.countDocuments({ guildId }),
        Giveaway.countDocuments({ guildId, ended: false }),
        Giveaway.countDocuments({ guildId, ended: true, cancelled: false }),
        Giveaway.countDocuments({ guildId, ended: true, cancelled: true })
      ]);

      // Get total participants and winners
      const participantStats = await Giveaway.aggregate([
        { $match: { guildId } },
        {
          $group: {
            _id: null,
            totalParticipants: { $sum: { $size: '$participants' } },
            totalWinners: { $sum: { $size: '$winnerIds' } }
          }
        }
      ]);

      const stats = {
        total,
        active,
        completed,
        cancelled,
        totalParticipants: participantStats[0]?.totalParticipants || 0,
        totalWinners: participantStats[0]?.totalWinners || 0
      };

      return { success: true, stats };
    } catch (error) {
      logger.error(`Error fetching giveaway stats: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Count active giveaways for a guild
   */
  static async countActiveGiveaways(guildId) {
    try {
      const count = await Giveaway.countDocuments({
        guildId,
        ended: false
      });

      return { success: true, count };
    } catch (error) {
      logger.error(`Error counting active giveaways: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's last giveaway time
   */
  static async getUserLastGiveawayTime(guildId, userId) {
    try {
      const lastGiveaway = await Giveaway.findOne({
        guildId,
        hostId: userId
      }).sort({ createdAt: -1 });

      return { 
        success: true, 
        lastTime: lastGiveaway?.createdAt?.getTime() || null 
      };
    } catch (error) {
      logger.error(`Error fetching user's last giveaway time: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cleanup old ended giveaways
   */
  static async cleanupOldGiveaways() {
    try {
      const cutoffDate = new Date(Date.now() - config.DATABASE.CLEANUP_INTERVAL);
      
      // Keep only the most recent ended giveaways
      const oldGiveaways = await Giveaway.find({
        ended: true,
        endedAt: { $lt: cutoffDate }
      })
      .sort({ endedAt: -1 })
      .skip(config.DATABASE.MAX_ENDED_GIVEAWAYS_KEEP);

      if (oldGiveaways.length > 0) {
        const idsToDelete = oldGiveaways.map(g => g._id);
        const result = await Giveaway.deleteMany({
          _id: { $in: idsToDelete }
        });

        logger.info(`Cleaned up ${result.deletedCount} old giveaways`);
        return { success: true, deletedCount: result.deletedCount };
      }

      return { success: true, deletedCount: 0 };
    } catch (error) {
      logger.error(`Error cleaning up old giveaways: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get giveaway by ID
   */
  static async getGiveawayById(giveawayId) {
    try {
      const giveaway = await Giveaway.findById(giveawayId);
      return { success: true, giveaway };
    } catch (error) {
      logger.error(`Error fetching giveaway by ID: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update giveaway participants count (for display)
   */
  static async updateParticipantCount(messageId) {
    try {
      const giveaway = await Giveaway.findOne({ messageId });
      if (!giveaway) {
        return { success: false, error: 'Giveaway not found' };
      }

      return { 
        success: true, 
        participantCount: giveaway.participants.length,
        giveaway 
      };
    } catch (error) {
      logger.error(`Error updating participant count: ${error.message}`);
      return { success: false, error: error.message };
    }
  }
}

module.exports = GiveawayDatabase;
