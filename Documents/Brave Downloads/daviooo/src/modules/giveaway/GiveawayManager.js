// Professional Giveaway Manager - Main Logic
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Giveaway = require('../../database/models/Giveaway');
const GiveawayUtils = require('./GiveawayUtils');
const GiveawayValidator = require('./GiveawayValidator');
const logger = require('../../utils/consoleLogger');

class GiveawayManager {
  constructor(client) {
    this.client = client;
    this.activeTimeouts = new Map();
  }

  /**
   * Schedule all ongoing giveaways on bot startup
   */
  async scheduleGiveaways() {
    try {
      const ongoingGiveaways = await Giveaway.find({ ended: false });
      let scheduledCount = 0;
      let cleanedCount = 0;

      for (const giveaway of ongoingGiveaways) {
        const remaining = giveaway.endsAt - Date.now();
        
        if (remaining > 0) {
          // Schedule the giveaway
          this.scheduleGiveaway(giveaway, remaining);
          scheduledCount++;
        } else {
          // End expired giveaways
          await this.endGiveaway(giveaway);
          cleanedCount++;
        }
      }

      if (scheduledCount > 0) {
        logger.success(`Scheduled ${scheduledCount} giveaways`);
      }
      if (cleanedCount > 0) {
        logger.info(`Cleaned up ${cleanedCount} expired giveaways`);
      }
    } catch (error) {
      logger.error(`Error scheduling giveaways: ${error.message}`);
    }
  }

  /**
   * Schedule a single giveaway
   */
  scheduleGiveaway(giveaway, delay) {
    const timeoutId = setTimeout(() => {
      this.endGiveaway(giveaway);
      this.activeTimeouts.delete(giveaway._id.toString());
    }, delay);

    this.activeTimeouts.set(giveaway._id.toString(), timeoutId);
  }

  /**
   * Start a new giveaway
   */
  async startGiveaway(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });

      // Validate input
      const validation = GiveawayValidator.validateStartInput(interaction);
      if (!validation.valid) {
        return await interaction.editReply({
          content: `❌ ${validation.error}`,
          ephemeral: true
        });
      }

      const { duration, winnerCount, prize, channel } = validation.data;
      const endAt = Date.now() + duration;

      // Create giveaway embed
      const embed = GiveawayUtils.createGiveawayEmbed({
        prize,
        winnerCount,
        endAt,
        hostId: interaction.user.id,
        hostUsername: interaction.user.username,
        hostAvatar: interaction.user.displayAvatarURL(),
        participantCount: 0
      });

      // Create buttons
      const buttons = GiveawayUtils.createGiveawayButtons();

      // Send giveaway message
      const message = await channel.send({ embeds: [embed], components: [buttons] });

      // Save to database
      const giveaway = new Giveaway({
        messageId: message.id,
        channelId: channel.id,
        guildId: interaction.guildId,
        hostId: interaction.user.id,
        prize,
        winnerCount,
        duration,
        endsAt: new Date(endAt),
        participants: [],
        winnerIds: [],
        ended: false
      });

      await giveaway.save();

      // Schedule the giveaway end
      this.scheduleGiveaway(giveaway, duration);

      await interaction.editReply({
        content: `✅ Giveaway started successfully in ${channel}!`,
        ephemeral: true
      });

      logger.info(`Giveaway started by ${interaction.user.tag} in ${channel.name}`);

    } catch (error) {
      logger.error(`Error starting giveaway: ${error.message}`);
      await interaction.editReply({
        content: '❌ Failed to start giveaway. Please try again.',
        ephemeral: true
      });
    }
  }

  /**
   * End a giveaway manually
   */
  async endGiveawayManually(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });

      const messageId = interaction.options.getString('message_id');
      const giveaway = await Giveaway.findOne({
        messageId: messageId,
        ended: false,
        guildId: interaction.guildId
      });

      if (!giveaway) {
        return await interaction.editReply({
          content: '❌ Giveaway not found or already ended!',
          ephemeral: true
        });
      }

      // Cancel scheduled timeout
      const timeoutId = this.activeTimeouts.get(giveaway._id.toString());
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.activeTimeouts.delete(giveaway._id.toString());
      }

      await this.endGiveaway(giveaway);
      await interaction.editReply({
        content: '✅ Giveaway ended successfully!',
        ephemeral: true
      });

    } catch (error) {
      logger.error(`Error ending giveaway manually: ${error.message}`);
      await interaction.editReply({
        content: '❌ Failed to end giveaway. Please try again.',
        ephemeral: true
      });
    }
  }

  /**
   * End a giveaway (main logic)
   */
  async endGiveaway(giveaway) {
    if (giveaway.ended) return;

    try {
      // Validate channel and message exist
      const validation = await GiveawayValidator.validateGiveawayExists(this.client, giveaway);
      if (!validation.valid) {
        // Silently mark as ended - channel/message no longer exists
        await Giveaway.findByIdAndUpdate(giveaway._id, { ended: true });
        return;
      }

      const { channel, message } = validation.data;

      // Update database
      const updatedGiveaway = await Giveaway.findByIdAndUpdate(
        giveaway._id,
        { ended: true },
        { new: true }
      );

      if (!updatedGiveaway) {
        logger.warn('Giveaway not found in database during end process');
        return;
      }

      // Check if enough participants
      if (updatedGiveaway.participants.length < 2) {
        await this.handleInsufficientParticipants(updatedGiveaway, message);
        return;
      }

      // Select winners and update database
      const winners = GiveawayUtils.selectWinners(
        updatedGiveaway.participants,
        updatedGiveaway.winnerCount
      );

      await Giveaway.findByIdAndUpdate(updatedGiveaway._id, { winnerIds: winners });

      // Update message with results
      await this.handleSuccessfulGiveaway(updatedGiveaway, message, winners);

      logger.success(`Giveaway ended successfully: ${updatedGiveaway.prize}`);

    } catch (error) {
      logger.error(`Error ending giveaway: ${error.message}`);
      // Mark as ended in database to prevent retry loops
      try {
        await Giveaway.findByIdAndUpdate(giveaway._id, { ended: true });
      } catch (dbError) {
        logger.error(`Failed to mark giveaway as ended: ${dbError.message}`);
      }
    }
  }

  /**
   * Handle giveaway with insufficient participants
   */
  async handleInsufficientParticipants(giveaway, message) {
    const embed = GiveawayUtils.createEndedEmbed({
      prize: giveaway.prize,
      hostId: giveaway.hostId,
      participantCount: giveaway.participants.length,
      status: 'cancelled',
      reason: 'Not enough participants (minimum 2 required)'
    });

    const buttons = GiveawayUtils.createDisabledButtons('❌', 'Cancelled');

    await message.edit({ embeds: [embed], components: [buttons] });
    await message.reply('❌ Giveaway cancelled: Not enough participants (minimum 2 required).');
  }

  /**
   * Handle successful giveaway completion
   */
  async handleSuccessfulGiveaway(giveaway, message, winners) {
    const embed = GiveawayUtils.createEndedEmbed({
      prize: giveaway.prize,
      hostId: giveaway.hostId,
      participantCount: giveaway.participants.length,
      winners: winners,
      status: 'completed'
    });

    const buttons = GiveawayUtils.createDisabledButtons('🎉', 'Ended');

    await message.edit({ embeds: [embed], components: [buttons] });

    // Send winner announcement
    if (winners.length > 0) {
      const winnerMessage = await message.reply({
        content: GiveawayUtils.createWinnerAnnouncement(giveaway, winners),
        allowedMentions: { users: winners }
      });

      // Try to pin the winner message
      try {
        await winnerMessage.pin();
      } catch (error) {
        logger.warn('Could not pin winner message: Missing permissions');
      }
    }
  }

  /**
   * Reroll a giveaway
   */
  async rerollGiveaway(interaction) {
    try {
      await interaction.deferReply({ ephemeral: true });

      const messageId = interaction.options.getString('message_id');
      const giveaway = await Giveaway.findOne({
        messageId: messageId,
        ended: true,
        guildId: interaction.guildId
      });

      if (!giveaway) {
        return await interaction.editReply({
          content: '❌ Giveaway not found or not ended yet!',
          ephemeral: true
        });
      }

      if (giveaway.participants.length < 2) {
        return await interaction.editReply({
          content: '❌ Cannot reroll: Not enough participants (minimum 2 required)!',
          ephemeral: true
        });
      }

      // Select new winners
      const newWinners = GiveawayUtils.selectWinners(
        giveaway.participants,
        giveaway.winnerCount
      );

      // Update database
      await Giveaway.findByIdAndUpdate(giveaway._id, { winnerIds: newWinners });

      // Send reroll announcement
      const channel = await this.client.channels.fetch(giveaway.channelId);
      const message = await channel.messages.fetch(giveaway.messageId);

      await message.reply({
        content: GiveawayUtils.createRerollAnnouncement(giveaway, newWinners),
        allowedMentions: { users: newWinners }
      });

      await interaction.editReply({
        content: '✅ Giveaway rerolled successfully!',
        ephemeral: true
      });

      logger.info(`Giveaway rerolled by ${interaction.user.tag}`);

    } catch (error) {
      logger.error(`Error rerolling giveaway: ${error.message}`);
      await interaction.editReply({
        content: '❌ Failed to reroll giveaway. Please try again.',
        ephemeral: true
      });
    }
  }
}

module.exports = GiveawayManager;
