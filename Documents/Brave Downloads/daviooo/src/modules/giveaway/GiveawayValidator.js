// Professional Giveaway Validation Functions
const ms = require('ms');
const config = require('./GiveawayConfig');
const GiveawayDatabase = require('./GiveawayDatabase');

class GiveawayValidator {
  /**
   * Validate giveaway start input
   */
  static validateStartInput(interaction) {
    try {
      const durationString = interaction.options.getString('duration');
      const winnerCount = interaction.options.getInteger('winners');
      const prize = interaction.options.getString('prize');
      const channel = interaction.options.getChannel('channel') || interaction.channel;

      // Validate duration
      const duration = ms(durationString);
      if (!duration || duration < config.LIMITS.MIN_DURATION) {
        return {
          valid: false,
          error: config.MESSAGES.INVALID_DURATION
        };
      }

      if (duration > config.LIMITS.MAX_DURATION) {
        return {
          valid: false,
          error: `Duration cannot exceed ${config.LIMITS.MAX_DURATION / (24 * 60 * 60 * 1000)} days!`
        };
      }

      // Validate winner count
      if (winnerCount < config.LIMITS.MIN_WINNERS || winnerCount > config.LIMITS.MAX_WINNERS) {
        return {
          valid: false,
          error: config.MESSAGES.INVALID_WINNERS
        };
      }

      // Validate prize
      if (!prize || prize.trim().length === 0) {
        return {
          valid: false,
          error: 'Prize description cannot be empty!'
        };
      }

      if (prize.length > config.LIMITS.PRIZE_MAX_LENGTH) {
        return {
          valid: false,
          error: config.MESSAGES.INVALID_PRIZE
        };
      }

      // Validate channel permissions
      const channelValidation = this.validateChannelPermissions(channel, interaction.guild.members.me);
      if (!channelValidation.valid) {
        return {
          valid: false,
          error: channelValidation.error
        };
      }

      // Validate user permissions
      if (!GiveawayUtils.hasGiveawayPermission(interaction.member)) {
        return {
          valid: false,
          error: 'You need Manage Guild permission to start giveaways!'
        };
      }

      return {
        valid: true,
        data: { duration, winnerCount, prize, channel }
      };

    } catch (error) {
      return {
        valid: false,
        error: 'Invalid input provided. Please check your parameters.'
      };
    }
  }

  /**
   * Validate channel permissions for giveaways
   */
  static validateChannelPermissions(channel, botMember) {
    if (!channel.isTextBased()) {
      return {
        valid: false,
        error: 'Giveaways can only be started in text channels!'
      };
    }

    const permissions = channel.permissionsFor(botMember);
    
    if (!permissions.has('SendMessages')) {
      return {
        valid: false,
        error: 'I don\'t have permission to send messages in that channel!'
      };
    }

    if (!permissions.has('EmbedLinks')) {
      return {
        valid: false,
        error: 'I don\'t have permission to embed links in that channel!'
      };
    }

    if (!permissions.has('AddReactions')) {
      return {
        valid: false,
        error: 'I don\'t have permission to add reactions in that channel!'
      };
    }

    if (!permissions.has('ReadMessageHistory')) {
      return {
        valid: false,
        error: 'I don\'t have permission to read message history in that channel!'
      };
    }

    return { valid: true };
  }

  /**
   * Validate that a giveaway exists and is accessible
   */
  static async validateGiveawayExists(client, giveaway) {
    try {
      // Check if channel exists
      let channel;
      try {
        channel = await client.channels.fetch(giveaway.channelId);
      } catch (error) {
        return {
          valid: false,
          error: 'Channel no longer exists',
          code: 'CHANNEL_NOT_FOUND'
        };
      }

      if (!channel) {
        return {
          valid: false,
          error: 'Channel not found',
          code: 'CHANNEL_NOT_FOUND'
        };
      }

      // Check if message exists
      let message;
      try {
        message = await channel.messages.fetch(giveaway.messageId);
      } catch (error) {
        return {
          valid: false,
          error: 'Giveaway message no longer exists',
          code: 'MESSAGE_NOT_FOUND'
        };
      }

      if (!message) {
        return {
          valid: false,
          error: 'Giveaway message not found',
          code: 'MESSAGE_NOT_FOUND'
        };
      }

      return {
        valid: true,
        data: { channel, message }
      };

    } catch (error) {
      return {
        valid: false,
        error: `Validation error: ${error.message}`,
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Validate message ID format
   */
  static validateMessageId(messageId) {
    if (!messageId) {
      return {
        valid: false,
        error: 'Message ID is required!'
      };
    }

    // Discord message IDs are 17-19 digit snowflakes
    if (!/^\d{17,19}$/.test(messageId)) {
      return {
        valid: false,
        error: 'Invalid message ID format!'
      };
    }

    return { valid: true };
  }

  /**
   * Validate giveaway participation
   */
  static validateParticipation(giveaway, userId) {
    if (giveaway.ended) {
      return {
        valid: false,
        error: 'This giveaway has already ended!'
      };
    }

    if (giveaway.hostId === userId) {
      return {
        valid: false,
        error: 'You cannot participate in your own giveaway!'
      };
    }

    const isParticipating = giveaway.participants.includes(userId);

    return {
      valid: true,
      isParticipating
    };
  }

  /**
   * Validate reroll requirements
   */
  static validateReroll(giveaway) {
    if (!giveaway.ended) {
      return {
        valid: false,
        error: 'Giveaway must be ended before rerolling!'
      };
    }

    if (giveaway.participants.length < 2) {
      return {
        valid: false,
        error: 'Cannot reroll: Not enough participants (minimum 2 required)!'
      };
    }

    return { valid: true };
  }

  /**
   * Validate guild context
   */
  static validateGuildContext(interaction) {
    if (!interaction.guild) {
      return {
        valid: false,
        error: 'Giveaways can only be used in servers!'
      };
    }

    if (!interaction.member) {
      return {
        valid: false,
        error: 'Member information not available!'
      };
    }

    return { valid: true };
  }

  /**
   * Validate bot permissions in guild
   */
  static validateBotPermissions(guild) {
    const botMember = guild.members.me;
    
    if (!botMember) {
      return {
        valid: false,
        error: 'Bot member information not available!'
      };
    }

    const requiredPermissions = [
      'SendMessages',
      'EmbedLinks',
      'AddReactions',
      'ReadMessageHistory',
      'ManageMessages'
    ];

    const missingPermissions = requiredPermissions.filter(
      perm => !botMember.permissions.has(perm)
    );

    if (missingPermissions.length > 0) {
      return {
        valid: false,
        error: `Missing permissions: ${missingPermissions.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * Validate giveaway limits per guild
   */
  static async validateGiveawayLimits(guildId) {
    const Giveaway = require('../../database/models/Giveaway');
    
    const activeGiveaways = await Giveaway.countDocuments({
      guildId,
      ended: false
    });

    const MAX_ACTIVE_GIVEAWAYS = 10; // Configurable limit

    if (activeGiveaways >= MAX_ACTIVE_GIVEAWAYS) {
      return {
        valid: false,
        error: `Maximum active giveaways reached (${MAX_ACTIVE_GIVEAWAYS}). Please end some giveaways first.`
      };
    }

    return { valid: true };
  }

  /**
   * Validate user cooldown
   */
  static validateUserCooldown(userId, lastGiveawayTime) {
    const COOLDOWN_MS = 5 * 60 * 1000; // 5 minutes
    const now = Date.now();

    if (lastGiveawayTime && (now - lastGiveawayTime) < COOLDOWN_MS) {
      const remainingTime = COOLDOWN_MS - (now - lastGiveawayTime);
      const remainingMinutes = Math.ceil(remainingTime / 60000);

      return {
        valid: false,
        error: `You're on cooldown! Please wait ${remainingMinutes} more minute(s) before starting another giveaway.`
      };
    }

    return { valid: true };
  }
}

module.exports = GiveawayValidator;
