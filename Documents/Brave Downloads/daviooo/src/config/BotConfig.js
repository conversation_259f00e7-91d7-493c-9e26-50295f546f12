// Professional Bot Configuration
const { GatewayIntentBits, Partials } = require('discord.js');

class BotConfig {
  static getClientOptions() {
    return {
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessageReactions
      ],
      partials: [
        Partials.Message, 
        Partials.Channel, 
        Partials.Reaction, 
        Partials.User
      ],
      presence: {
        status: 'online',
        activities: [{
          name: 'Professional Discord Bot',
          type: 0 // Playing
        }]
      }
    };
  }

  static validateEnvironment() {
    const required = ['TOKEN', 'CLIENT_ID'];
    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
    
    return {
      token: process.env.TOKEN,
      clientId: process.env.CLIENT_ID,
      nodeEnv: process.env.NODE_ENV || 'production'
    };
  }

  static getImportantCommands() {
    return ['ban', 'kick', 'timeout', 'warn', 'clear', 'mute', 'lock', 'unlock'];
  }
}

module.exports = BotConfig;