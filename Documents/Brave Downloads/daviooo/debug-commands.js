// 🔍 COMMAND DEBUG SCRIPT - DETAILED ERROR ANALYSIS
require('dotenv').config();
const fs = require('fs');
const path = require('path');

console.log('🔍 Analyzing command structure for validation errors...\n');

const commandsPath = path.join(__dirname, 'src/commands');
const categories = fs.readdirSync(commandsPath);

for (const category of categories) {
  const categoryPath = path.join(commandsPath, category);
  if (!fs.lstatSync(categoryPath).isDirectory()) continue;
  
  const indexPath = path.join(categoryPath, 'index.js');
  if (fs.existsSync(indexPath)) {
    try {
      console.log(`📂 Analyzing ${category}...`);
      const command = require(indexPath);
      
      if (command.data && command.data.toJSON) {
        const commandData = command.data.toJSON();
        console.log(`✅ Command: ${commandData.name}`);
        
        // Check for option ordering issues
        if (commandData.options) {
          commandData.options.forEach((option, index) => {
            if (option.type === 2) { // Subcommand group
              console.log(`  📁 Group: ${option.name}`);
              if (option.options) {
                option.options.forEach((subcommand, subIndex) => {
                  if (subcommand.type === 1) { // Subcommand
                    console.log(`    📝 Subcommand: ${subcommand.name}`);
                    if (subcommand.options) {
                      let foundNonRequired = false;
                      subcommand.options.forEach((opt, optIndex) => {
                        if (!opt.required && !foundNonRequired) {
                          foundNonRequired = true;
                        } else if (opt.required && foundNonRequired) {
                          console.log(`    ❌ ERROR: Required option "${opt.name}" after non-required option in ${subcommand.name}`);
                        }
                        console.log(`      ${opt.required ? '✓' : '○'} ${opt.name} (${opt.required ? 'required' : 'optional'})`);
                      });
                    }
                  }
                });
              }
            } else if (option.type === 1) { // Direct subcommand
              console.log(`  📝 Subcommand: ${option.name}`);
              if (option.options) {
                let foundNonRequired = false;
                option.options.forEach((opt, optIndex) => {
                  if (!opt.required && !foundNonRequired) {
                    foundNonRequired = true;
                  } else if (opt.required && foundNonRequired) {
                    console.log(`  ❌ ERROR: Required option "${opt.name}" after non-required option in ${option.name}`);
                  }
                  console.log(`    ${opt.required ? '✓' : '○'} ${opt.name} (${opt.required ? 'required' : 'optional'})`);
                });
              }
            }
          });
        }
        console.log('');
      }
    } catch (error) {
      console.log(`❌ Error loading ${category}: ${error.message}\n`);
    }
  }
}
